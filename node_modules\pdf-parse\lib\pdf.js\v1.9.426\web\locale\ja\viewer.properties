# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=前のページへ戻ります
previous_label=前へ
next.title=次のページへ進みます
next_label=次へ

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=ページ
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=/ {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} / {{pagesCount}})

zoom_out.title=表示を縮小します
zoom_out_label=縮小
zoom_in.title=表示を拡大します
zoom_in_label=拡大
zoom.title=拡大/縮小
presentation_mode.title=プレゼンテーションモードに切り替えます
presentation_mode_label=プレゼンテーションモード
open_file.title=ファイルを開きます
open_file_label=開く
print.title=印刷します
print_label=印刷
download.title=ダウンロードします
download_label=ダウンロード
bookmark.title=現在のビューの URL です (コピーまたは新しいウィンドウに開く)
bookmark_label=現在のビュー

# Secondary toolbar and context menu
tools.title=ツール
tools_label=ツール
first_page.title=最初のページへ移動します
first_page.label=最初のページへ移動
first_page_label=最初のページへ移動
last_page.title=最後のページへ移動します
last_page.label=最後のページへ移動
last_page_label=最後のページへ移動
page_rotate_cw.title=ページを右へ回転します
page_rotate_cw.label=右回転
page_rotate_cw_label=右回転
page_rotate_ccw.title=ページを左へ回転します
page_rotate_ccw.label=左回転
page_rotate_ccw_label=左回転

cursor_text_select_tool.title=テキスト選択ツールを有効にする
cursor_text_select_tool_label=テキスト選択ツール
cursor_hand_tool.title=手のひらツールを有効にする
cursor_hand_tool_label=手のひらツール

# Document properties dialog box
document_properties.title=文書のプロパティ...
document_properties_label=文書のプロパティ...
document_properties_file_name=ファイル名:
document_properties_file_size=ファイルサイズ:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} bytes)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bytes)
document_properties_title=タイトル:
document_properties_author=作成者:
document_properties_subject=件名:
document_properties_keywords=キーワード:
document_properties_creation_date=作成日:
document_properties_modification_date=更新日:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=アプリケーション:
document_properties_producer=PDF 作成:
document_properties_version=PDF のバージョン:
document_properties_page_count=ページ数:
document_properties_close=閉じる

print_progress_message=文書の印刷を準備しています...
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=キャンセル

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=サイドバー表示を切り替えます
toggle_sidebar_notification.title=サイドバー表示を切り替えます (文書に含まれるアウトライン / 添付)
toggle_sidebar_label=サイドバーの切り替え
document_outline.title=文書の目次を表示します (ダブルクリックで項目を開閉します)
document_outline_label=文書の目次
attachments.title=添付ファイルを表示します
attachments_label=添付ファイル
thumbs.title=縮小版を表示します
thumbs_label=縮小版
findbar.title=文書内を検索します
findbar_label=検索

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title={{page}} ページ
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=ページの縮小版 {{page}}

# Find panel button title and messages
find_input.title=検索
find_input.placeholder=文書内を検索...
find_previous.title=現在より前の位置で指定文字列が現れる部分を検索します
find_previous_label=前へ
find_next.title=現在より後の位置で指定文字列が現れる部分を検索します
find_next_label=次へ
find_highlight=すべて強調表示
find_match_case_label=大文字/小文字を区別
find_reached_top=文書先頭に到達したので末尾から続けて検索します
find_reached_bottom=文書末尾に到達したので先頭から続けて検索します
find_not_found=見つかりませんでした

# Error panel labels
error_more_info=詳細情報
error_less_info=詳細情報を隠す
error_close=閉じる
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (ビルド: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=メッセージ: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=スタック: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=ファイル: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=行: {{line}}
rendering_error=ページのレンダリング中にエラーが発生しました。

# Predefined zoom values
page_scale_width=幅に合わせる
page_scale_fit=ページのサイズに合わせる
page_scale_auto=自動ズーム
page_scale_actual=実際のサイズ
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=エラー
loading_error=PDF の読み込み中にエラーが発生しました。
invalid_file_error=無効または破損した PDF ファイル。
missing_file_error=PDF ファイルが見つかりません。
unexpected_response_error=サーバーから予期せぬ応答がありました。

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} 注釈]
password_label=この PDF ファイルを開くためのパスワードを入力してください。
password_invalid=無効なパスワードです。もう一度やり直してください。
password_ok=OK
password_cancel=キャンセル

printing_not_supported=警告: このブラウザーでは印刷が完全にサポートされていません。
printing_not_ready=警告: PDF を印刷するための読み込みが終了していません。
web_fonts_disabled=ウェブフォントが無効になっています: 埋め込まれた PDF のフォントを使用できません。
document_colors_not_allowed=PDF 文書は、ウェブページが指定した配色を使用することができません: 'ウェブページが指定した配色' はブラウザーで無効になっています。
