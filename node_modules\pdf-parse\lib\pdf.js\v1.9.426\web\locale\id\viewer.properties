# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Laman Sebel<PERSON>
previous_label=Sebelumnya
next.title=Laman <PERSON>
next_label=Selanjutnya

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Halaman
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=dari {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} dari {{pagesCount}})

zoom_out.title=Perkecil
zoom_out_label=Perkecil
zoom_in.title=Perbesar
zoom_in_label=Perbesar
zoom.title=Perbesaran
presentation_mode.title=Ganti ke Mode Presentasi
presentation_mode_label=Mode Presentasi
open_file.title=Buka Berkas
open_file_label=Buka
print.title=Cetak
print_label=Cetak
download.title=Unduh
download_label=Unduh
bookmark.title=Tampilan Sekarang (salin atau buka di jendela baru)
bookmark_label=Tampilan Sekarang

# Secondary toolbar and context menu
tools.title=Alat
tools_label=Alat
first_page.title=Buka Halaman Pertama
first_page.label=Ke Halaman Pertama
first_page_label=Buka Halaman Pertama
last_page.title=Buka Halaman Terakhir
last_page.label=Ke Halaman Terakhir
last_page_label=Buka Halaman Terakhir
page_rotate_cw.title=Putar Searah Jarum Jam
page_rotate_cw.label=Putar Searah Jarum Jam
page_rotate_cw_label=Putar Searah Jarum Jam
page_rotate_ccw.title=Putar Berlawanan Arah Jarum Jam
page_rotate_ccw.label=Putar Berlawanan Arah Jarum Jam
page_rotate_ccw_label=Putar Berlawanan Arah Jarum Jam

cursor_text_select_tool.title=Aktifkan Alat Seleksi Teks
cursor_text_select_tool_label=Alat Seleksi Teks
cursor_hand_tool.title=Aktifkan Alat Tangan
cursor_hand_tool_label=Alat Tangan

# Document properties dialog box
document_properties.title=Properti Dokumen…
document_properties_label=Properti Dokumen…
document_properties_file_name=Nama berkas:
document_properties_file_size=Ukuran berkas:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} byte)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} byte)
document_properties_title=Judul:
document_properties_author=Penyusun:
document_properties_subject=Subjek:
document_properties_keywords=Kata Kunci:
document_properties_creation_date=Tanggal Dibuat:
document_properties_modification_date=Tanggal Dimodifikasi:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=Pembuat:
document_properties_producer=Pemroduksi PDF:
document_properties_version=Versi PDF:
document_properties_page_count=Jumlah Halaman:
document_properties_close=Tutup

print_progress_message=Menyiapkan dokumen untuk pencetakan…
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=Batalkan

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Aktif/Nonaktifkan Bilah Samping
toggle_sidebar_notification.title=Aktif/Nonaktifkan Bilah Samping (dokumen berisi kerangka/lampiran)
toggle_sidebar_label=Aktif/Nonaktifkan Bilah Samping
document_outline.title=Tampilkan Kerangka Dokumen (klik ganda untuk membentangkan/menciutkan semua item)
document_outline_label=Kerangka Dokumen
attachments.title=Tampilkan Lampiran
attachments_label=Lampiran
thumbs.title=Tampilkan Miniatur
thumbs_label=Miniatur
findbar.title=Temukan di Dokumen
findbar_label=Temukan

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Laman {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=Miniatur Laman {{page}}

# Find panel button title and messages
find_input.title=Temukan
find_input.placeholder=Temukan di dokumen…
find_previous.title=Temukan kata sebelumnya
find_previous_label=Sebelumnya
find_next.title=Temukan lebih lanjut
find_next_label=Selanjutnya
find_highlight=Sorot semuanya
find_match_case_label=Cocokkan BESAR/kecil
find_reached_top=Sampai di awal dokumen, dilanjutkan dari bawah
find_reached_bottom=Sampai di akhir dokumen, dilanjutkan dari atas
find_not_found=Frasa tidak ditemukan

# Error panel labels
error_more_info=Lebih Banyak Informasi
error_less_info=Lebih Sedikit Informasi
error_close=Tutup
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Pesan: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Stack: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Berkas: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Baris: {{line}}
rendering_error=Galat terjadi saat merender laman.

# Predefined zoom values
page_scale_width=Lebar Laman
page_scale_fit=Muat Laman
page_scale_auto=Perbesaran Otomatis
page_scale_actual=Ukuran Asli
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=Galat
loading_error=Galat terjadi saat memuat PDF.
invalid_file_error=Berkas PDF tidak valid atau rusak.
missing_file_error=Berkas PDF tidak ada.
unexpected_response_error=Balasan server yang tidak diharapkan.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[Anotasi {{type}}]
password_label=Masukkan sandi untuk membuka berkas PDF ini.
password_invalid=Sandi tidak valid. Silakan coba lagi.
password_ok=Oke
password_cancel=Batal

printing_not_supported=Peringatan: Pencetakan tidak didukung secara lengkap pada peramban ini.
printing_not_ready=Peringatan: Berkas PDF masih belum dimuat secara lengkap untuk dapat dicetak.
web_fonts_disabled=Font web dinonaktifkan: tidak dapat menggunakan font PDF yang tersemat.
document_colors_not_allowed=Dokumen PDF tidak diizinkan untuk menggunakan warnanya sendiri karena setelan 'Izinkan laman memilih warna sendiri' dinonaktifkan pada pengaturan.
