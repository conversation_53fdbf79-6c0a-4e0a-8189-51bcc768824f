/**
 * @licstart The following is the entire license notice for the
 * JavaScript code in this page
 *
 * Copyright 2024 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * @licend The above is the entire license notice for the
 * JavaScript code in this page
 */var t={34:(t,e,i)=>{var s=i(4901);t.exports=function(t){return"object"==typeof t?null!==t:s(t)}},81:(t,e,i)=>{var s=i(9565),n=i(9306),r=i(8551),a=i(6823),o=i(851),l=TypeError;t.exports=function(t,e){var i=arguments.length<2?o(t):e;if(n(i))return r(s(i,t));throw new l(a(t)+" is not iterable")}},283:(t,e,i)=>{var s=i(9504),n=i(9039),r=i(4901),a=i(9297),o=i(3724),l=i(350).CONFIGURABLE,h=i(3706),c=i(1181),d=c.enforce,u=c.get,p=String,g=Object.defineProperty,f=s("".slice),m=s("".replace),b=s([].join),v=o&&!n((function(){return 8!==g((function(){}),"length",{value:8}).length})),w=String(String).split("String"),y=t.exports=function(t,e,i){"Symbol("===f(p(e),0,7)&&(e="["+m(p(e),/^Symbol\(([^)]*)\).*$/,"$1")+"]");i&&i.getter&&(e="get "+e);i&&i.setter&&(e="set "+e);(!a(t,"name")||l&&t.name!==e)&&(o?g(t,"name",{value:e,configurable:!0}):t.name=e);v&&i&&a(i,"arity")&&t.length!==i.arity&&g(t,"length",{value:i.arity});try{i&&a(i,"constructor")&&i.constructor?o&&g(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var s=d(t);a(s,"source")||(s.source=b(w,"string"==typeof e?e:""));return t};Function.prototype.toString=y((function toString(){return r(this)&&u(this).source||h(this)}),"toString")},350:(t,e,i)=>{var s=i(3724),n=i(9297),r=Function.prototype,a=s&&Object.getOwnPropertyDescriptor,o=n(r,"name"),l=o&&"something"===function something(){}.name,h=o&&(!s||s&&a(r,"name").configurable);t.exports={EXISTS:o,PROPER:l,CONFIGURABLE:h}},397:(t,e,i)=>{var s=i(7751);t.exports=s("document","documentElement")},421:t=>{t.exports={}},507:(t,e,i)=>{var s=i(9565);t.exports=function(t,e,i){for(var n,r,a=i?t:t.iterator,o=t.next;!(n=s(o,a)).done;)if(void 0!==(r=e(n.value)))return r}},531:(t,e,i)=>{var s=i(6518),n=i(9565),r=i(9306),a=i(8551),o=i(1767),l=i(8646),h=i(9462),c=i(9539),d=i(6395),u=h((function(){for(var t,e,i=this.iterator,s=this.mapper;;){if(e=this.inner)try{if(!(t=a(n(e.next,e.iterator))).done)return t.value;this.inner=null}catch(t){c(i,"throw",t)}t=a(n(this.next,i));if(this.done=!!t.done)return;try{this.inner=l(s(t.value,this.counter++),!1)}catch(t){c(i,"throw",t)}}}));s({target:"Iterator",proto:!0,real:!0,forced:d},{flatMap:function flatMap(t){a(this);r(t);return new u(o(this),{mapper:t,inner:null})}})},616:(t,e,i)=>{var s=i(9039);t.exports=!s((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")}))},655:(t,e,i)=>{var s=i(6955),n=String;t.exports=function(t){if("Symbol"===s(t))throw new TypeError("Cannot convert a Symbol value to a string");return n(t)}},679:(t,e,i)=>{var s=i(1625),n=TypeError;t.exports=function(t,e){if(s(e,t))return t;throw new n("Incorrect invocation")}},713:(t,e,i)=>{var s=i(9565),n=i(9306),r=i(8551),a=i(1767),o=i(9462),l=i(6319),h=o((function(){var t=this.iterator,e=r(s(this.next,t));if(!(this.done=!!e.done))return l(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function map(t){r(this);n(t);return new h(a(this),{mapper:t})}},741:t=>{var e=Math.ceil,i=Math.floor;t.exports=Math.trunc||function trunc(t){var s=+t;return(s>0?i:e)(s)}},757:(t,e,i)=>{var s=i(7751),n=i(4901),r=i(1625),a=i(7040),o=Object;t.exports=a?function(t){return"symbol"==typeof t}:function(t){var e=s("Symbol");return n(e)&&r(e.prototype,o(t))}},851:(t,e,i)=>{var s=i(6955),n=i(5966),r=i(4117),a=i(6269),o=i(8227)("iterator");t.exports=function(t){if(!r(t))return n(t,o)||n(t,"@@iterator")||a[s(t)]}},944:t=>{var e=TypeError;t.exports=function(t){var i=t&&t.alphabet;if(void 0===i||"base64"===i||"base64url"===i)return i||"base64";throw new e("Incorrect `alphabet` option")}},1072:(t,e,i)=>{var s=i(1828),n=i(8727);t.exports=Object.keys||function keys(t){return s(t,n)}},1103:t=>{t.exports=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}}},1148:(t,e,i)=>{var s=i(6518),n=i(2652),r=i(9306),a=i(8551),o=i(1767);s({target:"Iterator",proto:!0,real:!0},{every:function every(t){a(this);r(t);var e=o(this),i=0;return!n(e,(function(e,s){if(!t(e,i++))return s()}),{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},1181:(t,e,i)=>{var s,n,r,a=i(8622),o=i(4576),l=i(34),h=i(6699),c=i(9297),d=i(7629),u=i(6119),p=i(421),g="Object already initialized",f=o.TypeError,m=o.WeakMap;if(a||d.state){var b=d.state||(d.state=new m);b.get=b.get;b.has=b.has;b.set=b.set;s=function(t,e){if(b.has(t))throw new f(g);e.facade=t;b.set(t,e);return e};n=function(t){return b.get(t)||{}};r=function(t){return b.has(t)}}else{var v=u("state");p[v]=!0;s=function(t,e){if(c(t,v))throw new f(g);e.facade=t;h(t,v,e);return e};n=function(t){return c(t,v)?t[v]:{}};r=function(t){return c(t,v)}}t.exports={set:s,get:n,has:r,enforce:function(t){return r(t)?n(t):s(t,{})},getterFor:function(t){return function(e){var i;if(!l(e)||(i=n(e)).type!==t)throw new f("Incompatible receiver, "+t+" required");return i}}}},1291:(t,e,i)=>{var s=i(741);t.exports=function(t){var e=+t;return e!=e||0===e?0:s(e)}},1548:(t,e,i)=>{var s=i(4576),n=i(9039),r=i(9519),a=i(4215),o=s.structuredClone;t.exports=!!o&&!n((function(){if("DENO"===a&&r>92||"NODE"===a&&r>94||"BROWSER"===a&&r>97)return!1;var t=new ArrayBuffer(8),e=o(t,{transfer:[t]});return 0!==t.byteLength||8!==e.byteLength}))},1549:(t,e,i)=>{var s=i(6518),n=i(4576),r=i(9143),a=i(4154);n.Uint8Array&&s({target:"Uint8Array",proto:!0},{setFromBase64:function setFromBase64(t){a(this);var e=r(t,arguments.length>1?arguments[1]:void 0,this,this.length);return{read:e.read,written:e.written}}})},1625:(t,e,i)=>{var s=i(9504);t.exports=s({}.isPrototypeOf)},1689:(t,e,i)=>{var s=i(6518),n=i(4576),r=i(8745),a=i(7680),o=i(6043),l=i(9306),h=i(1103),c=n.Promise,d=!1;s({target:"Promise",stat:!0,forced:!c||!c.try||h((function(){c.try((function(t){d=8===t}),8)})).error||!d},{try:function(t){var e=arguments.length>1?a(arguments,1):[],i=o.f(this),s=h((function(){return r(l(t),void 0,e)}));(s.error?i.reject:i.resolve)(s.value);return i.promise}})},1698:(t,e,i)=>{var s=i(6518),n=i(4204);s({target:"Set",proto:!0,real:!0,forced:!i(4916)("union")},{union:n})},1701:(t,e,i)=>{var s=i(6518),n=i(713);s({target:"Iterator",proto:!0,real:!0,forced:i(6395)},{map:n})},1767:t=>{t.exports=function(t){return{iterator:t,next:t.next,done:!1}}},1828:(t,e,i)=>{var s=i(9504),n=i(9297),r=i(5397),a=i(9617).indexOf,o=i(421),l=s([].push);t.exports=function(t,e){var i,s=r(t),h=0,c=[];for(i in s)!n(o,i)&&n(s,i)&&l(c,i);for(;e.length>h;)n(s,i=e[h++])&&(~a(c,i)||l(c,i));return c}},2106:(t,e,i)=>{var s=i(283),n=i(4913);t.exports=function(t,e,i){i.get&&s(i.get,e,{getter:!0});i.set&&s(i.set,e,{setter:!0});return n.f(t,e,i)}},2140:(t,e,i)=>{var s={};s[i(8227)("toStringTag")]="z";t.exports="[object z]"===String(s)},2195:(t,e,i)=>{var s=i(9504),n=s({}.toString),r=s("".slice);t.exports=function(t){return r(n(t),8,-1)}},2211:(t,e,i)=>{var s=i(9039);t.exports=!s((function(){function F(){}F.prototype.constructor=null;return Object.getPrototypeOf(new F)!==F.prototype}))},2303:(t,e,i)=>{var s=i(4576),n=i(9504),r=s.Uint8Array,a=s.SyntaxError,o=s.parseInt,l=Math.min,h=/[^\da-f]/i,c=n(h.exec),d=n("".slice);t.exports=function(t,e){var i=t.length;if(i%2!=0)throw new a("String should be an even number of characters");for(var s=e?l(e.length,i/2):i/2,n=e||new r(s),u=0,p=0;p<s;){var g=d(t,u,u+=2);if(c(h,g))throw new a("String should only contain hex characters");n[p++]=o(g,16)}return{bytes:n,read:u}}},2360:(t,e,i)=>{var s,n=i(8551),r=i(6801),a=i(8727),o=i(421),l=i(397),h=i(4055),c=i(6119),d="prototype",u="script",p=c("IE_PROTO"),EmptyConstructor=function(){},scriptTag=function(t){return"<"+u+">"+t+"</"+u+">"},NullProtoObjectViaActiveX=function(t){t.write(scriptTag(""));t.close();var e=t.parentWindow.Object;t=null;return e},NullProtoObject=function(){try{s=new ActiveXObject("htmlfile")}catch(t){}NullProtoObject="undefined"!=typeof document?document.domain&&s?NullProtoObjectViaActiveX(s):function(){var t,e=h("iframe"),i="java"+u+":";e.style.display="none";l.appendChild(e);e.src=String(i);(t=e.contentWindow.document).open();t.write(scriptTag("document.F=Object"));t.close();return t.F}():NullProtoObjectViaActiveX(s);for(var t=a.length;t--;)delete NullProtoObject[d][a[t]];return NullProtoObject()};o[p]=!0;t.exports=Object.create||function create(t,e){var i;if(null!==t){EmptyConstructor[d]=n(t);i=new EmptyConstructor;EmptyConstructor[d]=null;i[p]=t}else i=NullProtoObject();return void 0===e?i:r.f(i,e)}},2475:(t,e,i)=>{var s=i(6518),n=i(8527);s({target:"Set",proto:!0,real:!0,forced:!i(4916)("isSupersetOf",(function(t){return!t}))},{isSupersetOf:n})},2489:(t,e,i)=>{var s=i(6518),n=i(9565),r=i(9306),a=i(8551),o=i(1767),l=i(9462),h=i(6319),c=i(6395),d=l((function(){for(var t,e,i=this.iterator,s=this.predicate,r=this.next;;){t=a(n(r,i));if(this.done=!!t.done)return;e=t.value;if(h(i,s,[e,this.counter++],!0))return e}}));s({target:"Iterator",proto:!0,real:!0,forced:c},{filter:function filter(t){a(this);r(t);return new d(o(this),{predicate:t})}})},2529:t=>{t.exports=function(t,e){return{value:t,done:e}}},2603:(t,e,i)=>{var s=i(655);t.exports=function(t,e){return void 0===t?arguments.length<2?"":e:s(t)}},2652:(t,e,i)=>{var s=i(6080),n=i(9565),r=i(8551),a=i(6823),o=i(4209),l=i(6198),h=i(1625),c=i(81),d=i(851),u=i(9539),p=TypeError,Result=function(t,e){this.stopped=t;this.result=e},g=Result.prototype;t.exports=function(t,e,i){var f,m,b,v,w,y,A,_=i&&i.that,x=!(!i||!i.AS_ENTRIES),E=!(!i||!i.IS_RECORD),S=!(!i||!i.IS_ITERATOR),C=!(!i||!i.INTERRUPTED),T=s(e,_),stop=function(t){f&&u(f,"normal",t);return new Result(!0,t)},callFn=function(t){if(x){r(t);return C?T(t[0],t[1],stop):T(t[0],t[1])}return C?T(t,stop):T(t)};if(E)f=t.iterator;else if(S)f=t;else{if(!(m=d(t)))throw new p(a(t)+" is not iterable");if(o(m)){for(b=0,v=l(t);v>b;b++)if((w=callFn(t[b]))&&h(g,w))return w;return new Result(!1)}f=c(t,m)}y=E?t.next:f.next;for(;!(A=n(y,f)).done;){try{w=callFn(A.value)}catch(t){u(f,"throw",t)}if("object"==typeof w&&w&&h(g,w))return w}return new Result(!1)}},2777:(t,e,i)=>{var s=i(9565),n=i(34),r=i(757),a=i(5966),o=i(4270),l=i(8227),h=TypeError,c=l("toPrimitive");t.exports=function(t,e){if(!n(t)||r(t))return t;var i,l=a(t,c);if(l){void 0===e&&(e="default");i=s(l,t,e);if(!n(i)||r(i))return i;throw new h("Can't convert object to primitive value")}void 0===e&&(e="number");return o(t,e)}},2787:(t,e,i)=>{var s=i(9297),n=i(4901),r=i(8981),a=i(6119),o=i(2211),l=a("IE_PROTO"),h=Object,c=h.prototype;t.exports=o?h.getPrototypeOf:function(t){var e=r(t);if(s(e,l))return e[l];var i=e.constructor;return n(i)&&e instanceof i?i.prototype:e instanceof h?c:null}},2796:(t,e,i)=>{var s=i(9039),n=i(4901),r=/#|\.prototype\./,isForced=function(t,e){var i=o[a(t)];return i===h||i!==l&&(n(e)?s(e):!!e)},a=isForced.normalize=function(t){return String(t).replace(r,".").toLowerCase()},o=isForced.data={},l=isForced.NATIVE="N",h=isForced.POLYFILL="P";t.exports=isForced},2804:t=>{var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",i=e+"+/",s=e+"-_",inverse=function(t){for(var e={},i=0;i<64;i++)e[t.charAt(i)]=i;return e};t.exports={i2c:i,c2i:inverse(i),i2cUrl:s,c2iUrl:inverse(s)}},2812:t=>{var e=TypeError;t.exports=function(t,i){if(t<i)throw new e("Not enough arguments");return t}},2839:(t,e,i)=>{var s=i(4576).navigator,n=s&&s.userAgent;t.exports=n?String(n):""},2967:(t,e,i)=>{var s=i(6706),n=i(34),r=i(7750),a=i(3506);t.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,i={};try{(t=s(Object.prototype,"__proto__","set"))(i,[]);e=i instanceof Array}catch(t){}return function setPrototypeOf(i,s){r(i);a(s);if(!n(i))return i;e?t(i,s):i.__proto__=s;return i}}():void 0)},3167:(t,e,i)=>{var s=i(4901),n=i(34),r=i(2967);t.exports=function(t,e,i){var a,o;r&&s(a=e.constructor)&&a!==i&&n(o=a.prototype)&&o!==i.prototype&&r(t,o);return t}},3238:(t,e,i)=>{var s=i(4576),n=i(7811),r=i(7394),a=s.DataView;t.exports=function(t){if(!n||0!==r(t))return!1;try{new a(t);return!1}catch(t){return!0}}},3392:(t,e,i)=>{var s=i(9504),n=0,r=Math.random(),a=s(1..toString);t.exports=function(t){return"Symbol("+(void 0===t?"":t)+")_"+a(++n+r,36)}},3440:(t,e,i)=>{var s=i(7080),n=i(4402),r=i(9286),a=i(5170),o=i(3789),l=i(8469),h=i(507),c=n.has,d=n.remove;t.exports=function difference(t){var e=s(this),i=o(t),n=r(e);a(e)<=i.size?l(e,(function(t){i.includes(t)&&d(n,t)})):h(i.getIterator(),(function(t){c(e,t)&&d(n,t)}));return n}},3463:t=>{var e=TypeError;t.exports=function(t){if("string"==typeof t)return t;throw new e("Argument is not a string")}},3506:(t,e,i)=>{var s=i(3925),n=String,r=TypeError;t.exports=function(t){if(s(t))return t;throw new r("Can't set "+n(t)+" as a prototype")}},3579:(t,e,i)=>{var s=i(6518),n=i(2652),r=i(9306),a=i(8551),o=i(1767);s({target:"Iterator",proto:!0,real:!0},{some:function some(t){a(this);r(t);var e=o(this),i=0;return n(e,(function(e,s){if(t(e,i++))return s()}),{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},3650:(t,e,i)=>{var s=i(7080),n=i(4402),r=i(9286),a=i(3789),o=i(507),l=n.add,h=n.has,c=n.remove;t.exports=function symmetricDifference(t){var e=s(this),i=a(t).getIterator(),n=r(e);o(i,(function(t){h(e,t)?c(n,t):l(n,t)}));return n}},3706:(t,e,i)=>{var s=i(9504),n=i(4901),r=i(7629),a=s(Function.toString);n(r.inspectSource)||(r.inspectSource=function(t){return a(t)});t.exports=r.inspectSource},3717:(t,e)=>{e.f=Object.getOwnPropertySymbols},3724:(t,e,i)=>{var s=i(9039);t.exports=!s((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},3789:(t,e,i)=>{var s=i(9306),n=i(8551),r=i(9565),a=i(1291),o=i(1767),l="Invalid size",h=RangeError,c=TypeError,d=Math.max,SetRecord=function(t,e){this.set=t;this.size=d(e,0);this.has=s(t.has);this.keys=s(t.keys)};SetRecord.prototype={getIterator:function(){return o(n(r(this.keys,this.set)))},includes:function(t){return r(this.has,this.set,t)}};t.exports=function(t){n(t);var e=+t.size;if(e!=e)throw new c(l);var i=a(e);if(i<0)throw new h(l);return new SetRecord(t,i)}},3838:(t,e,i)=>{var s=i(7080),n=i(5170),r=i(8469),a=i(3789);t.exports=function isSubsetOf(t){var e=s(this),i=a(t);return!(n(e)>i.size)&&!1!==r(e,(function(t){if(!i.includes(t))return!1}),!0)}},3853:(t,e,i)=>{var s=i(6518),n=i(4449);s({target:"Set",proto:!0,real:!0,forced:!i(4916)("isDisjointFrom",(function(t){return!t}))},{isDisjointFrom:n})},3925:(t,e,i)=>{var s=i(34);t.exports=function(t){return s(t)||null===t}},3972:(t,e,i)=>{var s=i(34),n=String,r=TypeError;t.exports=function(t){if(void 0===t||s(t))return t;throw new r(n(t)+" is not an object or undefined")}},4055:(t,e,i)=>{var s=i(4576),n=i(34),r=s.document,a=n(r)&&n(r.createElement);t.exports=function(t){return a?r.createElement(t):{}}},4114:(t,e,i)=>{var s=i(6518),n=i(8981),r=i(6198),a=i(4527),o=i(6837);s({target:"Array",proto:!0,arity:1,forced:i(9039)((function(){return 4294967297!==[].push.call({length:4294967296},1)}))||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(t){return t instanceof TypeError}}()},{push:function push(t){var e=n(this),i=r(e),s=arguments.length;o(i+s);for(var l=0;l<s;l++){e[i]=arguments[l];i++}a(e,i);return i}})},4117:t=>{t.exports=function(t){return null==t}},4149:t=>{var e=RangeError;t.exports=function(t){if(t==t)return t;throw new e("NaN is not allowed")}},4154:(t,e,i)=>{var s=i(6955),n=TypeError;t.exports=function(t){if("Uint8Array"===s(t))return t;throw new n("Argument is not an Uint8Array")}},4204:(t,e,i)=>{var s=i(7080),n=i(4402).add,r=i(9286),a=i(3789),o=i(507);t.exports=function union(t){var e=s(this),i=a(t).getIterator(),l=r(e);o(i,(function(t){n(l,t)}));return l}},4209:(t,e,i)=>{var s=i(8227),n=i(6269),r=s("iterator"),a=Array.prototype;t.exports=function(t){return void 0!==t&&(n.Array===t||a[r]===t)}},4215:(t,e,i)=>{var s=i(4576),n=i(2839),r=i(2195),userAgentStartsWith=function(t){return n.slice(0,t.length)===t};t.exports=userAgentStartsWith("Bun/")?"BUN":userAgentStartsWith("Cloudflare-Workers")?"CLOUDFLARE":userAgentStartsWith("Deno/")?"DENO":userAgentStartsWith("Node.js/")?"NODE":s.Bun&&"string"==typeof Bun.version?"BUN":s.Deno&&"object"==typeof Deno.version?"DENO":"process"===r(s.process)?"NODE":s.window&&s.document?"BROWSER":"REST"},4270:(t,e,i)=>{var s=i(9565),n=i(4901),r=i(34),a=TypeError;t.exports=function(t,e){var i,o;if("string"===e&&n(i=t.toString)&&!r(o=s(i,t)))return o;if(n(i=t.valueOf)&&!r(o=s(i,t)))return o;if("string"!==e&&n(i=t.toString)&&!r(o=s(i,t)))return o;throw new a("Can't convert object to primitive value")}},4376:(t,e,i)=>{var s=i(2195);t.exports=Array.isArray||function isArray(t){return"Array"===s(t)}},4402:(t,e,i)=>{var s=i(9504),n=Set.prototype;t.exports={Set,add:s(n.add),has:s(n.has),remove:s(n.delete),proto:n}},4449:(t,e,i)=>{var s=i(7080),n=i(4402).has,r=i(5170),a=i(3789),o=i(8469),l=i(507),h=i(9539);t.exports=function isDisjointFrom(t){var e=s(this),i=a(t);if(r(e)<=i.size)return!1!==o(e,(function(t){if(i.includes(t))return!1}),!0);var c=i.getIterator();return!1!==l(c,(function(t){if(n(e,t))return h(c,"normal",!1)}))}},4483:(t,e,i)=>{var s,n,r,a,o=i(4576),l=i(9429),h=i(1548),c=o.structuredClone,d=o.ArrayBuffer,u=o.MessageChannel,p=!1;if(h)p=function(t){c(t,{transfer:[t]})};else if(d)try{u||(s=l("worker_threads"))&&(u=s.MessageChannel);if(u){n=new u;r=new d(2);a=function(t){n.port1.postMessage(null,[t])};if(2===r.byteLength){a(r);0===r.byteLength&&(p=a)}}}catch(t){}t.exports=p},4495:(t,e,i)=>{var s=i(9519),n=i(9039),r=i(4576).String;t.exports=!!Object.getOwnPropertySymbols&&!n((function(){var t=Symbol("symbol detection");return!r(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&s&&s<41}))},4527:(t,e,i)=>{var s=i(3724),n=i(4376),r=TypeError,a=Object.getOwnPropertyDescriptor,o=s&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}();t.exports=o?function(t,e){if(n(t)&&!a(t,"length").writable)throw new r("Cannot set read only .length");return t.length=e}:function(t,e){return t.length=e}},4576:function(t){var check=function(t){return t&&t.Math===Math&&t};t.exports=check("object"==typeof globalThis&&globalThis)||check("object"==typeof window&&window)||check("object"==typeof self&&self)||check("object"==typeof global&&global)||check("object"==typeof this&&this)||function(){return this}()||Function("return this")()},4603:(t,e,i)=>{var s=i(6840),n=i(9504),r=i(655),a=i(2812),o=URLSearchParams,l=o.prototype,h=n(l.append),c=n(l.delete),d=n(l.forEach),u=n([].push),p=new o("a=1&a=2&b=3");p.delete("a",1);p.delete("b",void 0);p+""!="a=2"&&s(l,"delete",(function(t){var e=arguments.length,i=e<2?void 0:arguments[1];if(e&&void 0===i)return c(this,t);var s=[];d(this,(function(t,e){u(s,{key:e,value:t})}));a(e,1);for(var n,o=r(t),l=r(i),p=0,g=0,f=!1,m=s.length;p<m;){n=s[p++];if(f||n.key===o){f=!0;c(this,n.key)}else g++}for(;g<m;)(n=s[g++]).key===o&&n.value===l||h(this,n.key,n.value)}),{enumerable:!0,unsafe:!0})},4628:(t,e,i)=>{var s=i(6518),n=i(6043);s({target:"Promise",stat:!0},{withResolvers:function withResolvers(){var t=n.f(this);return{promise:t.promise,resolve:t.resolve,reject:t.reject}}})},4659:(t,e,i)=>{var s=i(3724),n=i(4913),r=i(6980);t.exports=function(t,e,i){s?n.f(t,e,r(0,i)):t[e]=i}},4901:t=>{var e="object"==typeof document&&document.all;t.exports=void 0===e&&void 0!==e?function(t){return"function"==typeof t||t===e}:function(t){return"function"==typeof t}},4913:(t,e,i)=>{var s=i(3724),n=i(5917),r=i(8686),a=i(8551),o=i(6969),l=TypeError,h=Object.defineProperty,c=Object.getOwnPropertyDescriptor,d="enumerable",u="configurable",p="writable";e.f=s?r?function defineProperty(t,e,i){a(t);e=o(e);a(i);if("function"==typeof t&&"prototype"===e&&"value"in i&&p in i&&!i[p]){var s=c(t,e);if(s&&s[p]){t[e]=i.value;i={configurable:u in i?i[u]:s[u],enumerable:d in i?i[d]:s[d],writable:!1}}}return h(t,e,i)}:h:function defineProperty(t,e,i){a(t);e=o(e);a(i);if(n)try{return h(t,e,i)}catch(t){}if("get"in i||"set"in i)throw new l("Accessors not supported");"value"in i&&(t[e]=i.value);return t}},4916:(t,e,i)=>{var s=i(7751),createSetLike=function(t){return{size:t,has:function(){return!1},keys:function(){return{next:function(){return{done:!0}}}}}},createSetLikeWithInfinitySize=function(t){return{size:t,has:function(){return!0},keys:function(){throw new Error("e")}}};t.exports=function(t,e){var i=s("Set");try{(new i)[t](createSetLike(0));try{(new i)[t](createSetLike(-1));return!1}catch(s){if(!e)return!0;try{(new i)[t](createSetLikeWithInfinitySize(-1/0));return!1}catch(s){var n=new i;n.add(1);n.add(2);return e(n[t](createSetLikeWithInfinitySize(1/0)))}}}catch(t){return!1}}},4979:(t,e,i)=>{var s=i(6518),n=i(4576),r=i(7751),a=i(6980),o=i(4913).f,l=i(9297),h=i(679),c=i(3167),d=i(2603),u=i(5002),p=i(8574),g=i(3724),f=i(6395),m="DOMException",b=r("Error"),v=r(m),w=function DOMException(){h(this,y);var t=arguments.length,e=d(t<1?void 0:arguments[0]),i=d(t<2?void 0:arguments[1],"Error"),s=new v(e,i),n=new b(e);n.name=m;o(s,"stack",a(1,p(n.stack,1)));c(s,this,w);return s},y=w.prototype=v.prototype,A="stack"in new b(m),_="stack"in new v(1,2),x=v&&g&&Object.getOwnPropertyDescriptor(n,m),E=!(!x||x.writable&&x.configurable),S=A&&!E&&!_;s({global:!0,constructor:!0,forced:f||S},{DOMException:S?w:v});var C=r(m),T=C.prototype;if(T.constructor!==C){f||o(T,"constructor",a(1,C));for(var D in u)if(l(u,D)){var M=u[D],P=M.s;l(C,P)||o(C,P,a(6,M.c))}}},5002:t=>{t.exports={IndexSizeError:{s:"INDEX_SIZE_ERR",c:1,m:1},DOMStringSizeError:{s:"DOMSTRING_SIZE_ERR",c:2,m:0},HierarchyRequestError:{s:"HIERARCHY_REQUEST_ERR",c:3,m:1},WrongDocumentError:{s:"WRONG_DOCUMENT_ERR",c:4,m:1},InvalidCharacterError:{s:"INVALID_CHARACTER_ERR",c:5,m:1},NoDataAllowedError:{s:"NO_DATA_ALLOWED_ERR",c:6,m:0},NoModificationAllowedError:{s:"NO_MODIFICATION_ALLOWED_ERR",c:7,m:1},NotFoundError:{s:"NOT_FOUND_ERR",c:8,m:1},NotSupportedError:{s:"NOT_SUPPORTED_ERR",c:9,m:1},InUseAttributeError:{s:"INUSE_ATTRIBUTE_ERR",c:10,m:1},InvalidStateError:{s:"INVALID_STATE_ERR",c:11,m:1},SyntaxError:{s:"SYNTAX_ERR",c:12,m:1},InvalidModificationError:{s:"INVALID_MODIFICATION_ERR",c:13,m:1},NamespaceError:{s:"NAMESPACE_ERR",c:14,m:1},InvalidAccessError:{s:"INVALID_ACCESS_ERR",c:15,m:1},ValidationError:{s:"VALIDATION_ERR",c:16,m:0},TypeMismatchError:{s:"TYPE_MISMATCH_ERR",c:17,m:1},SecurityError:{s:"SECURITY_ERR",c:18,m:1},NetworkError:{s:"NETWORK_ERR",c:19,m:1},AbortError:{s:"ABORT_ERR",c:20,m:1},URLMismatchError:{s:"URL_MISMATCH_ERR",c:21,m:1},QuotaExceededError:{s:"QUOTA_EXCEEDED_ERR",c:22,m:1},TimeoutError:{s:"TIMEOUT_ERR",c:23,m:1},InvalidNodeTypeError:{s:"INVALID_NODE_TYPE_ERR",c:24,m:1},DataCloneError:{s:"DATA_CLONE_ERR",c:25,m:1}}},5024:(t,e,i)=>{var s=i(6518),n=i(3650);s({target:"Set",proto:!0,real:!0,forced:!i(4916)("symmetricDifference")},{symmetricDifference:n})},5031:(t,e,i)=>{var s=i(7751),n=i(9504),r=i(8480),a=i(3717),o=i(8551),l=n([].concat);t.exports=s("Reflect","ownKeys")||function ownKeys(t){var e=r.f(o(t)),i=a.f;return i?l(e,i(t)):e}},5169:(t,e,i)=>{var s=i(3238),n=TypeError;t.exports=function(t){if(s(t))throw new n("ArrayBuffer is detached");return t}},5170:(t,e,i)=>{var s=i(6706),n=i(4402);t.exports=s(n.proto,"size","get")||function(t){return t.size}},5370:(t,e,i)=>{var s=i(6198);t.exports=function(t,e,i){for(var n=0,r=arguments.length>2?i:s(e),a=new t(r);r>n;)a[n]=e[n++];return a}},5397:(t,e,i)=>{var s=i(7055),n=i(7750);t.exports=function(t){return s(n(t))}},5610:(t,e,i)=>{var s=i(1291),n=Math.max,r=Math.min;t.exports=function(t,e){var i=s(t);return i<0?n(i+e,0):r(i,e)}},5623:(t,e,i)=>{var s=i(6518),n=i(4576),r=i(9504),a=i(4154),o=i(5169),l=r(1..toString);n.Uint8Array&&s({target:"Uint8Array",proto:!0},{toHex:function toHex(){a(this);o(this.buffer);for(var t="",e=0,i=this.length;e<i;e++){var s=l(this[e],16);t+=1===s.length?"0"+s:s}return t}})},5636:(t,e,i)=>{var s=i(4576),n=i(9504),r=i(6706),a=i(7696),o=i(5169),l=i(7394),h=i(4483),c=i(1548),d=s.structuredClone,u=s.ArrayBuffer,p=s.DataView,g=Math.min,f=u.prototype,m=p.prototype,b=n(f.slice),v=r(f,"resizable","get"),w=r(f,"maxByteLength","get"),y=n(m.getInt8),A=n(m.setInt8);t.exports=(c||h)&&function(t,e,i){var s,n=l(t),r=void 0===e?n:a(e),f=!v||!v(t);o(t);if(c){t=d(t,{transfer:[t]});if(n===r&&(i||f))return t}if(n>=r&&(!i||f))s=b(t,0,r);else{var m=i&&!f&&w?{maxByteLength:w(t)}:void 0;s=new u(r,m);for(var _=new p(t),x=new p(s),E=g(r,n),S=0;S<E;S++)A(x,S,y(_,S))}c||h(t);return s}},5745:(t,e,i)=>{var s=i(7629);t.exports=function(t,e){return s[t]||(s[t]=e||{})}},5781:(t,e,i)=>{var s=i(6518),n=i(7751),r=i(2812),a=i(655),o=i(7416),l=n("URL");s({target:"URL",stat:!0,forced:!o},{parse:function parse(t){var e=r(arguments.length,1),i=a(t),s=e<2||void 0===arguments[1]?void 0:a(arguments[1]);try{return new l(i,s)}catch(t){return null}}})},5876:(t,e,i)=>{var s=i(6518),n=i(3838);s({target:"Set",proto:!0,real:!0,forced:!i(4916)("isSubsetOf",(function(t){return t}))},{isSubsetOf:n})},5917:(t,e,i)=>{var s=i(3724),n=i(9039),r=i(4055);t.exports=!s&&!n((function(){return 7!==Object.defineProperty(r("div"),"a",{get:function(){return 7}}).a}))},5966:(t,e,i)=>{var s=i(9306),n=i(4117);t.exports=function(t,e){var i=t[e];return n(i)?void 0:s(i)}},6043:(t,e,i)=>{var s=i(9306),n=TypeError,PromiseCapability=function(t){var e,i;this.promise=new t((function(t,s){if(void 0!==e||void 0!==i)throw new n("Bad Promise constructor");e=t;i=s}));this.resolve=s(e);this.reject=s(i)};t.exports.f=function(t){return new PromiseCapability(t)}},6080:(t,e,i)=>{var s=i(7476),n=i(9306),r=i(616),a=s(s.bind);t.exports=function(t,e){n(t);return void 0===e?t:r?a(t,e):function(){return t.apply(e,arguments)}}},6119:(t,e,i)=>{var s=i(5745),n=i(3392),r=s("keys");t.exports=function(t){return r[t]||(r[t]=n(t))}},6193:(t,e,i)=>{var s=i(4215);t.exports="NODE"===s},6198:(t,e,i)=>{var s=i(8014);t.exports=function(t){return s(t.length)}},6269:t=>{t.exports={}},6279:(t,e,i)=>{var s=i(6840);t.exports=function(t,e,i){for(var n in e)s(t,n,e[n],i);return t}},6319:(t,e,i)=>{var s=i(8551),n=i(9539);t.exports=function(t,e,i,r){try{return r?e(s(i)[0],i[1]):e(i)}catch(e){n(t,"throw",e)}}},6395:t=>{t.exports=!1},6518:(t,e,i)=>{var s=i(4576),n=i(7347).f,r=i(6699),a=i(6840),o=i(9433),l=i(7740),h=i(2796);t.exports=function(t,e){var i,c,d,u,p,g=t.target,f=t.global,m=t.stat;if(i=f?s:m?s[g]||o(g,{}):s[g]&&s[g].prototype)for(c in e){u=e[c];d=t.dontCallGetSet?(p=n(i,c))&&p.value:i[c];if(!h(f?c:g+(m?".":"#")+c,t.forced)&&void 0!==d){if(typeof u==typeof d)continue;l(u,d)}(t.sham||d&&d.sham)&&r(u,"sham",!0);a(i,c,u,t)}}},6573:(t,e,i)=>{var s=i(3724),n=i(2106),r=i(3238),a=ArrayBuffer.prototype;s&&!("detached"in a)&&n(a,"detached",{configurable:!0,get:function detached(){return r(this)}})},6699:(t,e,i)=>{var s=i(3724),n=i(4913),r=i(6980);t.exports=s?function(t,e,i){return n.f(t,e,r(1,i))}:function(t,e,i){t[e]=i;return t}},6706:(t,e,i)=>{var s=i(9504),n=i(9306);t.exports=function(t,e,i){try{return s(n(Object.getOwnPropertyDescriptor(t,e)[i]))}catch(t){}}},6801:(t,e,i)=>{var s=i(3724),n=i(8686),r=i(4913),a=i(8551),o=i(5397),l=i(1072);e.f=s&&!n?Object.defineProperties:function defineProperties(t,e){a(t);for(var i,s=o(e),n=l(e),h=n.length,c=0;h>c;)r.f(t,i=n[c++],s[i]);return t}},6823:t=>{var e=String;t.exports=function(t){try{return e(t)}catch(t){return"Object"}}},6837:t=>{var e=TypeError;t.exports=function(t){if(t>9007199254740991)throw e("Maximum allowed index exceeded");return t}},6840:(t,e,i)=>{var s=i(4901),n=i(4913),r=i(283),a=i(9433);t.exports=function(t,e,i,o){o||(o={});var l=o.enumerable,h=void 0!==o.name?o.name:e;s(i)&&r(i,h,o);if(o.global)l?t[e]=i:a(e,i);else{try{o.unsafe?t[e]&&(l=!0):delete t[e]}catch(t){}l?t[e]=i:n.f(t,e,{value:i,enumerable:!1,configurable:!o.nonConfigurable,writable:!o.nonWritable})}return t}},6955:(t,e,i)=>{var s=i(2140),n=i(4901),r=i(2195),a=i(8227)("toStringTag"),o=Object,l="Arguments"===r(function(){return arguments}());t.exports=s?r:function(t){var e,i,s;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(i=function(t,e){try{return t[e]}catch(t){}}(e=o(t),a))?i:l?r(e):"Object"===(s=r(e))&&n(e.callee)?"Arguments":s}},6969:(t,e,i)=>{var s=i(2777),n=i(757);t.exports=function(t){var e=s(t,"string");return n(e)?e:e+""}},6980:t=>{t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},7040:(t,e,i)=>{var s=i(4495);t.exports=s&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},7055:(t,e,i)=>{var s=i(9504),n=i(9039),r=i(2195),a=Object,o=s("".split);t.exports=n((function(){return!a("z").propertyIsEnumerable(0)}))?function(t){return"String"===r(t)?o(t,""):a(t)}:a},7080:(t,e,i)=>{var s=i(4402).has;t.exports=function(t){s(t);return t}},7347:(t,e,i)=>{var s=i(3724),n=i(9565),r=i(8773),a=i(6980),o=i(5397),l=i(6969),h=i(9297),c=i(5917),d=Object.getOwnPropertyDescriptor;e.f=s?d:function getOwnPropertyDescriptor(t,e){t=o(t);e=l(e);if(c)try{return d(t,e)}catch(t){}if(h(t,e))return a(!n(r.f,t,e),t[e])}},7394:(t,e,i)=>{var s=i(4576),n=i(6706),r=i(2195),a=s.ArrayBuffer,o=s.TypeError;t.exports=a&&n(a.prototype,"byteLength","get")||function(t){if("ArrayBuffer"!==r(t))throw new o("ArrayBuffer expected");return t.byteLength}},7416:(t,e,i)=>{var s=i(9039),n=i(8227),r=i(3724),a=i(6395),o=n("iterator");t.exports=!s((function(){var t=new URL("b?a=1&b=2&c=3","https://a"),e=t.searchParams,i=new URLSearchParams("a=1&a=2&b=3"),s="";t.pathname="c%20d";e.forEach((function(t,i){e.delete("b");s+=i+t}));i.delete("a",2);i.delete("b",void 0);return a&&(!t.toJSON||!i.has("a",1)||i.has("a",2)||!i.has("a",void 0)||i.has("b"))||!e.size&&(a||!r)||!e.sort||"https://a/c%20d?a=1&c=3"!==t.href||"3"!==e.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!e[o]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("https://тест").host||"#%D0%B1"!==new URL("https://a#б").hash||"a1c3"!==s||"x"!==new URL("https://x",void 0).host}))},7476:(t,e,i)=>{var s=i(2195),n=i(9504);t.exports=function(t){if("Function"===s(t))return n(t)}},7566:(t,e,i)=>{var s=i(6840),n=i(9504),r=i(655),a=i(2812),o=URLSearchParams,l=o.prototype,h=n(l.getAll),c=n(l.has),d=new o("a=1");!d.has("a",2)&&d.has("a",void 0)||s(l,"has",(function has(t){var e=arguments.length,i=e<2?void 0:arguments[1];if(e&&void 0===i)return c(this,t);var s=h(this,t);a(e,1);for(var n=r(i),o=0;o<s.length;)if(s[o++]===n)return!0;return!1}),{enumerable:!0,unsafe:!0})},7629:(t,e,i)=>{var s=i(6395),n=i(4576),r=i(9433),a="__core-js_shared__",o=t.exports=n[a]||r(a,{});(o.versions||(o.versions=[])).push({version:"3.41.0",mode:s?"pure":"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.41.0/LICENSE",source:"https://github.com/zloirock/core-js"})},7642:(t,e,i)=>{var s=i(6518),n=i(3440);s({target:"Set",proto:!0,real:!0,forced:!i(4916)("difference",(function(t){return 0===t.size}))},{difference:n})},7657:(t,e,i)=>{var s,n,r,a=i(9039),o=i(4901),l=i(34),h=i(2360),c=i(2787),d=i(6840),u=i(8227),p=i(6395),g=u("iterator"),f=!1;[].keys&&("next"in(r=[].keys())?(n=c(c(r)))!==Object.prototype&&(s=n):f=!0);!l(s)||a((function(){var t={};return s[g].call(t)!==t}))?s={}:p&&(s=h(s));o(s[g])||d(s,g,(function(){return this}));t.exports={IteratorPrototype:s,BUGGY_SAFARI_ITERATORS:f}},7680:(t,e,i)=>{var s=i(9504);t.exports=s([].slice)},7696:(t,e,i)=>{var s=i(1291),n=i(8014),r=RangeError;t.exports=function(t){if(void 0===t)return 0;var e=s(t),i=n(e);if(e!==i)throw new r("Wrong length or index");return i}},7740:(t,e,i)=>{var s=i(9297),n=i(5031),r=i(7347),a=i(4913);t.exports=function(t,e,i){for(var o=n(e),l=a.f,h=r.f,c=0;c<o.length;c++){var d=o[c];s(t,d)||i&&s(i,d)||l(t,d,h(e,d))}}},7750:(t,e,i)=>{var s=i(4117),n=TypeError;t.exports=function(t){if(s(t))throw new n("Can't call method on "+t);return t}},7751:(t,e,i)=>{var s=i(4576),n=i(4901);t.exports=function(t,e){return arguments.length<2?(i=s[t],n(i)?i:void 0):s[t]&&s[t][e];var i}},7811:t=>{t.exports="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView},7936:(t,e,i)=>{var s=i(6518),n=i(5636);n&&s({target:"ArrayBuffer",proto:!0},{transferToFixedLength:function transferToFixedLength(){return n(this,arguments.length?arguments[0]:void 0,!1)}})},8004:(t,e,i)=>{var s=i(6518),n=i(9039),r=i(8750);s({target:"Set",proto:!0,real:!0,forced:!i(4916)("intersection",(function(t){return 2===t.size&&t.has(1)&&t.has(2)}))||n((function(){return"3,2"!==String(Array.from(new Set([1,2,3]).intersection(new Set([3,2]))))}))},{intersection:r})},8014:(t,e,i)=>{var s=i(1291),n=Math.min;t.exports=function(t){var e=s(t);return e>0?n(e,9007199254740991):0}},8100:(t,e,i)=>{var s=i(6518),n=i(5636);n&&s({target:"ArrayBuffer",proto:!0},{transfer:function transfer(){return n(this,arguments.length?arguments[0]:void 0,!0)}})},8111:(t,e,i)=>{var s=i(6518),n=i(4576),r=i(679),a=i(8551),o=i(4901),l=i(2787),h=i(2106),c=i(4659),d=i(9039),u=i(9297),p=i(8227),g=i(7657).IteratorPrototype,f=i(3724),m=i(6395),b="constructor",v="Iterator",w=p("toStringTag"),y=TypeError,A=n[v],_=m||!o(A)||A.prototype!==g||!d((function(){A({})})),x=function Iterator(){r(this,g);if(l(this)===g)throw new y("Abstract class Iterator not directly constructable")},defineIteratorPrototypeAccessor=function(t,e){f?h(g,t,{configurable:!0,get:function(){return e},set:function(e){a(this);if(this===g)throw new y("You can't redefine this property");u(this,t)?this[t]=e:c(this,t,e)}}):g[t]=e};u(g,w)||defineIteratorPrototypeAccessor(w,v);!_&&u(g,b)&&g[b]!==Object||defineIteratorPrototypeAccessor(b,x);x.prototype=g;s({global:!0,constructor:!0,forced:_},{Iterator:x})},8227:(t,e,i)=>{var s=i(4576),n=i(5745),r=i(9297),a=i(3392),o=i(4495),l=i(7040),h=s.Symbol,c=n("wks"),d=l?h.for||h:h&&h.withoutSetter||a;t.exports=function(t){r(c,t)||(c[t]=o&&r(h,t)?h[t]:d("Symbol."+t));return c[t]}},8235:(t,e,i)=>{var s=i(9504),n=i(9297),r=SyntaxError,a=parseInt,o=String.fromCharCode,l=s("".charAt),h=s("".slice),c=s(/./.exec),d={'\\"':'"',"\\\\":"\\","\\/":"/","\\b":"\b","\\f":"\f","\\n":"\n","\\r":"\r","\\t":"\t"},u=/^[\da-f]{4}$/i,p=/^[\u0000-\u001F]$/;t.exports=function(t,e){for(var i=!0,s="";e<t.length;){var g=l(t,e);if("\\"===g){var f=h(t,e,e+2);if(n(d,f)){s+=d[f];e+=2}else{if("\\u"!==f)throw new r('Unknown escape sequence: "'+f+'"');var m=h(t,e+=2,e+4);if(!c(u,m))throw new r("Bad Unicode escape at: "+e);s+=o(a(m,16));e+=4}}else{if('"'===g){i=!1;e++;break}if(c(p,g))throw new r("Bad control character in string literal at: "+e);s+=g;e++}}if(i)throw new r("Unterminated string at: "+e);return{value:s,end:e}}},8237:(t,e,i)=>{var s=i(6518),n=i(2652),r=i(9306),a=i(8551),o=i(1767),l=TypeError;s({target:"Iterator",proto:!0,real:!0},{reduce:function reduce(t){a(this);r(t);var e=o(this),i=arguments.length<2,s=i?void 0:arguments[1],h=0;n(e,(function(e){if(i){i=!1;s=e}else s=t(s,e,h);h++}),{IS_RECORD:!0});if(i)throw new l("Reduce of empty iterator with no initial value");return s}})},8335:(t,e,i)=>{var s=i(6518),n=i(3724),r=i(4576),a=i(7751),o=i(9504),l=i(9565),h=i(4901),c=i(34),d=i(4376),u=i(9297),p=i(655),g=i(6198),f=i(4659),m=i(9039),b=i(8235),v=i(4495),w=r.JSON,y=r.Number,A=r.SyntaxError,_=w&&w.parse,x=a("Object","keys"),E=Object.getOwnPropertyDescriptor,S=o("".charAt),C=o("".slice),T=o(/./.exec),D=o([].push),M=/^\d$/,P=/^[1-9]$/,k=/^[\d-]$/,I=/^[\t\n\r ]$/,internalize=function(t,e,i,s){var n,r,a,o,h,p=t[e],f=s&&p===s.value,m=f&&"string"==typeof s.source?{source:s.source}:{};if(c(p)){var b=d(p),v=f?s.nodes:b?[]:{};if(b){n=v.length;a=g(p);for(o=0;o<a;o++)internalizeProperty(p,o,internalize(p,""+o,i,o<n?v[o]:void 0))}else{r=x(p);a=g(r);for(o=0;o<a;o++){h=r[o];internalizeProperty(p,h,internalize(p,h,i,u(v,h)?v[h]:void 0))}}}return l(i,t,e,p,m)},internalizeProperty=function(t,e,i){if(n){var s=E(t,e);if(s&&!s.configurable)return}void 0===i?delete t[e]:f(t,e,i)},Node=function(t,e,i,s){this.value=t;this.end=e;this.source=i;this.nodes=s},Context=function(t,e){this.source=t;this.index=e};Context.prototype={fork:function(t){return new Context(this.source,t)},parse:function(){var t=this.source,e=this.skip(I,this.index),i=this.fork(e),s=S(t,e);if(T(k,s))return i.number();switch(s){case"{":return i.object();case"[":return i.array();case'"':return i.string();case"t":return i.keyword(!0);case"f":return i.keyword(!1);case"n":return i.keyword(null)}throw new A('Unexpected character: "'+s+'" at: '+e)},node:function(t,e,i,s,n){return new Node(e,s,t?null:C(this.source,i,s),n)},object:function(){for(var t=this.source,e=this.index+1,i=!1,s={},n={};e<t.length;){e=this.until(['"',"}"],e);if("}"===S(t,e)&&!i){e++;break}var r=this.fork(e).string(),a=r.value;e=r.end;e=this.until([":"],e)+1;e=this.skip(I,e);r=this.fork(e).parse();f(n,a,r);f(s,a,r.value);e=this.until([",","}"],r.end);var o=S(t,e);if(","===o){i=!0;e++}else if("}"===o){e++;break}}return this.node(1,s,this.index,e,n)},array:function(){for(var t=this.source,e=this.index+1,i=!1,s=[],n=[];e<t.length;){e=this.skip(I,e);if("]"===S(t,e)&&!i){e++;break}var r=this.fork(e).parse();D(n,r);D(s,r.value);e=this.until([",","]"],r.end);if(","===S(t,e)){i=!0;e++}else if("]"===S(t,e)){e++;break}}return this.node(1,s,this.index,e,n)},string:function(){var t=this.index,e=b(this.source,this.index+1);return this.node(0,e.value,t,e.end)},number:function(){var t=this.source,e=this.index,i=e;"-"===S(t,i)&&i++;if("0"===S(t,i))i++;else{if(!T(P,S(t,i)))throw new A("Failed to parse number at: "+i);i=this.skip(M,i+1)}"."===S(t,i)&&(i=this.skip(M,i+1));if("e"===S(t,i)||"E"===S(t,i)){i++;"+"!==S(t,i)&&"-"!==S(t,i)||i++;if(i===(i=this.skip(M,i)))throw new A("Failed to parse number's exponent value at: "+i)}return this.node(0,y(C(t,e,i)),e,i)},keyword:function(t){var e=""+t,i=this.index,s=i+e.length;if(C(this.source,i,s)!==e)throw new A("Failed to parse value at: "+i);return this.node(0,t,i,s)},skip:function(t,e){for(var i=this.source;e<i.length&&T(t,S(i,e));e++);return e},until:function(t,e){e=this.skip(I,e);for(var i=S(this.source,e),s=0;s<t.length;s++)if(t[s]===i)return e;throw new A('Unexpected character: "'+i+'" at: '+e)}};var R=m((function(){var t,e="9007199254740993";_(e,(function(e,i,s){t=s.source}));return t!==e})),L=v&&!m((function(){return 1/_("-0 \t")!=-1/0}));s({target:"JSON",stat:!0,forced:R},{parse:function parse(t,e){return L&&!h(e)?_(t):function(t,e){t=p(t);var i=new Context(t,0,""),s=i.parse(),n=s.value,r=i.skip(I,s.end);if(r<t.length)throw new A('Unexpected extra character: "'+S(t,r)+'" after the parsed data at: '+r);return h(e)?internalize({"":n},"",e,s):n}(t,e)}})},8469:(t,e,i)=>{var s=i(9504),n=i(507),r=i(4402),a=r.Set,o=r.proto,l=s(o.forEach),h=s(o.keys),c=h(new a).next;t.exports=function(t,e,i){return i?n({iterator:h(t),next:c},e):l(t,e)}},8480:(t,e,i)=>{var s=i(1828),n=i(8727).concat("length","prototype");e.f=Object.getOwnPropertyNames||function getOwnPropertyNames(t){return s(t,n)}},8527:(t,e,i)=>{var s=i(7080),n=i(4402).has,r=i(5170),a=i(3789),o=i(507),l=i(9539);t.exports=function isSupersetOf(t){var e=s(this),i=a(t);if(r(e)<i.size)return!1;var h=i.getIterator();return!1!==o(h,(function(t){if(!n(e,t))return l(h,"normal",!1)}))}},8551:(t,e,i)=>{var s=i(34),n=String,r=TypeError;t.exports=function(t){if(s(t))return t;throw new r(n(t)+" is not an object")}},8574:(t,e,i)=>{var s=i(9504),n=Error,r=s("".replace),a=String(new n("zxcasd").stack),o=/\n\s*at [^:]*:[^\n]*/,l=o.test(a);t.exports=function(t,e){if(l&&"string"==typeof t&&!n.prepareStackTrace)for(;e--;)t=r(t,o,"");return t}},8622:(t,e,i)=>{var s=i(4576),n=i(4901),r=s.WeakMap;t.exports=n(r)&&/native code/.test(String(r))},8646:(t,e,i)=>{var s=i(9565),n=i(8551),r=i(1767),a=i(851);t.exports=function(t,e){e&&"string"==typeof t||n(t);var i=a(t);return r(n(void 0!==i?s(i,t):t))}},8686:(t,e,i)=>{var s=i(3724),n=i(9039);t.exports=s&&n((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},8721:(t,e,i)=>{var s=i(3724),n=i(9504),r=i(2106),a=URLSearchParams.prototype,o=n(a.forEach);s&&!("size"in a)&&r(a,"size",{get:function size(){var t=0;o(this,(function(){t++}));return t},configurable:!0,enumerable:!0})},8727:t=>{t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},8745:(t,e,i)=>{var s=i(616),n=Function.prototype,r=n.apply,a=n.call;t.exports="object"==typeof Reflect&&Reflect.apply||(s?a.bind(r):function(){return a.apply(r,arguments)})},8750:(t,e,i)=>{var s=i(7080),n=i(4402),r=i(5170),a=i(3789),o=i(8469),l=i(507),h=n.Set,c=n.add,d=n.has;t.exports=function intersection(t){var e=s(this),i=a(t),n=new h;r(e)>i.size?l(i.getIterator(),(function(t){d(e,t)&&c(n,t)})):o(e,(function(t){i.includes(t)&&c(n,t)}));return n}},8773:(t,e)=>{var i={}.propertyIsEnumerable,s=Object.getOwnPropertyDescriptor,n=s&&!i.call({1:2},1);e.f=n?function propertyIsEnumerable(t){var e=s(this,t);return!!e&&e.enumerable}:i},8981:(t,e,i)=>{var s=i(7750),n=Object;t.exports=function(t){return n(s(t))}},9039:t=>{t.exports=function(t){try{return!!t()}catch(t){return!0}}},9143:(t,e,i)=>{var s=i(4576),n=i(9504),r=i(3972),a=i(3463),o=i(9297),l=i(2804),h=i(944),c=i(5169),d=l.c2i,u=l.c2iUrl,p=s.SyntaxError,g=s.TypeError,f=n("".charAt),skipAsciiWhitespace=function(t,e){for(var i=t.length;e<i;e++){var s=f(t,e);if(" "!==s&&"\t"!==s&&"\n"!==s&&"\f"!==s&&"\r"!==s)break}return e},decodeBase64Chunk=function(t,e,i){var s=t.length;s<4&&(t+=2===s?"AA":"A");var n=(e[f(t,0)]<<18)+(e[f(t,1)]<<12)+(e[f(t,2)]<<6)+e[f(t,3)],r=[n>>16&255,n>>8&255,255&n];if(2===s){if(i&&0!==r[1])throw new p("Extra bits");return[r[0]]}if(3===s){if(i&&0!==r[2])throw new p("Extra bits");return[r[0],r[1]]}return r},writeBytes=function(t,e,i){for(var s=e.length,n=0;n<s;n++)t[i+n]=e[n];return i+s};t.exports=function(t,e,i,s){a(t);r(e);var n="base64"===h(e)?d:u,l=e?e.lastChunkHandling:void 0;void 0===l&&(l="loose");if("loose"!==l&&"strict"!==l&&"stop-before-partial"!==l)throw new g("Incorrect `lastChunkHandling` option");i&&c(i.buffer);var m=i||[],b=0,v=0,w="",y=0;if(s)for(;;){if((y=skipAsciiWhitespace(t,y))===t.length){if(w.length>0){if("stop-before-partial"===l)break;if("loose"!==l)throw new p("Missing padding");if(1===w.length)throw new p("Malformed padding: exactly one additional character");b=writeBytes(m,decodeBase64Chunk(w,n,!1),b)}v=t.length;break}var A=f(t,y);++y;if("="===A){if(w.length<2)throw new p("Padding is too early");y=skipAsciiWhitespace(t,y);if(2===w.length){if(y===t.length){if("stop-before-partial"===l)break;throw new p("Malformed padding: only one =")}if("="===f(t,y)){++y;y=skipAsciiWhitespace(t,y)}}if(y<t.length)throw new p("Unexpected character after padding");b=writeBytes(m,decodeBase64Chunk(w,n,"strict"===l),b);v=t.length;break}if(!o(n,A))throw new p("Unexpected character");var _=s-b;if(1===_&&2===w.length||2===_&&3===w.length)break;if(4===(w+=A).length){b=writeBytes(m,decodeBase64Chunk(w,n,!1),b);w="";v=y;if(b===s)break}}return{bytes:m,read:v,written:b}}},9286:(t,e,i)=>{var s=i(4402),n=i(8469),r=s.Set,a=s.add;t.exports=function(t){var e=new r;n(t,(function(t){a(e,t)}));return e}},9297:(t,e,i)=>{var s=i(9504),n=i(8981),r=s({}.hasOwnProperty);t.exports=Object.hasOwn||function hasOwn(t,e){return r(n(t),e)}},9306:(t,e,i)=>{var s=i(4901),n=i(6823),r=TypeError;t.exports=function(t){if(s(t))return t;throw new r(n(t)+" is not a function")}},9314:(t,e,i)=>{var s=i(6518),n=i(9565),r=i(8551),a=i(1767),o=i(4149),l=i(9590),h=i(9462),c=i(6395),d=h((function(){for(var t,e=this.iterator,i=this.next;this.remaining;){this.remaining--;t=r(n(i,e));if(this.done=!!t.done)return}t=r(n(i,e));if(!(this.done=!!t.done))return t.value}));s({target:"Iterator",proto:!0,real:!0,forced:c},{drop:function drop(t){r(this);var e=l(o(+t));return new d(a(this),{remaining:e})}})},9429:(t,e,i)=>{var s=i(4576),n=i(6193);t.exports=function(t){if(n){try{return s.process.getBuiltinModule(t)}catch(t){}try{return Function('return require("'+t+'")')()}catch(t){}}}},9432:(t,e,i)=>{var s=i(6518),n=i(4576),r=i(5370),a=i(9143),o=n.Uint8Array;o&&s({target:"Uint8Array",stat:!0},{fromBase64:function fromBase64(t){var e=a(t,arguments.length>1?arguments[1]:void 0,null,9007199254740991);return r(o,e.bytes)}})},9433:(t,e,i)=>{var s=i(4576),n=Object.defineProperty;t.exports=function(t,e){try{n(s,t,{value:e,configurable:!0,writable:!0})}catch(i){s[t]=e}return e}},9462:(t,e,i)=>{var s=i(9565),n=i(2360),r=i(6699),a=i(6279),o=i(8227),l=i(1181),h=i(5966),c=i(7657).IteratorPrototype,d=i(2529),u=i(9539),p=o("toStringTag"),g="IteratorHelper",f="WrapForValidIterator",m=l.set,createIteratorProxyPrototype=function(t){var e=l.getterFor(t?f:g);return a(n(c),{next:function next(){var i=e(this);if(t)return i.nextHandler();if(i.done)return d(void 0,!0);try{var s=i.nextHandler();return i.returnHandlerResult?s:d(s,i.done)}catch(t){i.done=!0;throw t}},return:function(){var i=e(this),n=i.iterator;i.done=!0;if(t){var r=h(n,"return");return r?s(r,n):d(void 0,!0)}if(i.inner)try{u(i.inner.iterator,"normal")}catch(t){return u(n,"throw",t)}n&&u(n,"normal");return d(void 0,!0)}})},b=createIteratorProxyPrototype(!0),v=createIteratorProxyPrototype(!1);r(v,p,"Iterator Helper");t.exports=function(t,e,i){var s=function Iterator(s,n){if(n){n.iterator=s.iterator;n.next=s.next}else n=s;n.type=e?f:g;n.returnHandlerResult=!!i;n.nextHandler=t;n.counter=0;n.done=!1;m(this,n)};s.prototype=e?b:v;return s}},9504:(t,e,i)=>{var s=i(616),n=Function.prototype,r=n.call,a=s&&n.bind.bind(r,r);t.exports=s?a:function(t){return function(){return r.apply(t,arguments)}}},9519:(t,e,i)=>{var s,n,r=i(4576),a=i(2839),o=r.process,l=r.Deno,h=o&&o.versions||l&&l.version,c=h&&h.v8;c&&(n=(s=c.split("."))[0]>0&&s[0]<4?1:+(s[0]+s[1]));!n&&a&&(!(s=a.match(/Edge\/(\d+)/))||s[1]>=74)&&(s=a.match(/Chrome\/(\d+)/))&&(n=+s[1]);t.exports=n},9539:(t,e,i)=>{var s=i(9565),n=i(8551),r=i(5966);t.exports=function(t,e,i){var a,o;n(t);try{if(!(a=r(t,"return"))){if("throw"===e)throw i;return i}a=s(a,t)}catch(t){o=!0;a=t}if("throw"===e)throw i;if(o)throw a;n(a);return i}},9565:(t,e,i)=>{var s=i(616),n=Function.prototype.call;t.exports=s?n.bind(n):function(){return n.apply(n,arguments)}},9590:(t,e,i)=>{var s=i(1291),n=RangeError;t.exports=function(t){var e=s(t);if(e<0)throw new n("The argument can't be less than 0");return e}},9617:(t,e,i)=>{var s=i(5397),n=i(5610),r=i(6198),createMethod=function(t){return function(e,i,a){var o=s(e),l=r(o);if(0===l)return!t&&-1;var h,c=n(a,l);if(t&&i!=i){for(;l>c;)if((h=o[c++])!=h)return!0}else for(;l>c;c++)if((t||c in o)&&o[c]===i)return t||c||0;return!t&&-1}};t.exports={includes:createMethod(!0),indexOf:createMethod(!1)}},9631:(t,e,i)=>{var s=i(6518),n=i(4576),r=i(9504),a=i(3972),o=i(4154),l=i(5169),h=i(2804),c=i(944),d=h.i2c,u=h.i2cUrl,p=r("".charAt);n.Uint8Array&&s({target:"Uint8Array",proto:!0},{toBase64:function toBase64(){var t=o(this),e=arguments.length?a(arguments[0]):void 0,i="base64"===c(e)?d:u,s=!!e&&!!e.omitPadding;l(this.buffer);for(var n,r="",h=0,g=t.length,at=function(t){return p(i,n>>6*t&63)};h+2<g;h+=3){n=(t[h]<<16)+(t[h+1]<<8)+t[h+2];r+=at(3)+at(2)+at(1)+at(0)}if(h+2===g){n=(t[h]<<16)+(t[h+1]<<8);r+=at(3)+at(2)+at(1)+(s?"":"=")}else if(h+1===g){n=t[h]<<16;r+=at(3)+at(2)+(s?"":"==")}return r}})},9797:(t,e,i)=>{var s=i(6518),n=i(4576),r=i(3463),a=i(4154),o=i(5169),l=i(2303);n.Uint8Array&&s({target:"Uint8Array",proto:!0},{setFromHex:function setFromHex(t){a(this);r(t);o(this.buffer);var e=l(t,this).read;return{read:e,written:e/2}}})}},e={};function __webpack_require__(i){var s=e[i];if(void 0!==s)return s.exports;var n=e[i]={exports:{}};t[i].call(n.exports,n,n.exports,__webpack_require__);return n.exports}__webpack_require__(4114),__webpack_require__(6573),__webpack_require__(8100),__webpack_require__(7936),__webpack_require__(8111),__webpack_require__(8237),__webpack_require__(1689),__webpack_require__(9432),__webpack_require__(1549),__webpack_require__(9797),__webpack_require__(9631),__webpack_require__(5623),__webpack_require__(4979),__webpack_require__(5781);const i=!("object"!=typeof process||process+""!="[object process]"||process.versions.nw||process.versions.electron&&process.type&&"browser"!==process.type),s=[.001,0,0,.001,0,0],n=1.35,r=1,a=2,o=4,l=16,h=32,c=64,d=128,u=256,p={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3},g={DISABLE:-1,NONE:0,FREETEXT:3,HIGHLIGHT:9,STAMP:13,INK:15,SIGNATURE:101},f={RESIZE:1,CREATE:2,FREETEXT_SIZE:11,FREETEXT_COLOR:12,FREETEXT_OPACITY:13,INK_COLOR:21,INK_THICKNESS:22,INK_OPACITY:23,HIGHLIGHT_COLOR:31,HIGHLIGHT_DEFAULT_COLOR:32,HIGHLIGHT_THICKNESS:33,HIGHLIGHT_FREE:34,HIGHLIGHT_SHOW_ALL:35,DRAW_STEP:41},m={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048},b=0,v=1,w=2,y=3,A=3,_=4,x={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3},E={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26},S=1,C=2,T=3,D=4,M=5,P={ERRORS:0,WARNINGS:1,INFOS:5},k={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91,setStrokeTransparent:92,setFillTransparent:93,rawFillPath:94},I=0,R=1,L=2,O=3,N={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};let B=P.WARNINGS;function setVerbosityLevel(t){Number.isInteger(t)&&(B=t)}function getVerbosityLevel(){return B}function info(t){B>=P.INFOS&&console.log(`Info: ${t}`)}function warn(t){B>=P.WARNINGS&&console.log(`Warning: ${t}`)}function unreachable(t){throw new Error(t)}function assert(t,e){t||unreachable(e)}function createValidAbsoluteUrl(t,e=null,i=null){if(!t)return null;if(i&&"string"==typeof t){if(i.addDefaultProtocol&&t.startsWith("www.")){const e=t.match(/\./g);e?.length>=2&&(t=`http://${t}`)}if(i.tryConvertEncoding)try{t=function stringToUTF8String(t){return decodeURIComponent(escape(t))}(t)}catch{}}const s=e?URL.parse(t,e):URL.parse(t);return function _isValidProtocol(t){switch(t?.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}(s)?s:null}function updateUrlHash(t,e,i=!1){const s=URL.parse(t);if(s){s.hash=e;return s.href}return i&&createValidAbsoluteUrl(t,"http://example.com")?t.split("#",1)[0]+""+(e?`#${e}`:""):""}function shadow(t,e,i,s=!1){Object.defineProperty(t,e,{value:i,enumerable:!s,configurable:!0,writable:!1});return i}const U=function BaseExceptionClosure(){function BaseException(t,e){this.message=t;this.name=e}BaseException.prototype=new Error;BaseException.constructor=BaseException;return BaseException}();class PasswordException extends U{constructor(t,e){super(t,"PasswordException");this.code=e}}class UnknownErrorException extends U{constructor(t,e){super(t,"UnknownErrorException");this.details=e}}class InvalidPDFException extends U{constructor(t){super(t,"InvalidPDFException")}}class ResponseException extends U{constructor(t,e,i){super(t,"ResponseException");this.status=e;this.missing=i}}class FormatError extends U{constructor(t){super(t,"FormatError")}}class AbortException extends U{constructor(t){super(t,"AbortException")}}function bytesToString(t){"object"==typeof t&&void 0!==t?.length||unreachable("Invalid argument for bytesToString");const e=t.length,i=8192;if(e<i)return String.fromCharCode.apply(null,t);const s=[];for(let n=0;n<e;n+=i){const r=Math.min(n+i,e),a=t.subarray(n,r);s.push(String.fromCharCode.apply(null,a))}return s.join("")}function stringToBytes(t){"string"!=typeof t&&unreachable("Invalid argument for stringToBytes");const e=t.length,i=new Uint8Array(e);for(let s=0;s<e;++s)i[s]=255&t.charCodeAt(s);return i}class util_FeatureTest{static get isLittleEndian(){return shadow(this,"isLittleEndian",function isLittleEndian(){const t=new Uint8Array(4);t[0]=1;return 1===new Uint32Array(t.buffer,0,1)[0]}())}static get isEvalSupported(){return shadow(this,"isEvalSupported",function isEvalSupported(){try{new Function("");return!0}catch{return!1}}())}static get isOffscreenCanvasSupported(){return shadow(this,"isOffscreenCanvasSupported","undefined"!=typeof OffscreenCanvas)}static get isImageDecoderSupported(){return shadow(this,"isImageDecoderSupported","undefined"!=typeof ImageDecoder)}static get platform(){if("undefined"!=typeof navigator&&"string"==typeof navigator?.platform&&"string"==typeof navigator?.userAgent){const{platform:t,userAgent:e}=navigator;return shadow(this,"platform",{isAndroid:e.includes("Android"),isLinux:t.includes("Linux"),isMac:t.includes("Mac"),isWindows:t.includes("Win"),isFirefox:e.includes("Firefox")})}return shadow(this,"platform",{isAndroid:!1,isLinux:!1,isMac:!1,isWindows:!1,isFirefox:!1})}static get isCSSRoundSupported(){return shadow(this,"isCSSRoundSupported",globalThis.CSS?.supports?.("width: round(1.5px, 1px)"))}}const H=Array.from(Array(256).keys(),(t=>t.toString(16).padStart(2,"0")));class Util{static makeHexColor(t,e,i){return`#${H[t]}${H[e]}${H[i]}`}static scaleMinMax(t,e){let i;if(t[0]){if(t[0]<0){i=e[0];e[0]=e[2];e[2]=i}e[0]*=t[0];e[2]*=t[0];if(t[3]<0){i=e[1];e[1]=e[3];e[3]=i}e[1]*=t[3];e[3]*=t[3]}else{i=e[0];e[0]=e[1];e[1]=i;i=e[2];e[2]=e[3];e[3]=i;if(t[1]<0){i=e[1];e[1]=e[3];e[3]=i}e[1]*=t[1];e[3]*=t[1];if(t[2]<0){i=e[0];e[0]=e[2];e[2]=i}e[0]*=t[2];e[2]*=t[2]}e[0]+=t[4];e[1]+=t[5];e[2]+=t[4];e[3]+=t[5]}static transform(t,e){return[t[0]*e[0]+t[2]*e[1],t[1]*e[0]+t[3]*e[1],t[0]*e[2]+t[2]*e[3],t[1]*e[2]+t[3]*e[3],t[0]*e[4]+t[2]*e[5]+t[4],t[1]*e[4]+t[3]*e[5]+t[5]]}static applyTransform(t,e,i=0){const s=t[i],n=t[i+1];t[i]=s*e[0]+n*e[2]+e[4];t[i+1]=s*e[1]+n*e[3]+e[5]}static applyTransformToBezier(t,e,i=0){const s=e[0],n=e[1],r=e[2],a=e[3],o=e[4],l=e[5];for(let e=0;e<6;e+=2){const h=t[i+e],c=t[i+e+1];t[i+e]=h*s+c*r+o;t[i+e+1]=h*n+c*a+l}}static applyInverseTransform(t,e){const i=t[0],s=t[1],n=e[0]*e[3]-e[1]*e[2];t[0]=(i*e[3]-s*e[2]+e[2]*e[5]-e[4]*e[3])/n;t[1]=(-i*e[1]+s*e[0]+e[4]*e[1]-e[5]*e[0])/n}static axialAlignedBoundingBox(t,e,i){const s=e[0],n=e[1],r=e[2],a=e[3],o=e[4],l=e[5],h=t[0],c=t[1],d=t[2],u=t[3];let p=s*h+o,g=p,f=s*d+o,m=f,b=a*c+l,v=b,w=a*u+l,y=w;if(0!==n||0!==r){const t=n*h,e=n*d,i=r*c,s=r*u;p+=i;m+=i;f+=s;g+=s;b+=t;y+=t;w+=e;v+=e}i[0]=Math.min(i[0],p,f,g,m);i[1]=Math.min(i[1],b,w,v,y);i[2]=Math.max(i[2],p,f,g,m);i[3]=Math.max(i[3],b,w,v,y)}static inverseTransform(t){const e=t[0]*t[3]-t[1]*t[2];return[t[3]/e,-t[1]/e,-t[2]/e,t[0]/e,(t[2]*t[5]-t[4]*t[3])/e,(t[4]*t[1]-t[5]*t[0])/e]}static singularValueDecompose2dScale(t,e){const i=t[0],s=t[1],n=t[2],r=t[3],a=i**2+s**2,o=i*n+s*r,l=n**2+r**2,h=(a+l)/2,c=Math.sqrt(h**2-(a*l-o**2));e[0]=Math.sqrt(h+c||1);e[1]=Math.sqrt(h-c||1)}static normalizeRect(t){const e=t.slice(0);if(t[0]>t[2]){e[0]=t[2];e[2]=t[0]}if(t[1]>t[3]){e[1]=t[3];e[3]=t[1]}return e}static intersect(t,e){const i=Math.max(Math.min(t[0],t[2]),Math.min(e[0],e[2])),s=Math.min(Math.max(t[0],t[2]),Math.max(e[0],e[2]));if(i>s)return null;const n=Math.max(Math.min(t[1],t[3]),Math.min(e[1],e[3])),r=Math.min(Math.max(t[1],t[3]),Math.max(e[1],e[3]));return n>r?null:[i,n,s,r]}static pointBoundingBox(t,e,i){i[0]=Math.min(i[0],t);i[1]=Math.min(i[1],e);i[2]=Math.max(i[2],t);i[3]=Math.max(i[3],e)}static rectBoundingBox(t,e,i,s,n){n[0]=Math.min(n[0],t,i);n[1]=Math.min(n[1],e,s);n[2]=Math.max(n[2],t,i);n[3]=Math.max(n[3],e,s)}static#t(t,e,i,s,n,r,a,o,l,h){if(l<=0||l>=1)return;const c=1-l,d=l*l,u=d*l,p=c*(c*(c*t+3*l*e)+3*d*i)+u*s,g=c*(c*(c*n+3*l*r)+3*d*a)+u*o;h[0]=Math.min(h[0],p);h[1]=Math.min(h[1],g);h[2]=Math.max(h[2],p);h[3]=Math.max(h[3],g)}static#e(t,e,i,s,n,r,a,o,l,h,c,d){if(Math.abs(l)<1e-12){Math.abs(h)>=1e-12&&this.#t(t,e,i,s,n,r,a,o,-c/h,d);return}const u=h**2-4*c*l;if(u<0)return;const p=Math.sqrt(u),g=2*l;this.#t(t,e,i,s,n,r,a,o,(-h+p)/g,d);this.#t(t,e,i,s,n,r,a,o,(-h-p)/g,d)}static bezierBoundingBox(t,e,i,s,n,r,a,o,l){l[0]=Math.min(l[0],t,a);l[1]=Math.min(l[1],e,o);l[2]=Math.max(l[2],t,a);l[3]=Math.max(l[3],e,o);this.#e(t,i,n,a,e,s,r,o,3*(3*(i-n)-t+a),6*(t-2*i+n),3*(i-t),l);this.#e(t,i,n,a,e,s,r,o,3*(3*(s-r)-e+o),6*(e-2*s+r),3*(s-e),l)}}let z=null,j=null;function normalizeUnicode(t){if(!z){z=/([\u00a0\u00b5\u037e\u0eb3\u2000-\u200a\u202f\u2126\ufb00-\ufb04\ufb06\ufb20-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufba1\ufba4-\ufba9\ufbae-\ufbb1\ufbd3-\ufbdc\ufbde-\ufbe7\ufbea-\ufbf8\ufbfc-\ufbfd\ufc00-\ufc5d\ufc64-\ufcf1\ufcf5-\ufd3d\ufd88\ufdf4\ufdfa-\ufdfb\ufe71\ufe77\ufe79\ufe7b\ufe7d]+)|(\ufb05+)/gu;j=new Map([["ﬅ","ſt"]])}return t.replaceAll(z,((t,e,i)=>e?e.normalize("NFKC"):j.get(i)))}function getUuid(){if("function"==typeof crypto.randomUUID)return crypto.randomUUID();const t=new Uint8Array(32);crypto.getRandomValues(t);return bytesToString(t)}const G="pdfjs_internal_id_";function MathClamp(t,e,i){return Math.min(Math.max(t,e),i)}function toBase64Util(t){return Uint8Array.prototype.toBase64?t.toBase64():btoa(bytesToString(t))}"function"!=typeof Math.sumPrecise&&(Math.sumPrecise=function(t){return t.reduce(((t,e)=>t+e),0)});"function"!=typeof AbortSignal.any&&(AbortSignal.any=function(t){const e=new AbortController,{signal:i}=e;for(const s of t)if(s.aborted){e.abort(s.reason);return i}for(const s of t)s.addEventListener("abort",(()=>{e.abort(s.reason)}),{signal:i});return i});__webpack_require__(1701),__webpack_require__(4628),__webpack_require__(7642),__webpack_require__(8004),__webpack_require__(3853),__webpack_require__(5876),__webpack_require__(2475),__webpack_require__(5024),__webpack_require__(1698),__webpack_require__(4603),__webpack_require__(7566),__webpack_require__(8721),__webpack_require__(9314),__webpack_require__(1148),__webpack_require__(3579),__webpack_require__(8335);const W="http://www.w3.org/2000/svg";class PixelsPerInch{static CSS=96;static PDF=72;static PDF_TO_CSS_UNITS=this.CSS/this.PDF}async function fetchData(t,e="text"){if(isValidFetchUrl(t,document.baseURI)){const i=await fetch(t);if(!i.ok)throw new Error(i.statusText);switch(e){case"arraybuffer":return i.arrayBuffer();case"blob":return i.blob();case"json":return i.json()}return i.text()}return new Promise(((i,s)=>{const n=new XMLHttpRequest;n.open("GET",t,!0);n.responseType=e;n.onreadystatechange=()=>{if(n.readyState===XMLHttpRequest.DONE)if(200!==n.status&&0!==n.status)s(new Error(n.statusText));else{switch(e){case"arraybuffer":case"blob":case"json":i(n.response);return}i(n.responseText)}};n.send(null)}))}class PageViewport{constructor({viewBox:t,userUnit:e,scale:i,rotation:s,offsetX:n=0,offsetY:r=0,dontFlip:a=!1}){this.viewBox=t;this.userUnit=e;this.scale=i;this.rotation=s;this.offsetX=n;this.offsetY=r;i*=e;const o=(t[2]+t[0])/2,l=(t[3]+t[1])/2;let h,c,d,u,p,g,f,m;(s%=360)<0&&(s+=360);switch(s){case 180:h=-1;c=0;d=0;u=1;break;case 90:h=0;c=1;d=1;u=0;break;case 270:h=0;c=-1;d=-1;u=0;break;case 0:h=1;c=0;d=0;u=-1;break;default:throw new Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}if(a){d=-d;u=-u}if(0===h){p=Math.abs(l-t[1])*i+n;g=Math.abs(o-t[0])*i+r;f=(t[3]-t[1])*i;m=(t[2]-t[0])*i}else{p=Math.abs(o-t[0])*i+n;g=Math.abs(l-t[1])*i+r;f=(t[2]-t[0])*i;m=(t[3]-t[1])*i}this.transform=[h*i,c*i,d*i,u*i,p-h*i*o-d*i*l,g-c*i*o-u*i*l];this.width=f;this.height=m}get rawDims(){const t=this.viewBox;return shadow(this,"rawDims",{pageWidth:t[2]-t[0],pageHeight:t[3]-t[1],pageX:t[0],pageY:t[1]})}clone({scale:t=this.scale,rotation:e=this.rotation,offsetX:i=this.offsetX,offsetY:s=this.offsetY,dontFlip:n=!1}={}){return new PageViewport({viewBox:this.viewBox.slice(),userUnit:this.userUnit,scale:t,rotation:e,offsetX:i,offsetY:s,dontFlip:n})}convertToViewportPoint(t,e){const i=[t,e];Util.applyTransform(i,this.transform);return i}convertToViewportRectangle(t){const e=[t[0],t[1]];Util.applyTransform(e,this.transform);const i=[t[2],t[3]];Util.applyTransform(i,this.transform);return[e[0],e[1],i[0],i[1]]}convertToPdfPoint(t,e){const i=[t,e];Util.applyInverseTransform(i,this.transform);return i}}class RenderingCancelledException extends U{constructor(t,e=0){super(t,"RenderingCancelledException");this.extraDelay=e}}function isDataScheme(t){const e=t.length;let i=0;for(;i<e&&""===t[i].trim();)i++;return"data:"===t.substring(i,i+5).toLowerCase()}function isPdfFile(t){return"string"==typeof t&&/\.pdf$/i.test(t)}function getFilenameFromUrl(t){[t]=t.split(/[#?]/,1);return t.substring(t.lastIndexOf("/")+1)}function getPdfFilenameFromUrl(t,e="document.pdf"){if("string"!=typeof t)return e;if(isDataScheme(t)){warn('getPdfFilenameFromUrl: ignore "data:"-URL for performance reasons.');return e}const i=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i,s=/^(?:(?:[^:]+:)?\/\/[^/]+)?([^?#]*)(\?[^#]*)?(#.*)?$/.exec(t);let n=i.exec(s[1])||i.exec(s[2])||i.exec(s[3]);if(n){n=n[0];if(n.includes("%"))try{n=i.exec(decodeURIComponent(n))[0]}catch{}}return n||e}class StatTimer{started=Object.create(null);times=[];time(t){t in this.started&&warn(`Timer is already running for ${t}`);this.started[t]=Date.now()}timeEnd(t){t in this.started||warn(`Timer has not been started for ${t}`);this.times.push({name:t,start:this.started[t],end:Date.now()});delete this.started[t]}toString(){const t=[];let e=0;for(const{name:t}of this.times)e=Math.max(t.length,e);for(const{name:i,start:s,end:n}of this.times)t.push(`${i.padEnd(e)} ${n-s}ms\n`);return t.join("")}}function isValidFetchUrl(t,e){const i=e?URL.parse(t,e):URL.parse(t);return"http:"===i?.protocol||"https:"===i?.protocol}function noContextMenu(t){t.preventDefault()}function stopEvent(t){t.preventDefault();t.stopPropagation()}class PDFDateString{static#i;static toDateObject(t){if(!t||"string"!=typeof t)return null;this.#i||=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?");const e=this.#i.exec(t);if(!e)return null;const i=parseInt(e[1],10);let s=parseInt(e[2],10);s=s>=1&&s<=12?s-1:0;let n=parseInt(e[3],10);n=n>=1&&n<=31?n:1;let r=parseInt(e[4],10);r=r>=0&&r<=23?r:0;let a=parseInt(e[5],10);a=a>=0&&a<=59?a:0;let o=parseInt(e[6],10);o=o>=0&&o<=59?o:0;const l=e[7]||"Z";let h=parseInt(e[8],10);h=h>=0&&h<=23?h:0;let c=parseInt(e[9],10)||0;c=c>=0&&c<=59?c:0;if("-"===l){r+=h;a+=c}else if("+"===l){r-=h;a-=c}return new Date(Date.UTC(i,s,n,r,a,o))}}function getXfaPageViewport(t,{scale:e=1,rotation:i=0}){const{width:s,height:n}=t.attributes.style,r=[0,0,parseInt(s),parseInt(n)];return new PageViewport({viewBox:r,userUnit:1,scale:e,rotation:i})}function getRGB(t){if(t.startsWith("#")){const e=parseInt(t.slice(1),16);return[(16711680&e)>>16,(65280&e)>>8,255&e]}if(t.startsWith("rgb("))return t.slice(4,-1).split(",").map((t=>parseInt(t)));if(t.startsWith("rgba("))return t.slice(5,-1).split(",").map((t=>parseInt(t))).slice(0,3);warn(`Not a valid color format: "${t}"`);return[0,0,0]}function getCurrentTransform(t){const{a:e,b:i,c:s,d:n,e:r,f:a}=t.getTransform();return[e,i,s,n,r,a]}function getCurrentTransformInverse(t){const{a:e,b:i,c:s,d:n,e:r,f:a}=t.getTransform().invertSelf();return[e,i,s,n,r,a]}function setLayerDimensions(t,e,i=!1,s=!0){if(e instanceof PageViewport){const{pageWidth:s,pageHeight:n}=e.rawDims,{style:r}=t,a=util_FeatureTest.isCSSRoundSupported,o=`var(--total-scale-factor) * ${s}px`,l=`var(--total-scale-factor) * ${n}px`,h=a?`round(down, ${o}, var(--scale-round-x))`:`calc(${o})`,c=a?`round(down, ${l}, var(--scale-round-y))`:`calc(${l})`;if(i&&e.rotation%180!=0){r.width=c;r.height=h}else{r.width=h;r.height=c}}s&&t.setAttribute("data-main-rotation",e.rotation)}class OutputScale{constructor(){const{pixelRatio:t}=OutputScale;this.sx=t;this.sy=t}get scaled(){return 1!==this.sx||1!==this.sy}get symmetric(){return this.sx===this.sy}limitCanvas(t,e,i,s){let n=1/0,r=1/0,a=1/0;i>0&&(n=Math.sqrt(i/(t*e)));if(-1!==s){r=s/t;a=s/e}const o=Math.min(n,r,a);if(this.sx>o||this.sy>o){this.sx=o;this.sy=o;return!0}return!1}static get pixelRatio(){return globalThis.devicePixelRatio||1}}const $=["image/apng","image/avif","image/bmp","image/gif","image/jpeg","image/png","image/svg+xml","image/webp","image/x-icon"];class EditorToolbar{#s=null;#n=null;#r;#a=null;#o=null;#l=null;static#h=null;constructor(t){this.#r=t;EditorToolbar.#h||=Object.freeze({freetext:"pdfjs-editor-remove-freetext-button",highlight:"pdfjs-editor-remove-highlight-button",ink:"pdfjs-editor-remove-ink-button",stamp:"pdfjs-editor-remove-stamp-button",signature:"pdfjs-editor-remove-signature-button"})}render(){const t=this.#s=document.createElement("div");t.classList.add("editToolbar","hidden");t.setAttribute("role","toolbar");const e=this.#r._uiManager._signal;t.addEventListener("contextmenu",noContextMenu,{signal:e});t.addEventListener("pointerdown",EditorToolbar.#c,{signal:e});const i=this.#a=document.createElement("div");i.className="buttons";t.append(i);const s=this.#r.toolbarPosition;if(s){const{style:e}=t,i="ltr"===this.#r._uiManager.direction?1-s[0]:s[0];e.insetInlineEnd=100*i+"%";e.top=`calc(${100*s[1]}% + var(--editor-toolbar-vert-offset))`}this.#d();return t}get div(){return this.#s}static#c(t){t.stopPropagation()}#u(t){this.#r._focusEventsAllowed=!1;stopEvent(t)}#p(t){this.#r._focusEventsAllowed=!0;stopEvent(t)}#g(t){const e=this.#r._uiManager._signal;t.addEventListener("focusin",this.#u.bind(this),{capture:!0,signal:e});t.addEventListener("focusout",this.#p.bind(this),{capture:!0,signal:e});t.addEventListener("contextmenu",noContextMenu,{signal:e})}hide(){this.#s.classList.add("hidden");this.#n?.hideDropdown()}show(){this.#s.classList.remove("hidden");this.#o?.shown()}#d(){const{editorType:t,_uiManager:e}=this.#r,i=document.createElement("button");i.className="delete";i.tabIndex=0;i.setAttribute("data-l10n-id",EditorToolbar.#h[t]);this.#g(i);i.addEventListener("click",(t=>{e.delete()}),{signal:e._signal});this.#a.append(i)}get#f(){const t=document.createElement("div");t.className="divider";return t}async addAltText(t){const e=await t.render();this.#g(e);this.#a.prepend(e,this.#f);this.#o=t}addColorPicker(t){this.#n=t;const e=t.renderButton();this.#g(e);this.#a.prepend(e,this.#f)}async addEditSignatureButton(t){const e=this.#l=await t.renderEditButton(this.#r);this.#g(e);this.#a.prepend(e,this.#f)}updateEditSignatureButton(t){this.#l&&(this.#l.title=t)}remove(){this.#s.remove();this.#n?.destroy();this.#n=null}}class HighlightToolbar{#a=null;#s=null;#m;constructor(t){this.#m=t}#b(){const t=this.#s=document.createElement("div");t.className="editToolbar";t.setAttribute("role","toolbar");t.addEventListener("contextmenu",noContextMenu,{signal:this.#m._signal});const e=this.#a=document.createElement("div");e.className="buttons";t.append(e);this.#v();return t}#w(t,e){let i=0,s=0;for(const n of t){const t=n.y+n.height;if(t<i)continue;const r=n.x+(e?n.width:0);if(t>i){s=r;i=t}else e?r>s&&(s=r):r<s&&(s=r)}return[e?1-s:s,i]}show(t,e,i){const[s,n]=this.#w(e,i),{style:r}=this.#s||=this.#b();t.append(this.#s);r.insetInlineEnd=100*s+"%";r.top=`calc(${100*n}% + var(--editor-toolbar-vert-offset))`}hide(){this.#s.remove()}#v(){const t=document.createElement("button");t.className="highlightButton";t.tabIndex=0;t.setAttribute("data-l10n-id","pdfjs-highlight-floating-button1");const e=document.createElement("span");t.append(e);e.className="visuallyHidden";e.setAttribute("data-l10n-id","pdfjs-highlight-floating-button-label");const i=this.#m._signal;t.addEventListener("contextmenu",noContextMenu,{signal:i});t.addEventListener("click",(()=>{this.#m.highlightSelection("floating_button")}),{signal:i});this.#a.append(t)}}function bindEvents(t,e,i){for(const s of i)e.addEventListener(s,t[s].bind(t))}class IdManager{#y=0;get id(){return"pdfjs_internal_editor_"+this.#y++}}class ImageManager{#A=getUuid();#y=0;#_=null;static get _isSVGFittingCanvas(){const t=new OffscreenCanvas(1,3).getContext("2d",{willReadFrequently:!0}),e=new Image;e.src='data:image/svg+xml;charset=UTF-8,<svg viewBox="0 0 1 1" width="1" height="1" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" style="fill:red;"/></svg>';return shadow(this,"_isSVGFittingCanvas",e.decode().then((()=>{t.drawImage(e,0,0,1,1,0,0,1,3);return 0===new Uint32Array(t.getImageData(0,0,1,1).data.buffer)[0]})))}async#x(t,e){this.#_||=new Map;let i=this.#_.get(t);if(null===i)return null;if(i?.bitmap){i.refCounter+=1;return i}try{i||={bitmap:null,id:`image_${this.#A}_${this.#y++}`,refCounter:0,isSvg:!1};let t;if("string"==typeof e){i.url=e;t=await fetchData(e,"blob")}else e instanceof File?t=i.file=e:e instanceof Blob&&(t=e);if("image/svg+xml"===t.type){const e=ImageManager._isSVGFittingCanvas,s=new FileReader,n=new Image,r=new Promise(((t,r)=>{n.onload=()=>{i.bitmap=n;i.isSvg=!0;t()};s.onload=async()=>{const t=i.svgUrl=s.result;n.src=await e?`${t}#svgView(preserveAspectRatio(none))`:t};n.onerror=s.onerror=r}));s.readAsDataURL(t);await r}else i.bitmap=await createImageBitmap(t);i.refCounter=1}catch(t){warn(t);i=null}this.#_.set(t,i);i&&this.#_.set(i.id,i);return i}async getFromFile(t){const{lastModified:e,name:i,size:s,type:n}=t;return this.#x(`${e}_${i}_${s}_${n}`,t)}async getFromUrl(t){return this.#x(t,t)}async getFromBlob(t,e){const i=await e;return this.#x(t,i)}async getFromId(t){this.#_||=new Map;const e=this.#_.get(t);if(!e)return null;if(e.bitmap){e.refCounter+=1;return e}if(e.file)return this.getFromFile(e.file);if(e.blobPromise){const{blobPromise:t}=e;delete e.blobPromise;return this.getFromBlob(e.id,t)}return this.getFromUrl(e.url)}getFromCanvas(t,e){this.#_||=new Map;let i=this.#_.get(t);if(i?.bitmap){i.refCounter+=1;return i}const s=new OffscreenCanvas(e.width,e.height);s.getContext("2d").drawImage(e,0,0);i={bitmap:s.transferToImageBitmap(),id:`image_${this.#A}_${this.#y++}`,refCounter:1,isSvg:!1};this.#_.set(t,i);this.#_.set(i.id,i);return i}getSvgUrl(t){const e=this.#_.get(t);return e?.isSvg?e.svgUrl:null}deleteId(t){this.#_||=new Map;const e=this.#_.get(t);if(!e)return;e.refCounter-=1;if(0!==e.refCounter)return;const{bitmap:i}=e;if(!e.url&&!e.file){const t=new OffscreenCanvas(i.width,i.height);t.getContext("bitmaprenderer").transferFromImageBitmap(i);e.blobPromise=t.convertToBlob()}i.close?.();e.bitmap=null}isValidId(t){return t.startsWith(`image_${this.#A}_`)}}class CommandManager{#E=[];#S=!1;#C;#T=-1;constructor(t=128){this.#C=t}add({cmd:t,undo:e,post:i,mustExec:s,type:n=NaN,overwriteIfSameType:r=!1,keepUndo:a=!1}){s&&t();if(this.#S)return;const o={cmd:t,undo:e,post:i,type:n};if(-1===this.#T){this.#E.length>0&&(this.#E.length=0);this.#T=0;this.#E.push(o);return}if(r&&this.#E[this.#T].type===n){a&&(o.undo=this.#E[this.#T].undo);this.#E[this.#T]=o;return}const l=this.#T+1;if(l===this.#C)this.#E.splice(0,1);else{this.#T=l;l<this.#E.length&&this.#E.splice(l)}this.#E.push(o)}undo(){if(-1===this.#T)return;this.#S=!0;const{undo:t,post:e}=this.#E[this.#T];t();e?.();this.#S=!1;this.#T-=1}redo(){if(this.#T<this.#E.length-1){this.#T+=1;this.#S=!0;const{cmd:t,post:e}=this.#E[this.#T];t();e?.();this.#S=!1}}hasSomethingToUndo(){return-1!==this.#T}hasSomethingToRedo(){return this.#T<this.#E.length-1}cleanType(t){if(-1!==this.#T){for(let e=this.#T;e>=0;e--)if(this.#E[e].type!==t){this.#E.splice(e+1,this.#T-e);this.#T=e;return}this.#E.length=0;this.#T=-1}}destroy(){this.#E=null}}class KeyboardManager{constructor(t){this.buffer=[];this.callbacks=new Map;this.allKeys=new Set;const{isMac:e}=util_FeatureTest.platform;for(const[i,s,n={}]of t)for(const t of i){const i=t.startsWith("mac+");if(e&&i){this.callbacks.set(t.slice(4),{callback:s,options:n});this.allKeys.add(t.split("+").at(-1))}else if(!e&&!i){this.callbacks.set(t,{callback:s,options:n});this.allKeys.add(t.split("+").at(-1))}}}#D(t){t.altKey&&this.buffer.push("alt");t.ctrlKey&&this.buffer.push("ctrl");t.metaKey&&this.buffer.push("meta");t.shiftKey&&this.buffer.push("shift");this.buffer.push(t.key);const e=this.buffer.join("+");this.buffer.length=0;return e}exec(t,e){if(!this.allKeys.has(e.key))return;const i=this.callbacks.get(this.#D(e));if(!i)return;const{callback:s,options:{bubbles:n=!1,args:r=[],checker:a=null}}=i;if(!a||a(t,e)){s.bind(t,...r,e)();n||stopEvent(e)}}}class ColorManager{static _colorsMapping=new Map([["CanvasText",[0,0,0]],["Canvas",[255,255,255]]]);get _colors(){const t=new Map([["CanvasText",null],["Canvas",null]]);!function getColorValues(t){const e=document.createElement("span");e.style.visibility="hidden";e.style.colorScheme="only light";document.body.append(e);for(const i of t.keys()){e.style.color=i;const s=window.getComputedStyle(e).color;t.set(i,getRGB(s))}e.remove()}(t);return shadow(this,"_colors",t)}convert(t){const e=getRGB(t);if(!window.matchMedia("(forced-colors: active)").matches)return e;for(const[t,i]of this._colors)if(i.every(((t,i)=>t===e[i])))return ColorManager._colorsMapping.get(t);return e}getHexCode(t){const e=this._colors.get(t);return e?Util.makeHexColor(...e):t}}class AnnotationEditorUIManager{#M=new AbortController;#P=null;#k=new Map;#I=new Map;#R=null;#F=null;#L=null;#O=new CommandManager;#N=null;#B=null;#U=0;#H=new Set;#z=null;#j=null;#G=new Set;_editorUndoBar=null;#W=!1;#$=!1;#V=!1;#q=null;#X=null;#K=null;#Y=null;#Q=!1;#J=null;#Z=new IdManager;#tt=!1;#et=!1;#it=null;#st=null;#nt=null;#rt=null;#at=null;#ot=g.NONE;#lt=new Set;#ht=null;#ct=null;#dt=null;#ut=null;#pt={isEditing:!1,isEmpty:!0,hasSomethingToUndo:!1,hasSomethingToRedo:!1,hasSelectedEditor:!1,hasSelectedText:!1};#gt=[0,0];#ft=null;#mt=null;#bt=null;#vt=null;static TRANSLATE_SMALL=1;static TRANSLATE_BIG=10;static get _keyboardManager(){const t=AnnotationEditorUIManager.prototype,arrowChecker=t=>t.#mt.contains(document.activeElement)&&"BUTTON"!==document.activeElement.tagName&&t.hasSomethingToControl(),textInputChecker=(t,{target:e})=>{if(e instanceof HTMLInputElement){const{type:t}=e;return"text"!==t&&"number"!==t}return!0},e=this.TRANSLATE_SMALL,i=this.TRANSLATE_BIG;return shadow(this,"_keyboardManager",new KeyboardManager([[["ctrl+a","mac+meta+a"],t.selectAll,{checker:textInputChecker}],[["ctrl+z","mac+meta+z"],t.undo,{checker:textInputChecker}],[["ctrl+y","ctrl+shift+z","mac+meta+shift+z","ctrl+shift+Z","mac+meta+shift+Z"],t.redo,{checker:textInputChecker}],[["Backspace","alt+Backspace","ctrl+Backspace","shift+Backspace","mac+Backspace","mac+alt+Backspace","mac+ctrl+Backspace","Delete","ctrl+Delete","shift+Delete","mac+Delete"],t.delete,{checker:textInputChecker}],[["Enter","mac+Enter"],t.addNewEditorFromKeyboard,{checker:(t,{target:e})=>!(e instanceof HTMLButtonElement)&&t.#mt.contains(e)&&!t.isEnterHandled}],[[" ","mac+ "],t.addNewEditorFromKeyboard,{checker:(t,{target:e})=>!(e instanceof HTMLButtonElement)&&t.#mt.contains(document.activeElement)}],[["Escape","mac+Escape"],t.unselectAll],[["ArrowLeft","mac+ArrowLeft"],t.translateSelectedEditors,{args:[-e,0],checker:arrowChecker}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t.translateSelectedEditors,{args:[-i,0],checker:arrowChecker}],[["ArrowRight","mac+ArrowRight"],t.translateSelectedEditors,{args:[e,0],checker:arrowChecker}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t.translateSelectedEditors,{args:[i,0],checker:arrowChecker}],[["ArrowUp","mac+ArrowUp"],t.translateSelectedEditors,{args:[0,-e],checker:arrowChecker}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t.translateSelectedEditors,{args:[0,-i],checker:arrowChecker}],[["ArrowDown","mac+ArrowDown"],t.translateSelectedEditors,{args:[0,e],checker:arrowChecker}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t.translateSelectedEditors,{args:[0,i],checker:arrowChecker}]]))}constructor(t,e,i,s,n,r,a,o,l,h,c,d,u,p){const g=this._signal=this.#M.signal;this.#mt=t;this.#bt=e;this.#R=i;this.#ct=s;this._eventBus=n;n._on("editingaction",this.onEditingAction.bind(this),{signal:g});n._on("pagechanging",this.onPageChanging.bind(this),{signal:g});n._on("scalechanging",this.onScaleChanging.bind(this),{signal:g});n._on("rotationchanging",this.onRotationChanging.bind(this),{signal:g});n._on("setpreference",this.onSetPreference.bind(this),{signal:g});n._on("switchannotationeditorparams",(t=>this.updateParams(t.type,t.value)),{signal:g});this.#wt();this.#yt();this.#At();this.#F=r.annotationStorage;this.#q=r.filterFactory;this.#dt=a;this.#Y=o||null;this.#W=l;this.#$=h;this.#V=c;this.#at=d||null;this.viewParameters={realScale:PixelsPerInch.PDF_TO_CSS_UNITS,rotation:0};this.isShiftKeyDown=!1;this._editorUndoBar=u||null;this._supportsPinchToZoom=!1!==p}destroy(){this.#vt?.resolve();this.#vt=null;this.#M?.abort();this.#M=null;this._signal=null;for(const t of this.#I.values())t.destroy();this.#I.clear();this.#k.clear();this.#G.clear();this.#rt?.clear();this.#P=null;this.#lt.clear();this.#O.destroy();this.#R?.destroy();this.#ct?.destroy();this.#J?.hide();this.#J=null;this.#nt?.destroy();this.#nt=null;if(this.#X){clearTimeout(this.#X);this.#X=null}if(this.#ft){clearTimeout(this.#ft);this.#ft=null}this._editorUndoBar?.destroy()}combinedSignal(t){return AbortSignal.any([this._signal,t.signal])}get mlManager(){return this.#at}get useNewAltTextFlow(){return this.#$}get useNewAltTextWhenAddingImage(){return this.#V}get hcmFilter(){return shadow(this,"hcmFilter",this.#dt?this.#q.addHCMFilter(this.#dt.foreground,this.#dt.background):"none")}get direction(){return shadow(this,"direction",getComputedStyle(this.#mt).direction)}get highlightColors(){return shadow(this,"highlightColors",this.#Y?new Map(this.#Y.split(",").map((t=>t.split("=").map((t=>t.trim()))))):null)}get highlightColorNames(){return shadow(this,"highlightColorNames",this.highlightColors?new Map(Array.from(this.highlightColors,(t=>t.reverse()))):null)}setCurrentDrawingSession(t){if(t){this.unselectAll();this.disableUserSelect(!0)}else this.disableUserSelect(!1);this.#B=t}setMainHighlightColorPicker(t){this.#nt=t}editAltText(t,e=!1){this.#R?.editAltText(this,t,e)}getSignature(t){this.#ct?.getSignature({uiManager:this,editor:t})}get signatureManager(){return this.#ct}switchToMode(t,e){this._eventBus.on("annotationeditormodechanged",e,{once:!0,signal:this._signal});this._eventBus.dispatch("showannotationeditorui",{source:this,mode:t})}setPreference(t,e){this._eventBus.dispatch("setpreference",{source:this,name:t,value:e})}onSetPreference({name:t,value:e}){if("enableNewAltTextWhenAddingImage"===t)this.#V=e}onPageChanging({pageNumber:t}){this.#U=t-1}focusMainContainer(){this.#mt.focus()}findParent(t,e){for(const i of this.#I.values()){const{x:s,y:n,width:r,height:a}=i.div.getBoundingClientRect();if(t>=s&&t<=s+r&&e>=n&&e<=n+a)return i}return null}disableUserSelect(t=!1){this.#bt.classList.toggle("noUserSelect",t)}addShouldRescale(t){this.#G.add(t)}removeShouldRescale(t){this.#G.delete(t)}onScaleChanging({scale:t}){this.commitOrRemove();this.viewParameters.realScale=t*PixelsPerInch.PDF_TO_CSS_UNITS;for(const t of this.#G)t.onScaleChanging();this.#B?.onScaleChanging()}onRotationChanging({pagesRotation:t}){this.commitOrRemove();this.viewParameters.rotation=t}#_t({anchorNode:t}){return t.nodeType===Node.TEXT_NODE?t.parentElement:t}#xt(t){const{currentLayer:e}=this;if(e.hasTextLayer(t))return e;for(const e of this.#I.values())if(e.hasTextLayer(t))return e;return null}highlightSelection(t=""){const e=document.getSelection();if(!e||e.isCollapsed)return;const{anchorNode:i,anchorOffset:s,focusNode:n,focusOffset:r}=e,a=e.toString(),o=this.#_t(e).closest(".textLayer"),l=this.getSelectionBoxes(o);if(!l)return;e.empty();const h=this.#xt(o),c=this.#ot===g.NONE,callback=()=>{h?.createAndAddNewEditor({x:0,y:0},!1,{methodOfCreation:t,boxes:l,anchorNode:i,anchorOffset:s,focusNode:n,focusOffset:r,text:a});c&&this.showAllEditors("highlight",!0,!0)};c?this.switchToMode(g.HIGHLIGHT,callback):callback()}#Et(){const t=document.getSelection();if(!t||t.isCollapsed)return;const e=this.#_t(t).closest(".textLayer"),i=this.getSelectionBoxes(e);if(i){this.#J||=new HighlightToolbar(this);this.#J.show(e,i,"ltr"===this.direction)}}addToAnnotationStorage(t){t.isEmpty()||!this.#F||this.#F.has(t.id)||this.#F.setValue(t.id,t)}#St(){const t=document.getSelection();if(!t||t.isCollapsed){if(this.#ht){this.#J?.hide();this.#ht=null;this.#Ct({hasSelectedText:!1})}return}const{anchorNode:e}=t;if(e===this.#ht)return;const i=this.#_t(t).closest(".textLayer");if(i){this.#J?.hide();this.#ht=e;this.#Ct({hasSelectedText:!0});if(this.#ot===g.HIGHLIGHT||this.#ot===g.NONE){this.#ot===g.HIGHLIGHT&&this.showAllEditors("highlight",!0,!0);this.#Q=this.isShiftKeyDown;if(!this.isShiftKeyDown){const t=this.#ot===g.HIGHLIGHT?this.#xt(i):null;t?.toggleDrawing();const e=new AbortController,s=this.combinedSignal(e),pointerup=i=>{if("pointerup"!==i.type||0===i.button){e.abort();t?.toggleDrawing(!0);"pointerup"===i.type&&this.#Tt("main_toolbar")}};window.addEventListener("pointerup",pointerup,{signal:s});window.addEventListener("blur",pointerup,{signal:s})}}}else if(this.#ht){this.#J?.hide();this.#ht=null;this.#Ct({hasSelectedText:!1})}}#Tt(t=""){this.#ot===g.HIGHLIGHT?this.highlightSelection(t):this.#W&&this.#Et()}#wt(){document.addEventListener("selectionchange",this.#St.bind(this),{signal:this._signal})}#Dt(){if(this.#K)return;this.#K=new AbortController;const t=this.combinedSignal(this.#K);window.addEventListener("focus",this.focus.bind(this),{signal:t});window.addEventListener("blur",this.blur.bind(this),{signal:t})}#Mt(){this.#K?.abort();this.#K=null}blur(){this.isShiftKeyDown=!1;if(this.#Q){this.#Q=!1;this.#Tt("main_toolbar")}if(!this.hasSelection)return;const{activeElement:t}=document;for(const e of this.#lt)if(e.div.contains(t)){this.#st=[e,t];e._focusEventsAllowed=!1;break}}focus(){if(!this.#st)return;const[t,e]=this.#st;this.#st=null;e.addEventListener("focusin",(()=>{t._focusEventsAllowed=!0}),{once:!0,signal:this._signal});e.focus()}#At(){if(this.#it)return;this.#it=new AbortController;const t=this.combinedSignal(this.#it);window.addEventListener("keydown",this.keydown.bind(this),{signal:t});window.addEventListener("keyup",this.keyup.bind(this),{signal:t})}#Pt(){this.#it?.abort();this.#it=null}#kt(){if(this.#N)return;this.#N=new AbortController;const t=this.combinedSignal(this.#N);document.addEventListener("copy",this.copy.bind(this),{signal:t});document.addEventListener("cut",this.cut.bind(this),{signal:t});document.addEventListener("paste",this.paste.bind(this),{signal:t})}#It(){this.#N?.abort();this.#N=null}#yt(){const t=this._signal;document.addEventListener("dragover",this.dragOver.bind(this),{signal:t});document.addEventListener("drop",this.drop.bind(this),{signal:t})}addEditListeners(){this.#At();this.#kt()}removeEditListeners(){this.#Pt();this.#It()}dragOver(t){for(const{type:e}of t.dataTransfer.items)for(const i of this.#j)if(i.isHandlingMimeForPasting(e)){t.dataTransfer.dropEffect="copy";t.preventDefault();return}}drop(t){for(const e of t.dataTransfer.items)for(const i of this.#j)if(i.isHandlingMimeForPasting(e.type)){i.paste(e,this.currentLayer);t.preventDefault();return}}copy(t){t.preventDefault();this.#P?.commitOrRemove();if(!this.hasSelection)return;const e=[];for(const t of this.#lt){const i=t.serialize(!0);i&&e.push(i)}0!==e.length&&t.clipboardData.setData("application/pdfjs",JSON.stringify(e))}cut(t){this.copy(t);this.delete()}async paste(t){t.preventDefault();const{clipboardData:e}=t;for(const t of e.items)for(const e of this.#j)if(e.isHandlingMimeForPasting(t.type)){e.paste(t,this.currentLayer);return}let i=e.getData("application/pdfjs");if(!i)return;try{i=JSON.parse(i)}catch(t){warn(`paste: "${t.message}".`);return}if(!Array.isArray(i))return;this.unselectAll();const s=this.currentLayer;try{const t=[];for(const e of i){const i=await s.deserialize(e);if(!i)return;t.push(i)}const cmd=()=>{for(const e of t)this.#Rt(e);this.#Ft(t)},undo=()=>{for(const e of t)e.remove()};this.addCommands({cmd,undo,mustExec:!0})}catch(t){warn(`paste: "${t.message}".`)}}keydown(t){this.isShiftKeyDown||"Shift"!==t.key||(this.isShiftKeyDown=!0);this.#ot===g.NONE||this.isEditorHandlingKeyboard||AnnotationEditorUIManager._keyboardManager.exec(this,t)}keyup(t){if(this.isShiftKeyDown&&"Shift"===t.key){this.isShiftKeyDown=!1;if(this.#Q){this.#Q=!1;this.#Tt("main_toolbar")}}}onEditingAction({name:t}){switch(t){case"undo":case"redo":case"delete":case"selectAll":this[t]();break;case"highlightSelection":this.highlightSelection("context_menu")}}#Ct(t){if(Object.entries(t).some((([t,e])=>this.#pt[t]!==e))){this._eventBus.dispatch("annotationeditorstateschanged",{source:this,details:Object.assign(this.#pt,t)});this.#ot===g.HIGHLIGHT&&!1===t.hasSelectedEditor&&this.#Lt([[f.HIGHLIGHT_FREE,!0]])}}#Lt(t){this._eventBus.dispatch("annotationeditorparamschanged",{source:this,details:t})}setEditingState(t){if(t){this.#Dt();this.#kt();this.#Ct({isEditing:this.#ot!==g.NONE,isEmpty:this.#Ot(),hasSomethingToUndo:this.#O.hasSomethingToUndo(),hasSomethingToRedo:this.#O.hasSomethingToRedo(),hasSelectedEditor:!1})}else{this.#Mt();this.#It();this.#Ct({isEditing:!1});this.disableUserSelect(!1)}}registerEditorTypes(t){if(!this.#j){this.#j=t;for(const t of this.#j)this.#Lt(t.defaultPropertiesToUpdate)}}getId(){return this.#Z.id}get currentLayer(){return this.#I.get(this.#U)}getLayer(t){return this.#I.get(t)}get currentPageIndex(){return this.#U}addLayer(t){this.#I.set(t.pageIndex,t);this.#tt?t.enable():t.disable()}removeLayer(t){this.#I.delete(t.pageIndex)}async updateMode(t,e=null,i=!1){if(this.#ot!==t){if(this.#vt){await this.#vt.promise;if(!this.#vt)return}this.#vt=Promise.withResolvers();this.#B?.commitOrRemove();this.#ot=t;if(t!==g.NONE){t===g.SIGNATURE&&await(this.#ct?.loadSignatures());this.setEditingState(!0);await this.#Nt();this.unselectAll();for(const e of this.#I.values())e.updateMode(t);if(e){for(const t of this.#k.values())if(t.annotationElementId===e){this.setSelected(t);t.enterInEditMode()}else t.unselect();this.#vt.resolve()}else{i&&this.addNewEditorFromKeyboard();this.#vt.resolve()}}else{this.setEditingState(!1);this.#Bt();this._editorUndoBar?.hide();this.#vt.resolve()}}}addNewEditorFromKeyboard(){this.currentLayer.canCreateNewEmptyEditor()&&this.currentLayer.addNewEditor()}updateToolbar(t){t!==this.#ot&&this._eventBus.dispatch("switchannotationeditormode",{source:this,mode:t})}updateParams(t,e){if(this.#j){switch(t){case f.CREATE:this.currentLayer.addNewEditor(e);return;case f.HIGHLIGHT_DEFAULT_COLOR:this.#nt?.updateColor(e);break;case f.HIGHLIGHT_SHOW_ALL:this._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:{type:"highlight",action:"toggle_visibility"}}});(this.#ut||=new Map).set(t,e);this.showAllEditors("highlight",e)}for(const i of this.#lt)i.updateParams(t,e);for(const i of this.#j)i.updateDefaultParams(t,e)}}showAllEditors(t,e,i=!1){for(const i of this.#k.values())i.editorType===t&&i.show(e);(this.#ut?.get(f.HIGHLIGHT_SHOW_ALL)??!0)!==e&&this.#Lt([[f.HIGHLIGHT_SHOW_ALL,e]])}enableWaiting(t=!1){if(this.#et!==t){this.#et=t;for(const e of this.#I.values()){t?e.disableClick():e.enableClick();e.div.classList.toggle("waiting",t)}}}async#Nt(){if(!this.#tt){this.#tt=!0;const t=[];for(const e of this.#I.values())t.push(e.enable());await Promise.all(t);for(const t of this.#k.values())t.enable()}}#Bt(){this.unselectAll();if(this.#tt){this.#tt=!1;for(const t of this.#I.values())t.disable();for(const t of this.#k.values())t.disable()}}getEditors(t){const e=[];for(const i of this.#k.values())i.pageIndex===t&&e.push(i);return e}getEditor(t){return this.#k.get(t)}addEditor(t){this.#k.set(t.id,t)}removeEditor(t){if(t.div.contains(document.activeElement)){this.#X&&clearTimeout(this.#X);this.#X=setTimeout((()=>{this.focusMainContainer();this.#X=null}),0)}this.#k.delete(t.id);t.annotationElementId&&this.#rt?.delete(t.annotationElementId);this.unselect(t);t.annotationElementId&&this.#H.has(t.annotationElementId)||this.#F?.remove(t.id)}addDeletedAnnotationElement(t){this.#H.add(t.annotationElementId);this.addChangedExistingAnnotation(t);t.deleted=!0}isDeletedAnnotationElement(t){return this.#H.has(t)}removeDeletedAnnotationElement(t){this.#H.delete(t.annotationElementId);this.removeChangedExistingAnnotation(t);t.deleted=!1}#Rt(t){const e=this.#I.get(t.pageIndex);if(e)e.addOrRebuild(t);else{this.addEditor(t);this.addToAnnotationStorage(t)}}setActiveEditor(t){if(this.#P!==t){this.#P=t;t&&this.#Lt(t.propertiesToUpdate)}}get#Ut(){let t=null;for(t of this.#lt);return t}updateUI(t){this.#Ut===t&&this.#Lt(t.propertiesToUpdate)}updateUIForDefaultProperties(t){this.#Lt(t.defaultPropertiesToUpdate)}toggleSelected(t){if(this.#lt.has(t)){this.#lt.delete(t);t.unselect();this.#Ct({hasSelectedEditor:this.hasSelection})}else{this.#lt.add(t);t.select();this.#Lt(t.propertiesToUpdate);this.#Ct({hasSelectedEditor:!0})}}setSelected(t){this.#B?.commitOrRemove();for(const e of this.#lt)e!==t&&e.unselect();this.#lt.clear();this.#lt.add(t);t.select();this.#Lt(t.propertiesToUpdate);this.#Ct({hasSelectedEditor:!0})}isSelected(t){return this.#lt.has(t)}get firstSelectedEditor(){return this.#lt.values().next().value}unselect(t){t.unselect();this.#lt.delete(t);this.#Ct({hasSelectedEditor:this.hasSelection})}get hasSelection(){return 0!==this.#lt.size}get isEnterHandled(){return 1===this.#lt.size&&this.firstSelectedEditor.isEnterHandled}undo(){this.#O.undo();this.#Ct({hasSomethingToUndo:this.#O.hasSomethingToUndo(),hasSomethingToRedo:!0,isEmpty:this.#Ot()});this._editorUndoBar?.hide()}redo(){this.#O.redo();this.#Ct({hasSomethingToUndo:!0,hasSomethingToRedo:this.#O.hasSomethingToRedo(),isEmpty:this.#Ot()})}addCommands(t){this.#O.add(t);this.#Ct({hasSomethingToUndo:!0,hasSomethingToRedo:!1,isEmpty:this.#Ot()})}cleanUndoStack(t){this.#O.cleanType(t)}#Ot(){if(0===this.#k.size)return!0;if(1===this.#k.size)for(const t of this.#k.values())return t.isEmpty();return!1}delete(){this.commitOrRemove();const t=this.currentLayer?.endDrawingSession(!0);if(!this.hasSelection&&!t)return;const e=t?[t]:[...this.#lt],undo=()=>{for(const t of e)this.#Rt(t)};this.addCommands({cmd:()=>{this._editorUndoBar?.show(undo,1===e.length?e[0].editorType:e.length);for(const t of e)t.remove()},undo,mustExec:!0})}commitOrRemove(){this.#P?.commitOrRemove()}hasSomethingToControl(){return this.#P||this.hasSelection}#Ft(t){for(const t of this.#lt)t.unselect();this.#lt.clear();for(const e of t)if(!e.isEmpty()){this.#lt.add(e);e.select()}this.#Ct({hasSelectedEditor:this.hasSelection})}selectAll(){for(const t of this.#lt)t.commit();this.#Ft(this.#k.values())}unselectAll(){if(this.#P){this.#P.commitOrRemove();if(this.#ot!==g.NONE)return}if(!this.#B?.commitOrRemove()&&this.hasSelection){for(const t of this.#lt)t.unselect();this.#lt.clear();this.#Ct({hasSelectedEditor:!1})}}translateSelectedEditors(t,e,i=!1){i||this.commitOrRemove();if(!this.hasSelection)return;this.#gt[0]+=t;this.#gt[1]+=e;const[s,n]=this.#gt,r=[...this.#lt];this.#ft&&clearTimeout(this.#ft);this.#ft=setTimeout((()=>{this.#ft=null;this.#gt[0]=this.#gt[1]=0;this.addCommands({cmd:()=>{for(const t of r)if(this.#k.has(t.id)){t.translateInPage(s,n);t.translationDone()}},undo:()=>{for(const t of r)if(this.#k.has(t.id)){t.translateInPage(-s,-n);t.translationDone()}},mustExec:!1})}),1e3);for(const i of r){i.translateInPage(t,e);i.translationDone()}}setUpDragSession(){if(this.hasSelection){this.disableUserSelect(!0);this.#z=new Map;for(const t of this.#lt)this.#z.set(t,{savedX:t.x,savedY:t.y,savedPageIndex:t.pageIndex,newX:0,newY:0,newPageIndex:-1})}}endDragSession(){if(!this.#z)return!1;this.disableUserSelect(!1);const t=this.#z;this.#z=null;let e=!1;for(const[{x:i,y:s,pageIndex:n},r]of t){r.newX=i;r.newY=s;r.newPageIndex=n;e||=i!==r.savedX||s!==r.savedY||n!==r.savedPageIndex}if(!e)return!1;const move=(t,e,i,s)=>{if(this.#k.has(t.id)){const n=this.#I.get(s);if(n)t._setParentAndPosition(n,e,i);else{t.pageIndex=s;t.x=e;t.y=i}}};this.addCommands({cmd:()=>{for(const[e,{newX:i,newY:s,newPageIndex:n}]of t)move(e,i,s,n)},undo:()=>{for(const[e,{savedX:i,savedY:s,savedPageIndex:n}]of t)move(e,i,s,n)},mustExec:!0});return!0}dragSelectedEditors(t,e){if(this.#z)for(const i of this.#z.keys())i.drag(t,e)}rebuild(t){if(null===t.parent){const e=this.getLayer(t.pageIndex);if(e){e.changeParent(t);e.addOrRebuild(t)}else{this.addEditor(t);this.addToAnnotationStorage(t);t.rebuild()}}else t.parent.addOrRebuild(t)}get isEditorHandlingKeyboard(){return this.getActive()?.shouldGetKeyboardEvents()||1===this.#lt.size&&this.firstSelectedEditor.shouldGetKeyboardEvents()}isActive(t){return this.#P===t}getActive(){return this.#P}getMode(){return this.#ot}get imageManager(){return shadow(this,"imageManager",new ImageManager)}getSelectionBoxes(t){if(!t)return null;const e=document.getSelection();for(let i=0,s=e.rangeCount;i<s;i++)if(!t.contains(e.getRangeAt(i).commonAncestorContainer))return null;const{x:i,y:s,width:n,height:r}=t.getBoundingClientRect();let a;switch(t.getAttribute("data-main-rotation")){case"90":a=(t,e,a,o)=>({x:(e-s)/r,y:1-(t+a-i)/n,width:o/r,height:a/n});break;case"180":a=(t,e,a,o)=>({x:1-(t+a-i)/n,y:1-(e+o-s)/r,width:a/n,height:o/r});break;case"270":a=(t,e,a,o)=>({x:1-(e+o-s)/r,y:(t-i)/n,width:o/r,height:a/n});break;default:a=(t,e,a,o)=>({x:(t-i)/n,y:(e-s)/r,width:a/n,height:o/r})}const o=[];for(let t=0,i=e.rangeCount;t<i;t++){const i=e.getRangeAt(t);if(!i.collapsed)for(const{x:t,y:e,width:s,height:n}of i.getClientRects())0!==s&&0!==n&&o.push(a(t,e,s,n))}return 0===o.length?null:o}addChangedExistingAnnotation({annotationElementId:t,id:e}){(this.#L||=new Map).set(t,e)}removeChangedExistingAnnotation({annotationElementId:t}){this.#L?.delete(t)}renderAnnotationElement(t){const e=this.#L?.get(t.data.id);if(!e)return;const i=this.#F.getRawValue(e);i&&(this.#ot!==g.NONE||i.hasBeenModified)&&i.renderAnnotationElement(t)}setMissingCanvas(t,e,i){const s=this.#rt?.get(t);if(s){s.setCanvas(e,i);this.#rt.delete(t)}}addMissingCanvas(t,e){(this.#rt||=new Map).set(t,e)}}class AltText{#o=null;#Ht=!1;#zt=null;#jt=null;#Gt=null;#Wt=null;#$t=!1;#Vt=null;#r=null;#qt=null;#Xt=null;#Kt=!1;static#Yt=null;static _l10n=null;constructor(t){this.#r=t;this.#Kt=t._uiManager.useNewAltTextFlow;AltText.#Yt||=Object.freeze({added:"pdfjs-editor-new-alt-text-added-button","added-label":"pdfjs-editor-new-alt-text-added-button-label",missing:"pdfjs-editor-new-alt-text-missing-button","missing-label":"pdfjs-editor-new-alt-text-missing-button-label",review:"pdfjs-editor-new-alt-text-to-review-button","review-label":"pdfjs-editor-new-alt-text-to-review-button-label"})}static initialize(t){AltText._l10n??=t}async render(){const t=this.#zt=document.createElement("button");t.className="altText";t.tabIndex="0";const e=this.#jt=document.createElement("span");t.append(e);if(this.#Kt){t.classList.add("new");t.setAttribute("data-l10n-id",AltText.#Yt.missing);e.setAttribute("data-l10n-id",AltText.#Yt["missing-label"])}else{t.setAttribute("data-l10n-id","pdfjs-editor-alt-text-button");e.setAttribute("data-l10n-id","pdfjs-editor-alt-text-button-label")}const i=this.#r._uiManager._signal;t.addEventListener("contextmenu",noContextMenu,{signal:i});t.addEventListener("pointerdown",(t=>t.stopPropagation()),{signal:i});const onClick=t=>{t.preventDefault();this.#r._uiManager.editAltText(this.#r);this.#Kt&&this.#r._reportTelemetry({action:"pdfjs.image.alt_text.image_status_label_clicked",data:{label:this.#Qt}})};t.addEventListener("click",onClick,{capture:!0,signal:i});t.addEventListener("keydown",(e=>{if(e.target===t&&"Enter"===e.key){this.#$t=!0;onClick(e)}}),{signal:i});await this.#Jt();return t}get#Qt(){return(this.#o?"added":null===this.#o&&this.guessedText&&"review")||"missing"}finish(){if(this.#zt){this.#zt.focus({focusVisible:this.#$t});this.#$t=!1}}isEmpty(){return this.#Kt?null===this.#o:!this.#o&&!this.#Ht}hasData(){return this.#Kt?null!==this.#o||!!this.#qt:this.isEmpty()}get guessedText(){return this.#qt}async setGuessedText(t){if(null===this.#o){this.#qt=t;this.#Xt=await AltText._l10n.get("pdfjs-editor-new-alt-text-generated-alt-text-with-disclaimer",{generatedAltText:t});this.#Jt()}}toggleAltTextBadge(t=!1){if(this.#Kt&&!this.#o){if(!this.#Vt){const t=this.#Vt=document.createElement("div");t.className="noAltTextBadge";this.#r.div.append(t)}this.#Vt.classList.toggle("hidden",!t)}else{this.#Vt?.remove();this.#Vt=null}}serialize(t){let e=this.#o;t||this.#qt!==e||(e=this.#Xt);return{altText:e,decorative:this.#Ht,guessedText:this.#qt,textWithDisclaimer:this.#Xt}}get data(){return{altText:this.#o,decorative:this.#Ht}}set data({altText:t,decorative:e,guessedText:i,textWithDisclaimer:s,cancel:n=!1}){if(i){this.#qt=i;this.#Xt=s}if(this.#o!==t||this.#Ht!==e){if(!n){this.#o=t;this.#Ht=e}this.#Jt()}}toggle(t=!1){if(this.#zt){if(!t&&this.#Wt){clearTimeout(this.#Wt);this.#Wt=null}this.#zt.disabled=!t}}shown(){this.#r._reportTelemetry({action:"pdfjs.image.alt_text.image_status_label_displayed",data:{label:this.#Qt}})}destroy(){this.#zt?.remove();this.#zt=null;this.#jt=null;this.#Gt=null;this.#Vt?.remove();this.#Vt=null}async#Jt(){const t=this.#zt;if(!t)return;if(this.#Kt){t.classList.toggle("done",!!this.#o);t.setAttribute("data-l10n-id",AltText.#Yt[this.#Qt]);this.#jt?.setAttribute("data-l10n-id",AltText.#Yt[`${this.#Qt}-label`]);if(!this.#o){this.#Gt?.remove();return}}else{if(!this.#o&&!this.#Ht){t.classList.remove("done");this.#Gt?.remove();return}t.classList.add("done");t.setAttribute("data-l10n-id","pdfjs-editor-alt-text-edit-button")}let e=this.#Gt;if(!e){this.#Gt=e=document.createElement("span");e.className="tooltip";e.setAttribute("role","tooltip");e.id=`alt-text-tooltip-${this.#r.id}`;const i=100,s=this.#r._uiManager._signal;s.addEventListener("abort",(()=>{clearTimeout(this.#Wt);this.#Wt=null}),{once:!0});t.addEventListener("mouseenter",(()=>{this.#Wt=setTimeout((()=>{this.#Wt=null;this.#Gt.classList.add("show");this.#r._reportTelemetry({action:"alt_text_tooltip"})}),i)}),{signal:s});t.addEventListener("mouseleave",(()=>{if(this.#Wt){clearTimeout(this.#Wt);this.#Wt=null}this.#Gt?.classList.remove("show")}),{signal:s})}if(this.#Ht)e.setAttribute("data-l10n-id","pdfjs-editor-alt-text-decorative-tooltip");else{e.removeAttribute("data-l10n-id");e.textContent=this.#o}e.parentNode||t.append(e);const i=this.#r.getElementForAltText();i?.setAttribute("aria-describedby",e.id)}}class TouchManager{#mt;#Zt=!1;#te=null;#ee;#ie;#se;#ne;#re=null;#ae;#oe=null;#le;#he=null;constructor({container:t,isPinchingDisabled:e=null,isPinchingStopped:i=null,onPinchStart:s=null,onPinching:n=null,onPinchEnd:r=null,signal:a}){this.#mt=t;this.#te=i;this.#ee=e;this.#ie=s;this.#se=n;this.#ne=r;this.#le=new AbortController;this.#ae=AbortSignal.any([a,this.#le.signal]);t.addEventListener("touchstart",this.#ce.bind(this),{passive:!1,signal:this.#ae})}get MIN_TOUCH_DISTANCE_TO_PINCH(){return 35/OutputScale.pixelRatio}#ce(t){if(this.#ee?.())return;if(1===t.touches.length){if(this.#re)return;const t=this.#re=new AbortController,e=AbortSignal.any([this.#ae,t.signal]),i=this.#mt,s={capture:!0,signal:e,passive:!1},cancelPointerDown=t=>{if("touch"===t.pointerType){this.#re?.abort();this.#re=null}};i.addEventListener("pointerdown",(t=>{if("touch"===t.pointerType){stopEvent(t);cancelPointerDown(t)}}),s);i.addEventListener("pointerup",cancelPointerDown,s);i.addEventListener("pointercancel",cancelPointerDown,s);return}if(!this.#he){this.#he=new AbortController;const t=AbortSignal.any([this.#ae,this.#he.signal]),e=this.#mt,i={signal:t,capture:!1,passive:!1};e.addEventListener("touchmove",this.#de.bind(this),i);const s=this.#ue.bind(this);e.addEventListener("touchend",s,i);e.addEventListener("touchcancel",s,i);i.capture=!0;e.addEventListener("pointerdown",stopEvent,i);e.addEventListener("pointermove",stopEvent,i);e.addEventListener("pointercancel",stopEvent,i);e.addEventListener("pointerup",stopEvent,i);this.#ie?.()}stopEvent(t);if(2!==t.touches.length||this.#te?.()){this.#oe=null;return}let[e,i]=t.touches;e.identifier>i.identifier&&([e,i]=[i,e]);this.#oe={touch0X:e.screenX,touch0Y:e.screenY,touch1X:i.screenX,touch1Y:i.screenY}}#de(t){if(!this.#oe||2!==t.touches.length)return;stopEvent(t);let[e,i]=t.touches;e.identifier>i.identifier&&([e,i]=[i,e]);const{screenX:s,screenY:n}=e,{screenX:r,screenY:a}=i,o=this.#oe,{touch0X:l,touch0Y:h,touch1X:c,touch1Y:d}=o,u=c-l,p=d-h,g=r-s,f=a-n,m=Math.hypot(g,f)||1,b=Math.hypot(u,p)||1;if(!this.#Zt&&Math.abs(b-m)<=TouchManager.MIN_TOUCH_DISTANCE_TO_PINCH)return;o.touch0X=s;o.touch0Y=n;o.touch1X=r;o.touch1Y=a;if(!this.#Zt){this.#Zt=!0;return}const v=[(s+r)/2,(n+a)/2];this.#se?.(v,b,m)}#ue(t){if(!(t.touches.length>=2)){if(this.#he){this.#he.abort();this.#he=null;this.#ne?.()}if(this.#oe){stopEvent(t);this.#oe=null;this.#Zt=!1}}}destroy(){this.#le?.abort();this.#le=null;this.#re?.abort();this.#re=null}}class AnnotationEditor{#pe=null;#ge=null;#o=null;#fe=!1;#me=null;#be="";#ve=!1;#we=null;#ye=null;#Ae=null;#_e=null;#xe="";#Ee=!1;#Se=null;#Ce=!1;#Te=!1;#De=!1;#Me=null;#Pe=0;#ke=0;#Ie=null;#Re=null;_isCopy=!1;_editToolbar=null;_initialOptions=Object.create(null);_initialData=null;_isVisible=!0;_uiManager=null;_focusEventsAllowed=!0;static _l10n=null;static _l10nResizer=null;#Fe=!1;#Le=AnnotationEditor._zIndex++;static _borderLineWidth=-1;static _colorManager=new ColorManager;static _zIndex=1;static _telemetryTimeout=1e3;static get _resizerKeyboardManager(){const t=AnnotationEditor.prototype._resizeWithKeyboard,e=AnnotationEditorUIManager.TRANSLATE_SMALL,i=AnnotationEditorUIManager.TRANSLATE_BIG;return shadow(this,"_resizerKeyboardManager",new KeyboardManager([[["ArrowLeft","mac+ArrowLeft"],t,{args:[-e,0]}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t,{args:[-i,0]}],[["ArrowRight","mac+ArrowRight"],t,{args:[e,0]}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t,{args:[i,0]}],[["ArrowUp","mac+ArrowUp"],t,{args:[0,-e]}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t,{args:[0,-i]}],[["ArrowDown","mac+ArrowDown"],t,{args:[0,e]}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t,{args:[0,i]}],[["Escape","mac+Escape"],AnnotationEditor.prototype._stopResizingWithKeyboard]]))}constructor(t){this.parent=t.parent;this.id=t.id;this.width=this.height=null;this.pageIndex=t.parent.pageIndex;this.name=t.name;this.div=null;this._uiManager=t.uiManager;this.annotationElementId=null;this._willKeepAspectRatio=!1;this._initialOptions.isCentered=t.isCentered;this._structTreeParentId=null;const{rotation:e,rawDims:{pageWidth:i,pageHeight:s,pageX:n,pageY:r}}=this.parent.viewport;this.rotation=e;this.pageRotation=(360+e-this._uiManager.viewParameters.rotation)%360;this.pageDimensions=[i,s];this.pageTranslation=[n,r];const[a,o]=this.parentDimensions;this.x=t.x/a;this.y=t.y/o;this.isAttachedToDOM=!1;this.deleted=!1}get editorType(){return Object.getPrototypeOf(this).constructor._type}static get isDrawer(){return!1}static get _defaultLineColor(){return shadow(this,"_defaultLineColor",this._colorManager.getHexCode("CanvasText"))}static deleteAnnotationElement(t){const e=new FakeEditor({id:t.parent.getNextId(),parent:t.parent,uiManager:t._uiManager});e.annotationElementId=t.annotationElementId;e.deleted=!0;e._uiManager.addToAnnotationStorage(e)}static initialize(t,e){AnnotationEditor._l10n??=t;AnnotationEditor._l10nResizer||=Object.freeze({topLeft:"pdfjs-editor-resizer-top-left",topMiddle:"pdfjs-editor-resizer-top-middle",topRight:"pdfjs-editor-resizer-top-right",middleRight:"pdfjs-editor-resizer-middle-right",bottomRight:"pdfjs-editor-resizer-bottom-right",bottomMiddle:"pdfjs-editor-resizer-bottom-middle",bottomLeft:"pdfjs-editor-resizer-bottom-left",middleLeft:"pdfjs-editor-resizer-middle-left"});if(-1!==AnnotationEditor._borderLineWidth)return;const i=getComputedStyle(document.documentElement);AnnotationEditor._borderLineWidth=parseFloat(i.getPropertyValue("--outline-width"))||0}static updateDefaultParams(t,e){}static get defaultPropertiesToUpdate(){return[]}static isHandlingMimeForPasting(t){return!1}static paste(t,e){unreachable("Not implemented")}get propertiesToUpdate(){return[]}get _isDraggable(){return this.#Fe}set _isDraggable(t){this.#Fe=t;this.div?.classList.toggle("draggable",t)}get isEnterHandled(){return!0}center(){const[t,e]=this.pageDimensions;switch(this.parentRotation){case 90:this.x-=this.height*e/(2*t);this.y+=this.width*t/(2*e);break;case 180:this.x+=this.width/2;this.y+=this.height/2;break;case 270:this.x+=this.height*e/(2*t);this.y-=this.width*t/(2*e);break;default:this.x-=this.width/2;this.y-=this.height/2}this.fixAndSetPosition()}addCommands(t){this._uiManager.addCommands(t)}get currentLayer(){return this._uiManager.currentLayer}setInBackground(){this.div.style.zIndex=0}setInForeground(){this.div.style.zIndex=this.#Le}setParent(t){if(null!==t){this.pageIndex=t.pageIndex;this.pageDimensions=t.pageDimensions}else this.#Oe();this.parent=t}focusin(t){this._focusEventsAllowed&&(this.#Ee?this.#Ee=!1:this.parent.setSelected(this))}focusout(t){if(!this._focusEventsAllowed)return;if(!this.isAttachedToDOM)return;const e=t.relatedTarget;if(!e?.closest(`#${this.id}`)){t.preventDefault();this.parent?.isMultipleSelection||this.commitOrRemove()}}commitOrRemove(){this.isEmpty()?this.remove():this.commit()}commit(){this.addToAnnotationStorage()}addToAnnotationStorage(){this._uiManager.addToAnnotationStorage(this)}setAt(t,e,i,s){const[n,r]=this.parentDimensions;[i,s]=this.screenToPageTranslation(i,s);this.x=(t+i)/n;this.y=(e+s)/r;this.fixAndSetPosition()}_moveAfterPaste(t,e){const[i,s]=this.parentDimensions;this.setAt(t*i,e*s,this.width*i,this.height*s);this._onTranslated()}#Ne([t,e],i,s){[i,s]=this.screenToPageTranslation(i,s);this.x+=i/t;this.y+=s/e;this._onTranslating(this.x,this.y);this.fixAndSetPosition()}translate(t,e){this.#Ne(this.parentDimensions,t,e)}translateInPage(t,e){this.#Se||=[this.x,this.y,this.width,this.height];this.#Ne(this.pageDimensions,t,e);this.div.scrollIntoView({block:"nearest"})}translationDone(){this._onTranslated(this.x,this.y)}drag(t,e){this.#Se||=[this.x,this.y,this.width,this.height];const{div:i,parentDimensions:[s,n]}=this;this.x+=t/s;this.y+=e/n;if(this.parent&&(this.x<0||this.x>1||this.y<0||this.y>1)){const{x:t,y:e}=this.div.getBoundingClientRect();if(this.parent.findNewParent(this,t,e)){this.x-=Math.floor(this.x);this.y-=Math.floor(this.y)}}let{x:r,y:a}=this;const[o,l]=this.getBaseTranslation();r+=o;a+=l;const{style:h}=i;h.left=`${(100*r).toFixed(2)}%`;h.top=`${(100*a).toFixed(2)}%`;this._onTranslating(r,a);i.scrollIntoView({block:"nearest"})}_onTranslating(t,e){}_onTranslated(t,e){}get _hasBeenMoved(){return!!this.#Se&&(this.#Se[0]!==this.x||this.#Se[1]!==this.y)}get _hasBeenResized(){return!!this.#Se&&(this.#Se[2]!==this.width||this.#Se[3]!==this.height)}getBaseTranslation(){const[t,e]=this.parentDimensions,{_borderLineWidth:i}=AnnotationEditor,s=i/t,n=i/e;switch(this.rotation){case 90:return[-s,n];case 180:return[s,n];case 270:return[s,-n];default:return[-s,-n]}}get _mustFixPosition(){return!0}fixAndSetPosition(t=this.rotation){const{div:{style:e},pageDimensions:[i,s]}=this;let{x:n,y:r,width:a,height:o}=this;a*=i;o*=s;n*=i;r*=s;if(this._mustFixPosition)switch(t){case 0:n=MathClamp(n,0,i-a);r=MathClamp(r,0,s-o);break;case 90:n=MathClamp(n,0,i-o);r=MathClamp(r,a,s);break;case 180:n=MathClamp(n,a,i);r=MathClamp(r,o,s);break;case 270:n=MathClamp(n,o,i);r=MathClamp(r,0,s-a)}this.x=n/=i;this.y=r/=s;const[l,h]=this.getBaseTranslation();n+=l;r+=h;e.left=`${(100*n).toFixed(2)}%`;e.top=`${(100*r).toFixed(2)}%`;this.moveInDOM()}static#Be(t,e,i){switch(i){case 90:return[e,-t];case 180:return[-t,-e];case 270:return[-e,t];default:return[t,e]}}screenToPageTranslation(t,e){return AnnotationEditor.#Be(t,e,this.parentRotation)}pageTranslationToScreen(t,e){return AnnotationEditor.#Be(t,e,360-this.parentRotation)}#Ue(t){switch(t){case 90:{const[t,e]=this.pageDimensions;return[0,-t/e,e/t,0]}case 180:return[-1,0,0,-1];case 270:{const[t,e]=this.pageDimensions;return[0,t/e,-e/t,0]}default:return[1,0,0,1]}}get parentScale(){return this._uiManager.viewParameters.realScale}get parentRotation(){return(this._uiManager.viewParameters.rotation+this.pageRotation)%360}get parentDimensions(){const{parentScale:t,pageDimensions:[e,i]}=this;return[e*t,i*t]}setDims(t,e){const[i,s]=this.parentDimensions,{style:n}=this.div;n.width=`${(100*t/i).toFixed(2)}%`;this.#ve||(n.height=`${(100*e/s).toFixed(2)}%`)}fixDims(){const{style:t}=this.div,{height:e,width:i}=t,s=i.endsWith("%"),n=!this.#ve&&e.endsWith("%");if(s&&n)return;const[r,a]=this.parentDimensions;s||(t.width=`${(100*parseFloat(i)/r).toFixed(2)}%`);this.#ve||n||(t.height=`${(100*parseFloat(e)/a).toFixed(2)}%`)}getInitialTranslation(){return[0,0]}#He(){if(this.#we)return;this.#we=document.createElement("div");this.#we.classList.add("resizers");const t=this._willKeepAspectRatio?["topLeft","topRight","bottomRight","bottomLeft"]:["topLeft","topMiddle","topRight","middleRight","bottomRight","bottomMiddle","bottomLeft","middleLeft"],e=this._uiManager._signal;for(const i of t){const t=document.createElement("div");this.#we.append(t);t.classList.add("resizer",i);t.setAttribute("data-resizer-name",i);t.addEventListener("pointerdown",this.#ze.bind(this,i),{signal:e});t.addEventListener("contextmenu",noContextMenu,{signal:e});t.tabIndex=-1}this.div.prepend(this.#we)}#ze(t,e){e.preventDefault();const{isMac:i}=util_FeatureTest.platform;if(0!==e.button||e.ctrlKey&&i)return;this.#o?.toggle(!1);const s=this._isDraggable;this._isDraggable=!1;this.#ye=[e.screenX,e.screenY];const n=new AbortController,r=this._uiManager.combinedSignal(n);this.parent.togglePointerEvents(!1);window.addEventListener("pointermove",this.#je.bind(this,t),{passive:!0,capture:!0,signal:r});window.addEventListener("touchmove",stopEvent,{passive:!1,signal:r});window.addEventListener("contextmenu",noContextMenu,{signal:r});this.#Ae={savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height};const a=this.parent.div.style.cursor,o=this.div.style.cursor;this.div.style.cursor=this.parent.div.style.cursor=window.getComputedStyle(e.target).cursor;const pointerUpCallback=()=>{n.abort();this.parent.togglePointerEvents(!0);this.#o?.toggle(!0);this._isDraggable=s;this.parent.div.style.cursor=a;this.div.style.cursor=o;this.#Ge()};window.addEventListener("pointerup",pointerUpCallback,{signal:r});window.addEventListener("blur",pointerUpCallback,{signal:r})}#We(t,e,i,s){this.width=i;this.height=s;this.x=t;this.y=e;const[n,r]=this.parentDimensions;this.setDims(n*i,r*s);this.fixAndSetPosition();this._onResized()}_onResized(){}#Ge(){if(!this.#Ae)return;const{savedX:t,savedY:e,savedWidth:i,savedHeight:s}=this.#Ae;this.#Ae=null;const n=this.x,r=this.y,a=this.width,o=this.height;n===t&&r===e&&a===i&&o===s||this.addCommands({cmd:this.#We.bind(this,n,r,a,o),undo:this.#We.bind(this,t,e,i,s),mustExec:!0})}static _round(t){return Math.round(1e4*t)/1e4}#je(t,e){const[i,s]=this.parentDimensions,n=this.x,r=this.y,a=this.width,o=this.height,l=AnnotationEditor.MIN_SIZE/i,h=AnnotationEditor.MIN_SIZE/s,c=this.#Ue(this.rotation),transf=(t,e)=>[c[0]*t+c[2]*e,c[1]*t+c[3]*e],d=this.#Ue(360-this.rotation);let u,p,g=!1,f=!1;switch(t){case"topLeft":g=!0;u=(t,e)=>[0,0];p=(t,e)=>[t,e];break;case"topMiddle":u=(t,e)=>[t/2,0];p=(t,e)=>[t/2,e];break;case"topRight":g=!0;u=(t,e)=>[t,0];p=(t,e)=>[0,e];break;case"middleRight":f=!0;u=(t,e)=>[t,e/2];p=(t,e)=>[0,e/2];break;case"bottomRight":g=!0;u=(t,e)=>[t,e];p=(t,e)=>[0,0];break;case"bottomMiddle":u=(t,e)=>[t/2,e];p=(t,e)=>[t/2,0];break;case"bottomLeft":g=!0;u=(t,e)=>[0,e];p=(t,e)=>[t,0];break;case"middleLeft":f=!0;u=(t,e)=>[0,e/2];p=(t,e)=>[t,e/2]}const m=u(a,o),b=p(a,o);let v=transf(...b);const w=AnnotationEditor._round(n+v[0]),y=AnnotationEditor._round(r+v[1]);let A,_,x=1,E=1;if(e.fromKeyboard)({deltaX:A,deltaY:_}=e);else{const{screenX:t,screenY:i}=e,[s,n]=this.#ye;[A,_]=this.screenToPageTranslation(t-s,i-n);this.#ye[0]=t;this.#ye[1]=i}[A,_]=(S=A/i,C=_/s,[d[0]*S+d[2]*C,d[1]*S+d[3]*C]);var S,C;if(g){const t=Math.hypot(a,o);x=E=Math.max(Math.min(Math.hypot(b[0]-m[0]-A,b[1]-m[1]-_)/t,1/a,1/o),l/a,h/o)}else f?x=MathClamp(Math.abs(b[0]-m[0]-A),l,1)/a:E=MathClamp(Math.abs(b[1]-m[1]-_),h,1)/o;const T=AnnotationEditor._round(a*x),D=AnnotationEditor._round(o*E);v=transf(...p(T,D));const M=w-v[0],P=y-v[1];this.#Se||=[this.x,this.y,this.width,this.height];this.width=T;this.height=D;this.x=M;this.y=P;this.setDims(i*T,s*D);this.fixAndSetPosition();this._onResizing()}_onResizing(){}altTextFinish(){this.#o?.finish()}async addEditToolbar(){if(this._editToolbar||this.#Te)return this._editToolbar;this._editToolbar=new EditorToolbar(this);this.div.append(this._editToolbar.render());this.#o&&await this._editToolbar.addAltText(this.#o);return this._editToolbar}removeEditToolbar(){if(this._editToolbar){this._editToolbar.remove();this._editToolbar=null;this.#o?.destroy()}}addContainer(t){const e=this._editToolbar?.div;e?e.before(t):this.div.append(t)}getClientDimensions(){return this.div.getBoundingClientRect()}async addAltTextButton(){if(!this.#o){AltText.initialize(AnnotationEditor._l10n);this.#o=new AltText(this);if(this.#pe){this.#o.data=this.#pe;this.#pe=null}await this.addEditToolbar()}}get altTextData(){return this.#o?.data}set altTextData(t){this.#o&&(this.#o.data=t)}get guessedAltText(){return this.#o?.guessedText}async setGuessedAltText(t){await(this.#o?.setGuessedText(t))}serializeAltText(t){return this.#o?.serialize(t)}hasAltText(){return!!this.#o&&!this.#o.isEmpty()}hasAltTextData(){return this.#o?.hasData()??!1}render(){const t=this.div=document.createElement("div");t.setAttribute("data-editor-rotation",(360-this.rotation)%360);t.className=this.name;t.setAttribute("id",this.id);t.tabIndex=this.#fe?-1:0;t.setAttribute("role","application");this.defaultL10nId&&t.setAttribute("data-l10n-id",this.defaultL10nId);this._isVisible||t.classList.add("hidden");this.setInForeground();this.#$e();const[e,i]=this.parentDimensions;if(this.parentRotation%180!=0){t.style.maxWidth=`${(100*i/e).toFixed(2)}%`;t.style.maxHeight=`${(100*e/i).toFixed(2)}%`}const[s,n]=this.getInitialTranslation();this.translate(s,n);bindEvents(this,t,["keydown","pointerdown"]);this.isResizable&&this._uiManager._supportsPinchToZoom&&(this.#Re||=new TouchManager({container:t,isPinchingDisabled:()=>!this.isSelected,onPinchStart:this.#Ve.bind(this),onPinching:this.#qe.bind(this),onPinchEnd:this.#Xe.bind(this),signal:this._uiManager._signal}));this._uiManager._editorUndoBar?.hide();return t}#Ve(){this.#Ae={savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height};this.#o?.toggle(!1);this.parent.togglePointerEvents(!1)}#qe(t,e,i){let s=i/e*.7+1-.7;if(1===s)return;const n=this.#Ue(this.rotation),transf=(t,e)=>[n[0]*t+n[2]*e,n[1]*t+n[3]*e],[r,a]=this.parentDimensions,o=this.x,l=this.y,h=this.width,c=this.height,d=AnnotationEditor.MIN_SIZE/r,u=AnnotationEditor.MIN_SIZE/a;s=Math.max(Math.min(s,1/h,1/c),d/h,u/c);const p=AnnotationEditor._round(h*s),g=AnnotationEditor._round(c*s);if(p===h&&g===c)return;this.#Se||=[o,l,h,c];const f=transf(h/2,c/2),m=AnnotationEditor._round(o+f[0]),b=AnnotationEditor._round(l+f[1]),v=transf(p/2,g/2);this.x=m-v[0];this.y=b-v[1];this.width=p;this.height=g;this.setDims(r*p,a*g);this.fixAndSetPosition();this._onResizing()}#Xe(){this.#o?.toggle(!0);this.parent.togglePointerEvents(!0);this.#Ge()}pointerdown(t){const{isMac:e}=util_FeatureTest.platform;if(0!==t.button||t.ctrlKey&&e)t.preventDefault();else{this.#Ee=!0;this._isDraggable?this.#Ke(t):this.#Ye(t)}}get isSelected(){return this._uiManager.isSelected(this)}#Ye(t){const{isMac:e}=util_FeatureTest.platform;t.ctrlKey&&!e||t.shiftKey||t.metaKey&&e?this.parent.toggleSelected(this):this.parent.setSelected(this)}#Ke(t){const{isSelected:e}=this;this._uiManager.setUpDragSession();let i=!1;const s=new AbortController,n=this._uiManager.combinedSignal(s),r={capture:!0,passive:!1,signal:n},cancelDrag=t=>{s.abort();this.#me=null;this.#Ee=!1;this._uiManager.endDragSession()||this.#Ye(t);i&&this._onStopDragging()};if(e){this.#Pe=t.clientX;this.#ke=t.clientY;this.#me=t.pointerId;this.#be=t.pointerType;window.addEventListener("pointermove",(t=>{if(!i){i=!0;this._onStartDragging()}const{clientX:e,clientY:s,pointerId:n}=t;if(n!==this.#me){stopEvent(t);return}const[r,a]=this.screenToPageTranslation(e-this.#Pe,s-this.#ke);this.#Pe=e;this.#ke=s;this._uiManager.dragSelectedEditors(r,a)}),r);window.addEventListener("touchmove",stopEvent,r);window.addEventListener("pointerdown",(t=>{t.pointerType===this.#be&&(this.#Re||t.isPrimary)&&cancelDrag(t);stopEvent(t)}),r)}const pointerUpCallback=t=>{this.#me&&this.#me!==t.pointerId?stopEvent(t):cancelDrag(t)};window.addEventListener("pointerup",pointerUpCallback,{signal:n});window.addEventListener("blur",pointerUpCallback,{signal:n})}_onStartDragging(){}_onStopDragging(){}moveInDOM(){this.#Me&&clearTimeout(this.#Me);this.#Me=setTimeout((()=>{this.#Me=null;this.parent?.moveEditorInDOM(this)}),0)}_setParentAndPosition(t,e,i){t.changeParent(this);this.x=e;this.y=i;this.fixAndSetPosition();this._onTranslated()}getRect(t,e,i=this.rotation){const s=this.parentScale,[n,r]=this.pageDimensions,[a,o]=this.pageTranslation,l=t/s,h=e/s,c=this.x*n,d=this.y*r,u=this.width*n,p=this.height*r;switch(i){case 0:return[c+l+a,r-d-h-p+o,c+l+u+a,r-d-h+o];case 90:return[c+h+a,r-d+l+o,c+h+p+a,r-d+l+u+o];case 180:return[c-l-u+a,r-d+h+o,c-l+a,r-d+h+p+o];case 270:return[c-h-p+a,r-d-l-u+o,c-h+a,r-d-l+o];default:throw new Error("Invalid rotation")}}getRectInCurrentCoords(t,e){const[i,s,n,r]=t,a=n-i,o=r-s;switch(this.rotation){case 0:return[i,e-r,a,o];case 90:return[i,e-s,o,a];case 180:return[n,e-s,a,o];case 270:return[n,e-r,o,a];default:throw new Error("Invalid rotation")}}onceAdded(t){}isEmpty(){return!1}enableEditMode(){this.#Te=!0}disableEditMode(){this.#Te=!1}isInEditMode(){return this.#Te}shouldGetKeyboardEvents(){return this.#De}needsToBeRebuilt(){return this.div&&!this.isAttachedToDOM}get isOnScreen(){const{top:t,left:e,bottom:i,right:s}=this.getClientDimensions(),{innerHeight:n,innerWidth:r}=window;return e<r&&s>0&&t<n&&i>0}#$e(){if(this.#_e||!this.div)return;this.#_e=new AbortController;const t=this._uiManager.combinedSignal(this.#_e);this.div.addEventListener("focusin",this.focusin.bind(this),{signal:t});this.div.addEventListener("focusout",this.focusout.bind(this),{signal:t})}rebuild(){this.#$e()}rotate(t){}resize(){}serializeDeleted(){return{id:this.annotationElementId,deleted:!0,pageIndex:this.pageIndex,popupRef:this._initialData?.popupRef||""}}serialize(t=!1,e=null){unreachable("An editor must be serializable")}static async deserialize(t,e,i){const s=new this.prototype.constructor({parent:e,id:e.getNextId(),uiManager:i});s.rotation=t.rotation;s.#pe=t.accessibilityData;s._isCopy=t.isCopy||!1;const[n,r]=s.pageDimensions,[a,o,l,h]=s.getRectInCurrentCoords(t.rect,r);s.x=a/n;s.y=o/r;s.width=l/n;s.height=h/r;return s}get hasBeenModified(){return!!this.annotationElementId&&(this.deleted||null!==this.serialize())}remove(){this.#_e?.abort();this.#_e=null;this.isEmpty()||this.commit();this.parent?this.parent.remove(this):this._uiManager.removeEditor(this);if(this.#Me){clearTimeout(this.#Me);this.#Me=null}this.#Oe();this.removeEditToolbar();if(this.#Ie){for(const t of this.#Ie.values())clearTimeout(t);this.#Ie=null}this.parent=null;this.#Re?.destroy();this.#Re=null}get isResizable(){return!1}makeResizable(){if(this.isResizable){this.#He();this.#we.classList.remove("hidden")}}get toolbarPosition(){return null}keydown(t){if(!this.isResizable||t.target!==this.div||"Enter"!==t.key)return;this._uiManager.setSelected(this);this.#Ae={savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height};const e=this.#we.children;if(!this.#ge){this.#ge=Array.from(e);const t=this.#Qe.bind(this),i=this.#Je.bind(this),s=this._uiManager._signal;for(const e of this.#ge){const n=e.getAttribute("data-resizer-name");e.setAttribute("role","spinbutton");e.addEventListener("keydown",t,{signal:s});e.addEventListener("blur",i,{signal:s});e.addEventListener("focus",this.#Ze.bind(this,n),{signal:s});e.setAttribute("data-l10n-id",AnnotationEditor._l10nResizer[n])}}const i=this.#ge[0];let s=0;for(const t of e){if(t===i)break;s++}const n=(360-this.rotation+this.parentRotation)%360/90*(this.#ge.length/4);if(n!==s){if(n<s)for(let t=0;t<s-n;t++)this.#we.append(this.#we.firstChild);else if(n>s)for(let t=0;t<n-s;t++)this.#we.firstChild.before(this.#we.lastChild);let t=0;for(const i of e){const e=this.#ge[t++].getAttribute("data-resizer-name");i.setAttribute("data-l10n-id",AnnotationEditor._l10nResizer[e])}}this.#ti(0);this.#De=!0;this.#we.firstChild.focus({focusVisible:!0});t.preventDefault();t.stopImmediatePropagation()}#Qe(t){AnnotationEditor._resizerKeyboardManager.exec(this,t)}#Je(t){this.#De&&t.relatedTarget?.parentNode!==this.#we&&this.#Oe()}#Ze(t){this.#xe=this.#De?t:""}#ti(t){if(this.#ge)for(const e of this.#ge)e.tabIndex=t}_resizeWithKeyboard(t,e){this.#De&&this.#je(this.#xe,{deltaX:t,deltaY:e,fromKeyboard:!0})}#Oe(){this.#De=!1;this.#ti(-1);this.#Ge()}_stopResizingWithKeyboard(){this.#Oe();this.div.focus()}select(){this.makeResizable();this.div?.classList.add("selectedEditor");if(this._editToolbar){this._editToolbar?.show();this.#o?.toggleAltTextBadge(!1)}else this.addEditToolbar().then((()=>{this.div?.classList.contains("selectedEditor")&&this._editToolbar?.show()}))}unselect(){this.#we?.classList.add("hidden");this.div?.classList.remove("selectedEditor");this.div?.contains(document.activeElement)&&this._uiManager.currentLayer.div.focus({preventScroll:!0});this._editToolbar?.hide();this.#o?.toggleAltTextBadge(!0)}updateParams(t,e){}disableEditing(){}enableEditing(){}enterInEditMode(){}getElementForAltText(){return this.div}get contentDiv(){return this.div}get isEditing(){return this.#Ce}set isEditing(t){this.#Ce=t;if(this.parent)if(t){this.parent.setSelected(this);this.parent.setActiveEditor(this)}else this.parent.setActiveEditor(null)}setAspectRatio(t,e){this.#ve=!0;const i=t/e,{style:s}=this.div;s.aspectRatio=i;s.height="auto"}static get MIN_SIZE(){return 16}static canCreateNewEmptyEditor(){return!0}get telemetryInitialData(){return{action:"added"}}get telemetryFinalData(){return null}_reportTelemetry(t,e=!1){if(e){this.#Ie||=new Map;const{action:e}=t;let i=this.#Ie.get(e);i&&clearTimeout(i);i=setTimeout((()=>{this._reportTelemetry(t);this.#Ie.delete(e);0===this.#Ie.size&&(this.#Ie=null)}),AnnotationEditor._telemetryTimeout);this.#Ie.set(e,i)}else{t.type||=this.editorType;this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:t}})}}show(t=this._isVisible){this.div.classList.toggle("hidden",!t);this._isVisible=t}enable(){this.div&&(this.div.tabIndex=0);this.#fe=!1}disable(){this.div&&(this.div.tabIndex=-1);this.#fe=!0}renderAnnotationElement(t){let e=t.container.querySelector(".annotationContent");if(e){if("CANVAS"===e.nodeName){const t=e;e=document.createElement("div");e.classList.add("annotationContent",this.editorType);t.before(e)}}else{e=document.createElement("div");e.classList.add("annotationContent",this.editorType);t.container.prepend(e)}return e}resetAnnotationElement(t){const{firstChild:e}=t.container;"DIV"===e?.nodeName&&e.classList.contains("annotationContent")&&e.remove()}}class FakeEditor extends AnnotationEditor{constructor(t){super(t);this.annotationElementId=t.annotationElementId;this.deleted=!0}serialize(){return this.serializeDeleted()}}const V=3285377520,q=4294901760,X=65535;class MurmurHash3_64{constructor(t){this.h1=t?4294967295&t:V;this.h2=t?4294967295&t:V}update(t){let e,i;if("string"==typeof t){e=new Uint8Array(2*t.length);i=0;for(let s=0,n=t.length;s<n;s++){const n=t.charCodeAt(s);if(n<=255)e[i++]=n;else{e[i++]=n>>>8;e[i++]=255&n}}}else{if(!ArrayBuffer.isView(t))throw new Error("Invalid data format, must be a string or TypedArray.");e=t.slice();i=e.byteLength}const s=i>>2,n=i-4*s,r=new Uint32Array(e.buffer,0,s);let a=0,o=0,l=this.h1,h=this.h2;const c=3432918353,d=461845907,u=11601,p=13715;for(let t=0;t<s;t++)if(1&t){a=r[t];a=a*c&q|a*u&X;a=a<<15|a>>>17;a=a*d&q|a*p&X;l^=a;l=l<<13|l>>>19;l=5*l+3864292196}else{o=r[t];o=o*c&q|o*u&X;o=o<<15|o>>>17;o=o*d&q|o*p&X;h^=o;h=h<<13|h>>>19;h=5*h+3864292196}a=0;switch(n){case 3:a^=e[4*s+2]<<16;case 2:a^=e[4*s+1]<<8;case 1:a^=e[4*s];a=a*c&q|a*u&X;a=a<<15|a>>>17;a=a*d&q|a*p&X;1&s?l^=a:h^=a}this.h1=l;this.h2=h}hexdigest(){let t=this.h1,e=this.h2;t^=e>>>1;t=3981806797*t&q|36045*t&X;e=4283543511*e&q|(2950163797*(e<<16|t>>>16)&q)>>>16;t^=e>>>1;t=444984403*t&q|60499*t&X;e=3301882366*e&q|(3120437893*(e<<16|t>>>16)&q)>>>16;t^=e>>>1;return(t>>>0).toString(16).padStart(8,"0")+(e>>>0).toString(16).padStart(8,"0")}}const K=Object.freeze({map:null,hash:"",transfer:void 0});class AnnotationStorage{#ei=!1;#ii=null;#si=new Map;constructor(){this.onSetModified=null;this.onResetModified=null;this.onAnnotationEditor=null}getValue(t,e){const i=this.#si.get(t);return void 0===i?e:Object.assign(e,i)}getRawValue(t){return this.#si.get(t)}remove(t){this.#si.delete(t);0===this.#si.size&&this.resetModified();if("function"==typeof this.onAnnotationEditor){for(const t of this.#si.values())if(t instanceof AnnotationEditor)return;this.onAnnotationEditor(null)}}setValue(t,e){const i=this.#si.get(t);let s=!1;if(void 0!==i){for(const[t,n]of Object.entries(e))if(i[t]!==n){s=!0;i[t]=n}}else{s=!0;this.#si.set(t,e)}s&&this.#ni();e instanceof AnnotationEditor&&"function"==typeof this.onAnnotationEditor&&this.onAnnotationEditor(e.constructor._type)}has(t){return this.#si.has(t)}get size(){return this.#si.size}#ni(){if(!this.#ei){this.#ei=!0;"function"==typeof this.onSetModified&&this.onSetModified()}}resetModified(){if(this.#ei){this.#ei=!1;"function"==typeof this.onResetModified&&this.onResetModified()}}get print(){return new PrintAnnotationStorage(this)}get serializable(){if(0===this.#si.size)return K;const t=new Map,e=new MurmurHash3_64,i=[],s=Object.create(null);let n=!1;for(const[i,r]of this.#si){const a=r instanceof AnnotationEditor?r.serialize(!1,s):r;if(a){t.set(i,a);e.update(`${i}:${JSON.stringify(a)}`);n||=!!a.bitmap}}if(n)for(const e of t.values())e.bitmap&&i.push(e.bitmap);return t.size>0?{map:t,hash:e.hexdigest(),transfer:i}:K}get editorStats(){let t=null;const e=new Map;for(const i of this.#si.values()){if(!(i instanceof AnnotationEditor))continue;const s=i.telemetryFinalData;if(!s)continue;const{type:n}=s;e.has(n)||e.set(n,Object.getPrototypeOf(i).constructor);t||=Object.create(null);const r=t[n]||=new Map;for(const[t,e]of Object.entries(s)){if("type"===t)continue;let i=r.get(t);if(!i){i=new Map;r.set(t,i)}const s=i.get(e)??0;i.set(e,s+1)}}for(const[i,s]of e)t[i]=s.computeTelemetryFinalData(t[i]);return t}resetModifiedIds(){this.#ii=null}get modifiedIds(){if(this.#ii)return this.#ii;const t=[];for(const e of this.#si.values())e instanceof AnnotationEditor&&e.annotationElementId&&e.serialize()&&t.push(e.annotationElementId);return this.#ii={ids:new Set(t),hash:t.join(",")}}[Symbol.iterator](){return this.#si.entries()}}class PrintAnnotationStorage extends AnnotationStorage{#ri;constructor(t){super();const{map:e,hash:i,transfer:s}=t.serializable,n=structuredClone(e,s?{transfer:s}:null);this.#ri={map:n,hash:i,transfer:s}}get print(){unreachable("Should not call PrintAnnotationStorage.print")}get serializable(){return this.#ri}get modifiedIds(){return shadow(this,"modifiedIds",{ids:new Set,hash:""})}}class FontLoader{#ai=new Set;constructor({ownerDocument:t=globalThis.document,styleElement:e=null}){this._document=t;this.nativeFontFaces=new Set;this.styleElement=null;this.loadingRequests=[];this.loadTestFontId=0}addNativeFontFace(t){this.nativeFontFaces.add(t);this._document.fonts.add(t)}removeNativeFontFace(t){this.nativeFontFaces.delete(t);this._document.fonts.delete(t)}insertRule(t){if(!this.styleElement){this.styleElement=this._document.createElement("style");this._document.documentElement.getElementsByTagName("head")[0].append(this.styleElement)}const e=this.styleElement.sheet;e.insertRule(t,e.cssRules.length)}clear(){for(const t of this.nativeFontFaces)this._document.fonts.delete(t);this.nativeFontFaces.clear();this.#ai.clear();if(this.styleElement){this.styleElement.remove();this.styleElement=null}}async loadSystemFont({systemFontInfo:t,disableFontFace:e,_inspectFont:i}){if(t&&!this.#ai.has(t.loadedName)){assert(!e,"loadSystemFont shouldn't be called when `disableFontFace` is set.");if(this.isFontLoadingAPISupported){const{loadedName:e,src:s,style:n}=t,r=new FontFace(e,s,n);this.addNativeFontFace(r);try{await r.load();this.#ai.add(e);i?.(t)}catch{warn(`Cannot load system font: ${t.baseFontName}, installing it could help to improve PDF rendering.`);this.removeNativeFontFace(r)}}else unreachable("Not implemented: loadSystemFont without the Font Loading API.")}}async bind(t){if(t.attached||t.missingFile&&!t.systemFontInfo)return;t.attached=!0;if(t.systemFontInfo){await this.loadSystemFont(t);return}if(this.isFontLoadingAPISupported){const e=t.createNativeFontFace();if(e){this.addNativeFontFace(e);try{await e.loaded}catch(i){warn(`Failed to load font '${e.family}': '${i}'.`);t.disableFontFace=!0;throw i}}return}const e=t.createFontFaceRule();if(e){this.insertRule(e);if(this.isSyncFontLoadingSupported)return;await new Promise((e=>{const i=this._queueLoadingCallback(e);this._prepareFontLoadEvent(t,i)}))}}get isFontLoadingAPISupported(){return shadow(this,"isFontLoadingAPISupported",!!this._document?.fonts)}get isSyncFontLoadingSupported(){return shadow(this,"isSyncFontLoadingSupported",i||util_FeatureTest.platform.isFirefox)}_queueLoadingCallback(t){const{loadingRequests:e}=this,i={done:!1,complete:function completeRequest(){assert(!i.done,"completeRequest() cannot be called twice.");i.done=!0;for(;e.length>0&&e[0].done;){const t=e.shift();setTimeout(t.callback,0)}},callback:t};e.push(i);return i}get _loadTestFont(){return shadow(this,"_loadTestFont",atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA=="))}_prepareFontLoadEvent(t,e){function int32(t,e){return t.charCodeAt(e)<<24|t.charCodeAt(e+1)<<16|t.charCodeAt(e+2)<<8|255&t.charCodeAt(e+3)}function spliceString(t,e,i,s){return t.substring(0,e)+s+t.substring(e+i)}let i,s;const n=this._document.createElement("canvas");n.width=1;n.height=1;const r=n.getContext("2d");let a=0;const o=`lt${Date.now()}${this.loadTestFontId++}`;let l=this._loadTestFont;l=spliceString(l,976,o.length,o);const h=1482184792;let c=int32(l,16);for(i=0,s=o.length-3;i<s;i+=4)c=c-h+int32(o,i)|0;i<o.length&&(c=c-h+int32(o+"XXX",i)|0);l=spliceString(l,16,4,function string32(t){return String.fromCharCode(t>>24&255,t>>16&255,t>>8&255,255&t)}(c));const d=`@font-face {font-family:"${o}";src:${`url(data:font/opentype;base64,${btoa(l)});`}}`;this.insertRule(d);const u=this._document.createElement("div");u.style.visibility="hidden";u.style.width=u.style.height="10px";u.style.position="absolute";u.style.top=u.style.left="0px";for(const e of[t.loadedName,o]){const t=this._document.createElement("span");t.textContent="Hi";t.style.fontFamily=e;u.append(t)}this._document.body.append(u);!function isFontReady(t,e){if(++a>30){warn("Load test font never loaded.");e();return}r.font="30px "+t;r.fillText(".",0,20);r.getImageData(0,0,1,1).data[3]>0?e():setTimeout(isFontReady.bind(null,t,e))}(o,(()=>{u.remove();e.complete()}))}}class FontFaceObject{constructor(t,e=null){this.compiledGlyphs=Object.create(null);for(const e in t)this[e]=t[e];this._inspectFont=e}createNativeFontFace(){if(!this.data||this.disableFontFace)return null;let t;if(this.cssFontInfo){const e={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(e.style=`oblique ${this.cssFontInfo.italicAngle}deg`);t=new FontFace(this.cssFontInfo.fontFamily,this.data,e)}else t=new FontFace(this.loadedName,this.data,{});this._inspectFont?.(this);return t}createFontFaceRule(){if(!this.data||this.disableFontFace)return null;const t=`url(data:${this.mimetype};base64,${toBase64Util(this.data)});`;let e;if(this.cssFontInfo){let i=`font-weight: ${this.cssFontInfo.fontWeight};`;this.cssFontInfo.italicAngle&&(i+=`font-style: oblique ${this.cssFontInfo.italicAngle}deg;`);e=`@font-face {font-family:"${this.cssFontInfo.fontFamily}";${i}src:${t}}`}else e=`@font-face {font-family:"${this.loadedName}";src:${t}}`;this._inspectFont?.(this,t);return e}getPathGenerator(t,e){if(void 0!==this.compiledGlyphs[e])return this.compiledGlyphs[e];const i=this.loadedName+"_path_"+e;let s;try{s=t.get(i)}catch(t){warn(`getPathGenerator - ignoring character: "${t}".`)}const n=new Path2D(s||"");this.fontExtraProperties||t.delete(i);return this.compiledGlyphs[e]=n}}const Y=1,Q=2,J=1,Z=2,tt=3,et=4,it=5,st=6,nt=7,rt=8;function onFn(){}function wrapReason(t){if(t instanceof AbortException||t instanceof InvalidPDFException||t instanceof PasswordException||t instanceof ResponseException||t instanceof UnknownErrorException)return t;t instanceof Error||"object"==typeof t&&null!==t||unreachable('wrapReason: Expected "reason" to be a (possibly cloned) Error.');switch(t.name){case"AbortException":return new AbortException(t.message);case"InvalidPDFException":return new InvalidPDFException(t.message);case"PasswordException":return new PasswordException(t.message,t.code);case"ResponseException":return new ResponseException(t.message,t.status,t.missing);case"UnknownErrorException":return new UnknownErrorException(t.message,t.details)}return new UnknownErrorException(t.message,t.toString())}class MessageHandler{#oi=new AbortController;constructor(t,e,i){this.sourceName=t;this.targetName=e;this.comObj=i;this.callbackId=1;this.streamId=1;this.streamSinks=Object.create(null);this.streamControllers=Object.create(null);this.callbackCapabilities=Object.create(null);this.actionHandler=Object.create(null);i.addEventListener("message",this.#li.bind(this),{signal:this.#oi.signal})}#li({data:t}){if(t.targetName!==this.sourceName)return;if(t.stream){this.#hi(t);return}if(t.callback){const e=t.callbackId,i=this.callbackCapabilities[e];if(!i)throw new Error(`Cannot resolve callback ${e}`);delete this.callbackCapabilities[e];if(t.callback===Y)i.resolve(t.data);else{if(t.callback!==Q)throw new Error("Unexpected callback case");i.reject(wrapReason(t.reason))}return}const e=this.actionHandler[t.action];if(!e)throw new Error(`Unknown action from worker: ${t.action}`);if(t.callbackId){const i=this.sourceName,s=t.sourceName,n=this.comObj;Promise.try(e,t.data).then((function(e){n.postMessage({sourceName:i,targetName:s,callback:Y,callbackId:t.callbackId,data:e})}),(function(e){n.postMessage({sourceName:i,targetName:s,callback:Q,callbackId:t.callbackId,reason:wrapReason(e)})}))}else t.streamId?this.#ci(t):e(t.data)}on(t,e){const i=this.actionHandler;if(i[t])throw new Error(`There is already an actionName called "${t}"`);i[t]=e}send(t,e,i){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,data:e},i)}sendWithPromise(t,e,i){const s=this.callbackId++,n=Promise.withResolvers();this.callbackCapabilities[s]=n;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,callbackId:s,data:e},i)}catch(t){n.reject(t)}return n.promise}sendWithStream(t,e,i,s){const n=this.streamId++,r=this.sourceName,a=this.targetName,o=this.comObj;return new ReadableStream({start:i=>{const l=Promise.withResolvers();this.streamControllers[n]={controller:i,startCall:l,pullCall:null,cancelCall:null,isClosed:!1};o.postMessage({sourceName:r,targetName:a,action:t,streamId:n,data:e,desiredSize:i.desiredSize},s);return l.promise},pull:t=>{const e=Promise.withResolvers();this.streamControllers[n].pullCall=e;o.postMessage({sourceName:r,targetName:a,stream:st,streamId:n,desiredSize:t.desiredSize});return e.promise},cancel:t=>{assert(t instanceof Error,"cancel must have a valid reason");const e=Promise.withResolvers();this.streamControllers[n].cancelCall=e;this.streamControllers[n].isClosed=!0;o.postMessage({sourceName:r,targetName:a,stream:J,streamId:n,reason:wrapReason(t)});return e.promise}},i)}#ci(t){const e=t.streamId,i=this.sourceName,s=t.sourceName,n=this.comObj,r=this,a=this.actionHandler[t.action],o={enqueue(t,r=1,a){if(this.isCancelled)return;const o=this.desiredSize;this.desiredSize-=r;if(o>0&&this.desiredSize<=0){this.sinkCapability=Promise.withResolvers();this.ready=this.sinkCapability.promise}n.postMessage({sourceName:i,targetName:s,stream:et,streamId:e,chunk:t},a)},close(){if(!this.isCancelled){this.isCancelled=!0;n.postMessage({sourceName:i,targetName:s,stream:tt,streamId:e});delete r.streamSinks[e]}},error(t){assert(t instanceof Error,"error must have a valid reason");if(!this.isCancelled){this.isCancelled=!0;n.postMessage({sourceName:i,targetName:s,stream:it,streamId:e,reason:wrapReason(t)})}},sinkCapability:Promise.withResolvers(),onPull:null,onCancel:null,isCancelled:!1,desiredSize:t.desiredSize,ready:null};o.sinkCapability.resolve();o.ready=o.sinkCapability.promise;this.streamSinks[e]=o;Promise.try(a,t.data,o).then((function(){n.postMessage({sourceName:i,targetName:s,stream:rt,streamId:e,success:!0})}),(function(t){n.postMessage({sourceName:i,targetName:s,stream:rt,streamId:e,reason:wrapReason(t)})}))}#hi(t){const e=t.streamId,i=this.sourceName,s=t.sourceName,n=this.comObj,r=this.streamControllers[e],a=this.streamSinks[e];switch(t.stream){case rt:t.success?r.startCall.resolve():r.startCall.reject(wrapReason(t.reason));break;case nt:t.success?r.pullCall.resolve():r.pullCall.reject(wrapReason(t.reason));break;case st:if(!a){n.postMessage({sourceName:i,targetName:s,stream:nt,streamId:e,success:!0});break}a.desiredSize<=0&&t.desiredSize>0&&a.sinkCapability.resolve();a.desiredSize=t.desiredSize;Promise.try(a.onPull||onFn).then((function(){n.postMessage({sourceName:i,targetName:s,stream:nt,streamId:e,success:!0})}),(function(t){n.postMessage({sourceName:i,targetName:s,stream:nt,streamId:e,reason:wrapReason(t)})}));break;case et:assert(r,"enqueue should have stream controller");if(r.isClosed)break;r.controller.enqueue(t.chunk);break;case tt:assert(r,"close should have stream controller");if(r.isClosed)break;r.isClosed=!0;r.controller.close();this.#di(r,e);break;case it:assert(r,"error should have stream controller");r.controller.error(wrapReason(t.reason));this.#di(r,e);break;case Z:t.success?r.cancelCall.resolve():r.cancelCall.reject(wrapReason(t.reason));this.#di(r,e);break;case J:if(!a)break;const o=wrapReason(t.reason);Promise.try(a.onCancel||onFn,o).then((function(){n.postMessage({sourceName:i,targetName:s,stream:Z,streamId:e,success:!0})}),(function(t){n.postMessage({sourceName:i,targetName:s,stream:Z,streamId:e,reason:wrapReason(t)})}));a.sinkCapability.reject(o);a.isCancelled=!0;delete this.streamSinks[e];break;default:throw new Error("Unexpected stream case")}}async#di(t,e){await Promise.allSettled([t.startCall?.promise,t.pullCall?.promise,t.cancelCall?.promise]);delete this.streamControllers[e]}destroy(){this.#oi?.abort();this.#oi=null}}class BaseCanvasFactory{#ui=!1;constructor({enableHWA:t=!1}){this.#ui=t}create(t,e){if(t<=0||e<=0)throw new Error("Invalid canvas size");const i=this._createCanvas(t,e);return{canvas:i,context:i.getContext("2d",{willReadFrequently:!this.#ui})}}reset(t,e,i){if(!t.canvas)throw new Error("Canvas is not specified");if(e<=0||i<=0)throw new Error("Invalid canvas size");t.canvas.width=e;t.canvas.height=i}destroy(t){if(!t.canvas)throw new Error("Canvas is not specified");t.canvas.width=0;t.canvas.height=0;t.canvas=null;t.context=null}_createCanvas(t,e){unreachable("Abstract method `_createCanvas` called.")}}class DOMCanvasFactory extends BaseCanvasFactory{constructor({ownerDocument:t=globalThis.document,enableHWA:e=!1}){super({enableHWA:e});this._document=t}_createCanvas(t,e){const i=this._document.createElement("canvas");i.width=t;i.height=e;return i}}class BaseCMapReaderFactory{constructor({baseUrl:t=null,isCompressed:e=!0}){this.baseUrl=t;this.isCompressed=e}async fetch({name:t}){if(!this.baseUrl)throw new Error("Ensure that the `cMapUrl` and `cMapPacked` API parameters are provided.");if(!t)throw new Error("CMap name must be specified.");const e=this.baseUrl+t+(this.isCompressed?".bcmap":"");return this._fetch(e).then((t=>({cMapData:t,isCompressed:this.isCompressed}))).catch((t=>{throw new Error(`Unable to load ${this.isCompressed?"binary ":""}CMap at: ${e}`)}))}async _fetch(t){unreachable("Abstract method `_fetch` called.")}}class DOMCMapReaderFactory extends BaseCMapReaderFactory{async _fetch(t){const e=await fetchData(t,this.isCompressed?"arraybuffer":"text");return e instanceof ArrayBuffer?new Uint8Array(e):stringToBytes(e)}}__webpack_require__(2489);class BaseFilterFactory{addFilter(t){return"none"}addHCMFilter(t,e){return"none"}addAlphaFilter(t){return"none"}addLuminosityFilter(t){return"none"}addHighlightHCMFilter(t,e,i,s,n){return"none"}destroy(t=!1){}}class DOMFilterFactory extends BaseFilterFactory{#pi;#gi;#fi;#mi;#bi;#vi;#y=0;constructor({docId:t,ownerDocument:e=globalThis.document}){super();this.#mi=t;this.#bi=e}get#_(){return this.#gi||=new Map}get#wi(){return this.#vi||=new Map}get#yi(){if(!this.#fi){const t=this.#bi.createElement("div"),{style:e}=t;e.visibility="hidden";e.contain="strict";e.width=e.height=0;e.position="absolute";e.top=e.left=0;e.zIndex=-1;const i=this.#bi.createElementNS(W,"svg");i.setAttribute("width",0);i.setAttribute("height",0);this.#fi=this.#bi.createElementNS(W,"defs");t.append(i);i.append(this.#fi);this.#bi.body.append(t)}return this.#fi}#Ai(t){if(1===t.length){const e=t[0],i=new Array(256);for(let t=0;t<256;t++)i[t]=e[t]/255;const s=i.join(",");return[s,s,s]}const[e,i,s]=t,n=new Array(256),r=new Array(256),a=new Array(256);for(let t=0;t<256;t++){n[t]=e[t]/255;r[t]=i[t]/255;a[t]=s[t]/255}return[n.join(","),r.join(","),a.join(",")]}#_i(t){if(void 0===this.#pi){this.#pi="";const t=this.#bi.URL;t!==this.#bi.baseURI&&(isDataScheme(t)?warn('#createUrl: ignore "data:"-URL for performance reasons.'):this.#pi=updateUrlHash(t,""))}return`url(${this.#pi}#${t})`}addFilter(t){if(!t)return"none";let e=this.#_.get(t);if(e)return e;const[i,s,n]=this.#Ai(t),r=1===t.length?i:`${i}${s}${n}`;e=this.#_.get(r);if(e){this.#_.set(t,e);return e}const a=`g_${this.#mi}_transfer_map_${this.#y++}`,o=this.#_i(a);this.#_.set(t,o);this.#_.set(r,o);const l=this.#xi(a);this.#Ei(i,s,n,l);return o}addHCMFilter(t,e){const i=`${t}-${e}`,s="base";let n=this.#wi.get(s);if(n?.key===i)return n.url;if(n){n.filter?.remove();n.key=i;n.url="none";n.filter=null}else{n={key:i,url:"none",filter:null};this.#wi.set(s,n)}if(!t||!e)return n.url;const r=this.#Si(t);t=Util.makeHexColor(...r);const a=this.#Si(e);e=Util.makeHexColor(...a);this.#yi.style.color="";if("#000000"===t&&"#ffffff"===e||t===e)return n.url;const o=new Array(256);for(let t=0;t<=255;t++){const e=t/255;o[t]=e<=.03928?e/12.92:((e+.055)/1.055)**2.4}const l=o.join(","),h=`g_${this.#mi}_hcm_filter`,c=n.filter=this.#xi(h);this.#Ei(l,l,l,c);this.#Ci(c);const getSteps=(t,e)=>{const i=r[t]/255,s=a[t]/255,n=new Array(e+1);for(let t=0;t<=e;t++)n[t]=i+t/e*(s-i);return n.join(",")};this.#Ei(getSteps(0,5),getSteps(1,5),getSteps(2,5),c);n.url=this.#_i(h);return n.url}addAlphaFilter(t){let e=this.#_.get(t);if(e)return e;const[i]=this.#Ai([t]),s=`alpha_${i}`;e=this.#_.get(s);if(e){this.#_.set(t,e);return e}const n=`g_${this.#mi}_alpha_map_${this.#y++}`,r=this.#_i(n);this.#_.set(t,r);this.#_.set(s,r);const a=this.#xi(n);this.#Ti(i,a);return r}addLuminosityFilter(t){let e,i,s=this.#_.get(t||"luminosity");if(s)return s;if(t){[e]=this.#Ai([t]);i=`luminosity_${e}`}else i="luminosity";s=this.#_.get(i);if(s){this.#_.set(t,s);return s}const n=`g_${this.#mi}_luminosity_map_${this.#y++}`,r=this.#_i(n);this.#_.set(t,r);this.#_.set(i,r);const a=this.#xi(n);this.#Di(a);t&&this.#Ti(e,a);return r}addHighlightHCMFilter(t,e,i,s,n){const r=`${e}-${i}-${s}-${n}`;let a=this.#wi.get(t);if(a?.key===r)return a.url;if(a){a.filter?.remove();a.key=r;a.url="none";a.filter=null}else{a={key:r,url:"none",filter:null};this.#wi.set(t,a)}if(!e||!i)return a.url;const[o,l]=[e,i].map(this.#Si.bind(this));let h=Math.round(.2126*o[0]+.7152*o[1]+.0722*o[2]),c=Math.round(.2126*l[0]+.7152*l[1]+.0722*l[2]),[d,u]=[s,n].map(this.#Si.bind(this));c<h&&([h,c,d,u]=[c,h,u,d]);this.#yi.style.color="";const getSteps=(t,e,i)=>{const s=new Array(256),n=(c-h)/i,r=t/255,a=(e-t)/(255*i);let o=0;for(let t=0;t<=i;t++){const e=Math.round(h+t*n),i=r+t*a;for(let t=o;t<=e;t++)s[t]=i;o=e+1}for(let t=o;t<256;t++)s[t]=s[o-1];return s.join(",")},p=`g_${this.#mi}_hcm_${t}_filter`,g=a.filter=this.#xi(p);this.#Ci(g);this.#Ei(getSteps(d[0],u[0],5),getSteps(d[1],u[1],5),getSteps(d[2],u[2],5),g);a.url=this.#_i(p);return a.url}destroy(t=!1){if(!t||!this.#vi?.size){this.#fi?.parentNode.parentNode.remove();this.#fi=null;this.#gi?.clear();this.#gi=null;this.#vi?.clear();this.#vi=null;this.#y=0}}#Di(t){const e=this.#bi.createElementNS(W,"feColorMatrix");e.setAttribute("type","matrix");e.setAttribute("values","0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0.59 0.11 0 0");t.append(e)}#Ci(t){const e=this.#bi.createElementNS(W,"feColorMatrix");e.setAttribute("type","matrix");e.setAttribute("values","0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0 0 0 1 0");t.append(e)}#xi(t){const e=this.#bi.createElementNS(W,"filter");e.setAttribute("color-interpolation-filters","sRGB");e.setAttribute("id",t);this.#yi.append(e);return e}#Mi(t,e,i){const s=this.#bi.createElementNS(W,e);s.setAttribute("type","discrete");s.setAttribute("tableValues",i);t.append(s)}#Ei(t,e,i,s){const n=this.#bi.createElementNS(W,"feComponentTransfer");s.append(n);this.#Mi(n,"feFuncR",t);this.#Mi(n,"feFuncG",e);this.#Mi(n,"feFuncB",i)}#Ti(t,e){const i=this.#bi.createElementNS(W,"feComponentTransfer");e.append(i);this.#Mi(i,"feFuncA",t)}#Si(t){this.#yi.style.color=t;return getRGB(getComputedStyle(this.#yi).getPropertyValue("color"))}}class BaseStandardFontDataFactory{constructor({baseUrl:t=null}){this.baseUrl=t}async fetch({filename:t}){if(!this.baseUrl)throw new Error("Ensure that the `standardFontDataUrl` API parameter is provided.");if(!t)throw new Error("Font filename must be specified.");const e=`${this.baseUrl}${t}`;return this._fetch(e).catch((t=>{throw new Error(`Unable to load font data at: ${e}`)}))}async _fetch(t){unreachable("Abstract method `_fetch` called.")}}class DOMStandardFontDataFactory extends BaseStandardFontDataFactory{async _fetch(t){const e=await fetchData(t,"arraybuffer");return new Uint8Array(e)}}class BaseWasmFactory{constructor({baseUrl:t=null}){this.baseUrl=t}async fetch({filename:t}){if(!this.baseUrl)throw new Error("Ensure that the `wasmUrl` API parameter is provided.");if(!t)throw new Error("Wasm filename must be specified.");const e=`${this.baseUrl}${t}`;return this._fetch(e).catch((t=>{throw new Error(`Unable to load wasm data at: ${e}`)}))}async _fetch(t){unreachable("Abstract method `_fetch` called.")}}class DOMWasmFactory extends BaseWasmFactory{async _fetch(t){const e=await fetchData(t,"arraybuffer");return new Uint8Array(e)}}if(i){let t;try{const e=process.getBuiltinModule("module").createRequire(import.meta.url);try{t=e("@napi-rs/canvas")}catch(t){warn(`Cannot load "@napi-rs/canvas" package: "${t}".`)}}catch(t){warn(`Cannot access the \`require\` function: "${t}".`)}globalThis.DOMMatrix||(t?.DOMMatrix?globalThis.DOMMatrix=t.DOMMatrix:warn("Cannot polyfill `DOMMatrix`, rendering may be broken."));globalThis.ImageData||(t?.ImageData?globalThis.ImageData=t.ImageData:warn("Cannot polyfill `ImageData`, rendering may be broken."));globalThis.Path2D||(t?.Path2D?globalThis.Path2D=t.Path2D:warn("Cannot polyfill `Path2D`, rendering may be broken."))}async function node_utils_fetchData(t){const e=process.getBuiltinModule("fs"),i=await e.promises.readFile(t);return new Uint8Array(i)}class NodeFilterFactory extends BaseFilterFactory{}class NodeCanvasFactory extends BaseCanvasFactory{_createCanvas(t,e){return process.getBuiltinModule("module").createRequire(import.meta.url)("@napi-rs/canvas").createCanvas(t,e)}}class NodeCMapReaderFactory extends BaseCMapReaderFactory{async _fetch(t){return node_utils_fetchData(t)}}class NodeStandardFontDataFactory extends BaseStandardFontDataFactory{async _fetch(t){return node_utils_fetchData(t)}}class NodeWasmFactory extends BaseWasmFactory{async _fetch(t){return node_utils_fetchData(t)}}const ot="Fill",lt="Stroke",ht="Shading";function applyBoundingBox(t,e){if(!e)return;const i=e[2]-e[0],s=e[3]-e[1],n=new Path2D;n.rect(e[0],e[1],i,s);t.clip(n)}class BaseShadingPattern{isModifyingCurrentTransform(){return!1}getPattern(){unreachable("Abstract method `getPattern` called.")}}class RadialAxialShadingPattern extends BaseShadingPattern{constructor(t){super();this._type=t[1];this._bbox=t[2];this._colorStops=t[3];this._p0=t[4];this._p1=t[5];this._r0=t[6];this._r1=t[7];this.matrix=null}_createGradient(t){let e;"axial"===this._type?e=t.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):"radial"===this._type&&(e=t.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1));for(const t of this._colorStops)e.addColorStop(t[0],t[1]);return e}getPattern(t,e,i,s){let n;if(s===lt||s===ot){const r=e.current.getClippedPathBoundingBox(s,getCurrentTransform(t))||[0,0,0,0],a=Math.ceil(r[2]-r[0])||1,o=Math.ceil(r[3]-r[1])||1,l=e.cachedCanvases.getCanvas("pattern",a,o),h=l.context;h.clearRect(0,0,h.canvas.width,h.canvas.height);h.beginPath();h.rect(0,0,h.canvas.width,h.canvas.height);h.translate(-r[0],-r[1]);i=Util.transform(i,[1,0,0,1,r[0],r[1]]);h.transform(...e.baseTransform);this.matrix&&h.transform(...this.matrix);applyBoundingBox(h,this._bbox);h.fillStyle=this._createGradient(h);h.fill();n=t.createPattern(l.canvas,"no-repeat");const c=new DOMMatrix(i);n.setTransform(c)}else{applyBoundingBox(t,this._bbox);n=this._createGradient(t)}return n}}function drawTriangle(t,e,i,s,n,r,a,o){const l=e.coords,h=e.colors,c=t.data,d=4*t.width;let u;if(l[i+1]>l[s+1]){u=i;i=s;s=u;u=r;r=a;a=u}if(l[s+1]>l[n+1]){u=s;s=n;n=u;u=a;a=o;o=u}if(l[i+1]>l[s+1]){u=i;i=s;s=u;u=r;r=a;a=u}const p=(l[i]+e.offsetX)*e.scaleX,g=(l[i+1]+e.offsetY)*e.scaleY,f=(l[s]+e.offsetX)*e.scaleX,m=(l[s+1]+e.offsetY)*e.scaleY,b=(l[n]+e.offsetX)*e.scaleX,v=(l[n+1]+e.offsetY)*e.scaleY;if(g>=v)return;const w=h[r],y=h[r+1],A=h[r+2],_=h[a],x=h[a+1],E=h[a+2],S=h[o],C=h[o+1],T=h[o+2],D=Math.round(g),M=Math.round(v);let P,k,I,R,L,O,N,B;for(let t=D;t<=M;t++){if(t<m){const e=t<g?0:(g-t)/(g-m);P=p-(p-f)*e;k=w-(w-_)*e;I=y-(y-x)*e;R=A-(A-E)*e}else{let e;e=t>v?1:m===v?0:(m-t)/(m-v);P=f-(f-b)*e;k=_-(_-S)*e;I=x-(x-C)*e;R=E-(E-T)*e}let e;e=t<g?0:t>v?1:(g-t)/(g-v);L=p-(p-b)*e;O=w-(w-S)*e;N=y-(y-C)*e;B=A-(A-T)*e;const i=Math.round(Math.min(P,L)),s=Math.round(Math.max(P,L));let n=d*t+4*i;for(let t=i;t<=s;t++){e=(P-t)/(P-L);e<0?e=0:e>1&&(e=1);c[n++]=k-(k-O)*e|0;c[n++]=I-(I-N)*e|0;c[n++]=R-(R-B)*e|0;c[n++]=255}}}function drawFigure(t,e,i){const s=e.coords,n=e.colors;let r,a;switch(e.type){case"lattice":const o=e.verticesPerRow,l=Math.floor(s.length/o)-1,h=o-1;for(r=0;r<l;r++){let e=r*o;for(let r=0;r<h;r++,e++){drawTriangle(t,i,s[e],s[e+1],s[e+o],n[e],n[e+1],n[e+o]);drawTriangle(t,i,s[e+o+1],s[e+1],s[e+o],n[e+o+1],n[e+1],n[e+o])}}break;case"triangles":for(r=0,a=s.length;r<a;r+=3)drawTriangle(t,i,s[r],s[r+1],s[r+2],n[r],n[r+1],n[r+2]);break;default:throw new Error("illegal figure")}}class MeshShadingPattern extends BaseShadingPattern{constructor(t){super();this._coords=t[2];this._colors=t[3];this._figures=t[4];this._bounds=t[5];this._bbox=t[6];this._background=t[7];this.matrix=null}_createMeshCanvas(t,e,i){const s=Math.floor(this._bounds[0]),n=Math.floor(this._bounds[1]),r=Math.ceil(this._bounds[2])-s,a=Math.ceil(this._bounds[3])-n,o=Math.min(Math.ceil(Math.abs(r*t[0]*1.1)),3e3),l=Math.min(Math.ceil(Math.abs(a*t[1]*1.1)),3e3),h=r/o,c=a/l,d={coords:this._coords,colors:this._colors,offsetX:-s,offsetY:-n,scaleX:1/h,scaleY:1/c},u=o+4,p=l+4,g=i.getCanvas("mesh",u,p),f=g.context,m=f.createImageData(o,l);if(e){const t=m.data;for(let i=0,s=t.length;i<s;i+=4){t[i]=e[0];t[i+1]=e[1];t[i+2]=e[2];t[i+3]=255}}for(const t of this._figures)drawFigure(m,t,d);f.putImageData(m,2,2);return{canvas:g.canvas,offsetX:s-2*h,offsetY:n-2*c,scaleX:h,scaleY:c}}isModifyingCurrentTransform(){return!0}getPattern(t,e,i,s){applyBoundingBox(t,this._bbox);const n=new Float32Array(2);if(s===ht)Util.singularValueDecompose2dScale(getCurrentTransform(t),n);else if(this.matrix){Util.singularValueDecompose2dScale(this.matrix,n);const[t,i]=n;Util.singularValueDecompose2dScale(e.baseTransform,n);n[0]*=t;n[1]*=i}else Util.singularValueDecompose2dScale(e.baseTransform,n);const r=this._createMeshCanvas(n,s===ht?null:this._background,e.cachedCanvases);if(s!==ht){t.setTransform(...e.baseTransform);this.matrix&&t.transform(...this.matrix)}t.translate(r.offsetX,r.offsetY);t.scale(r.scaleX,r.scaleY);return t.createPattern(r.canvas,"no-repeat")}}class DummyShadingPattern extends BaseShadingPattern{getPattern(){return"hotpink"}}const ct=1,dt=2;class TilingPattern{static MAX_PATTERN_SIZE=3e3;constructor(t,e,i,s){this.color=t[1];this.operatorList=t[2];this.matrix=t[3];this.bbox=t[4];this.xstep=t[5];this.ystep=t[6];this.paintType=t[7];this.tilingType=t[8];this.ctx=e;this.canvasGraphicsFactory=i;this.baseTransform=s}createPatternCanvas(t){const{bbox:e,operatorList:i,paintType:s,tilingType:n,color:r,canvasGraphicsFactory:a}=this;let{xstep:o,ystep:l}=this;o=Math.abs(o);l=Math.abs(l);info("TilingType: "+n);const h=e[0],c=e[1],d=e[2],u=e[3],p=d-h,g=u-c,f=new Float32Array(2);Util.singularValueDecompose2dScale(this.matrix,f);const[m,b]=f;Util.singularValueDecompose2dScale(this.baseTransform,f);const v=m*f[0],w=b*f[1];let y=p,A=g,_=!1,x=!1;const E=Math.ceil(o*v),S=Math.ceil(l*w);E>=Math.ceil(p*v)?y=o:_=!0;S>=Math.ceil(g*w)?A=l:x=!0;const C=this.getSizeAndScale(y,this.ctx.canvas.width,v),T=this.getSizeAndScale(A,this.ctx.canvas.height,w),D=t.cachedCanvases.getCanvas("pattern",C.size,T.size),M=D.context,P=a.createCanvasGraphics(M);P.groupLevel=t.groupLevel;this.setFillAndStrokeStyleToContext(P,s,r);M.translate(-C.scale*h,-T.scale*c);P.transform(C.scale,0,0,T.scale,0,0);M.save();this.clipBbox(P,h,c,d,u);P.baseTransform=getCurrentTransform(P.ctx);P.executeOperatorList(i);P.endDrawing();M.restore();if(_||x){const e=D.canvas;_&&(y=o);x&&(A=l);const i=this.getSizeAndScale(y,this.ctx.canvas.width,v),s=this.getSizeAndScale(A,this.ctx.canvas.height,w),n=i.size,r=s.size,a=t.cachedCanvases.getCanvas("pattern-workaround",n,r),d=a.context,u=_?Math.floor(p/o):0,f=x?Math.floor(g/l):0;for(let t=0;t<=u;t++)for(let i=0;i<=f;i++)d.drawImage(e,n*t,r*i,n,r,0,0,n,r);return{canvas:a.canvas,scaleX:i.scale,scaleY:s.scale,offsetX:h,offsetY:c}}return{canvas:D.canvas,scaleX:C.scale,scaleY:T.scale,offsetX:h,offsetY:c}}getSizeAndScale(t,e,i){const s=Math.max(TilingPattern.MAX_PATTERN_SIZE,e);let n=Math.ceil(t*i);n>=s?n=s:i=n/t;return{scale:i,size:n}}clipBbox(t,e,i,s,n){const r=s-e,a=n-i;t.ctx.rect(e,i,r,a);Util.axialAlignedBoundingBox([e,i,s,n],getCurrentTransform(t.ctx),t.current.minMax);t.clip();t.endPath()}setFillAndStrokeStyleToContext(t,e,i){const s=t.ctx,n=t.current;switch(e){case ct:const t=this.ctx;s.fillStyle=t.fillStyle;s.strokeStyle=t.strokeStyle;n.fillColor=t.fillStyle;n.strokeColor=t.strokeStyle;break;case dt:const r=Util.makeHexColor(i[0],i[1],i[2]);s.fillStyle=r;s.strokeStyle=r;n.fillColor=r;n.strokeColor=r;break;default:throw new FormatError(`Unsupported paint type: ${e}`)}}isModifyingCurrentTransform(){return!1}getPattern(t,e,i,s){let n=i;if(s!==ht){n=Util.transform(n,e.baseTransform);this.matrix&&(n=Util.transform(n,this.matrix))}const r=this.createPatternCanvas(e);let a=new DOMMatrix(n);a=a.translate(r.offsetX,r.offsetY);a=a.scale(1/r.scaleX,1/r.scaleY);const o=t.createPattern(r.canvas,"repeat");o.setTransform(a);return o}}function convertBlackAndWhiteToRGBA({src:t,srcPos:e=0,dest:i,width:s,height:n,nonBlackColor:r=4294967295,inverseDecode:a=!1}){const o=util_FeatureTest.isLittleEndian?4278190080:255,[l,h]=a?[r,o]:[o,r],c=s>>3,d=7&s,u=t.length;i=new Uint32Array(i.buffer);let p=0;for(let s=0;s<n;s++){for(const s=e+c;e<s;e++){const s=e<u?t[e]:255;i[p++]=128&s?h:l;i[p++]=64&s?h:l;i[p++]=32&s?h:l;i[p++]=16&s?h:l;i[p++]=8&s?h:l;i[p++]=4&s?h:l;i[p++]=2&s?h:l;i[p++]=1&s?h:l}if(0===d)continue;const s=e<u?t[e++]:255;for(let t=0;t<d;t++)i[p++]=s&1<<7-t?h:l}return{srcPos:e,destPos:p}}const ut=16,pt=new DOMMatrix,gt=new Float32Array(2),ft=new Float32Array([1/0,1/0,-1/0,-1/0]);class CachedCanvases{constructor(t){this.canvasFactory=t;this.cache=Object.create(null)}getCanvas(t,e,i){let s;if(void 0!==this.cache[t]){s=this.cache[t];this.canvasFactory.reset(s,e,i)}else{s=this.canvasFactory.create(e,i);this.cache[t]=s}return s}delete(t){delete this.cache[t]}clear(){for(const t in this.cache){const e=this.cache[t];this.canvasFactory.destroy(e);delete this.cache[t]}}}function drawImageAtIntegerCoords(t,e,i,s,n,r,a,o,l,h){const[c,d,u,p,g,f]=getCurrentTransform(t);if(0===d&&0===u){const m=a*c+g,b=Math.round(m),v=o*p+f,w=Math.round(v),y=(a+l)*c+g,A=Math.abs(Math.round(y)-b)||1,_=(o+h)*p+f,x=Math.abs(Math.round(_)-w)||1;t.setTransform(Math.sign(c),0,0,Math.sign(p),b,w);t.drawImage(e,i,s,n,r,0,0,A,x);t.setTransform(c,d,u,p,g,f);return[A,x]}if(0===c&&0===p){const m=o*u+g,b=Math.round(m),v=a*d+f,w=Math.round(v),y=(o+h)*u+g,A=Math.abs(Math.round(y)-b)||1,_=(a+l)*d+f,x=Math.abs(Math.round(_)-w)||1;t.setTransform(0,Math.sign(d),Math.sign(u),0,b,w);t.drawImage(e,i,s,n,r,0,0,x,A);t.setTransform(c,d,u,p,g,f);return[x,A]}t.drawImage(e,i,s,n,r,a,o,l,h);return[Math.hypot(c,d)*l,Math.hypot(u,p)*h]}class CanvasExtraState{alphaIsShape=!1;fontSize=0;fontSizeScale=1;textMatrix=null;textMatrixScale=1;fontMatrix=s;leading=0;x=0;y=0;lineX=0;lineY=0;charSpacing=0;wordSpacing=0;textHScale=1;textRenderingMode=b;textRise=0;fillColor="#000000";strokeColor="#000000";patternFill=!1;patternStroke=!1;fillAlpha=1;strokeAlpha=1;lineWidth=1;activeSMask=null;transferMaps="none";constructor(t,e){this.clipBox=new Float32Array([0,0,t,e]);this.minMax=ft.slice()}clone(){const t=Object.create(this);t.clipBox=this.clipBox.slice();t.minMax=this.minMax.slice();return t}getPathBoundingBox(t=ot,e=null){const i=this.minMax.slice();if(t===lt){e||unreachable("Stroke bounding box must include transform.");Util.singularValueDecompose2dScale(e,gt);const t=gt[0]*this.lineWidth/2,s=gt[1]*this.lineWidth/2;i[0]-=t;i[1]-=s;i[2]+=t;i[3]+=s}return i}updateClipFromPath(){const t=Util.intersect(this.clipBox,this.getPathBoundingBox());this.startNewPathAndClipBox(t||[0,0,0,0])}isEmptyClip(){return this.minMax[0]===1/0}startNewPathAndClipBox(t){this.clipBox.set(t,0);this.minMax.set(ft,0)}getClippedPathBoundingBox(t=ot,e=null){return Util.intersect(this.clipBox,this.getPathBoundingBox(t,e))}}function putBinaryImageData(t,e){if(e instanceof ImageData){t.putImageData(e,0,0);return}const i=e.height,s=e.width,n=i%ut,r=(i-n)/ut,a=0===n?r:r+1,o=t.createImageData(s,ut);let l,h=0;const c=e.data,d=o.data;let u,p,g,f;if(e.kind===x.GRAYSCALE_1BPP){const e=c.byteLength,i=new Uint32Array(d.buffer,0,d.byteLength>>2),f=i.length,m=s+7>>3,b=4294967295,v=util_FeatureTest.isLittleEndian?4278190080:255;for(u=0;u<a;u++){g=u<r?ut:n;l=0;for(p=0;p<g;p++){const t=e-h;let n=0;const r=t>m?s:8*t-7,a=-8&r;let o=0,d=0;for(;n<a;n+=8){d=c[h++];i[l++]=128&d?b:v;i[l++]=64&d?b:v;i[l++]=32&d?b:v;i[l++]=16&d?b:v;i[l++]=8&d?b:v;i[l++]=4&d?b:v;i[l++]=2&d?b:v;i[l++]=1&d?b:v}for(;n<r;n++){if(0===o){d=c[h++];o=128}i[l++]=d&o?b:v;o>>=1}}for(;l<f;)i[l++]=0;t.putImageData(o,0,u*ut)}}else if(e.kind===x.RGBA_32BPP){p=0;f=s*ut*4;for(u=0;u<r;u++){d.set(c.subarray(h,h+f));h+=f;t.putImageData(o,0,p);p+=ut}if(u<a){f=s*n*4;d.set(c.subarray(h,h+f));t.putImageData(o,0,p)}}else{if(e.kind!==x.RGB_24BPP)throw new Error(`bad image kind: ${e.kind}`);g=ut;f=s*g;for(u=0;u<a;u++){if(u>=r){g=n;f=s*g}l=0;for(p=f;p--;){d[l++]=c[h++];d[l++]=c[h++];d[l++]=c[h++];d[l++]=255}t.putImageData(o,0,u*ut)}}}function putBinaryImageMask(t,e){if(e.bitmap){t.drawImage(e.bitmap,0,0);return}const i=e.height,s=e.width,n=i%ut,r=(i-n)/ut,a=0===n?r:r+1,o=t.createImageData(s,ut);let l=0;const h=e.data,c=o.data;for(let e=0;e<a;e++){const i=e<r?ut:n;({srcPos:l}=convertBlackAndWhiteToRGBA({src:h,srcPos:l,dest:c,width:s,height:i,nonBlackColor:0}));t.putImageData(o,0,e*ut)}}function copyCtxState(t,e){const i=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font","filter"];for(const s of i)void 0!==t[s]&&(e[s]=t[s]);if(void 0!==t.setLineDash){e.setLineDash(t.getLineDash());e.lineDashOffset=t.lineDashOffset}}function resetCtxToDefault(t){t.strokeStyle=t.fillStyle="#000000";t.fillRule="nonzero";t.globalAlpha=1;t.lineWidth=1;t.lineCap="butt";t.lineJoin="miter";t.miterLimit=10;t.globalCompositeOperation="source-over";t.font="10px sans-serif";if(void 0!==t.setLineDash){t.setLineDash([]);t.lineDashOffset=0}const{filter:e}=t;"none"!==e&&""!==e&&(t.filter="none")}function getImageSmoothingEnabled(t,e){if(e)return!0;Util.singularValueDecompose2dScale(t,gt);const i=Math.fround(OutputScale.pixelRatio*PixelsPerInch.PDF_TO_CSS_UNITS);return gt[0]<=i&&gt[1]<=i}const mt=["butt","round","square"],bt=["miter","round","bevel"],vt={},wt={};class CanvasGraphics{constructor(t,e,i,s,n,{optionalContentConfig:r,markedContentStack:a=null},o,l){this.ctx=t;this.current=new CanvasExtraState(this.ctx.canvas.width,this.ctx.canvas.height);this.stateStack=[];this.pendingClip=null;this.pendingEOFill=!1;this.res=null;this.xobjs=null;this.commonObjs=e;this.objs=i;this.canvasFactory=s;this.filterFactory=n;this.groupStack=[];this.baseTransform=null;this.baseTransformStack=[];this.groupLevel=0;this.smaskStack=[];this.smaskCounter=0;this.tempSMask=null;this.suspendedCtx=null;this.contentVisible=!0;this.markedContentStack=a||[];this.optionalContentConfig=r;this.cachedCanvases=new CachedCanvases(this.canvasFactory);this.cachedPatterns=new Map;this.annotationCanvasMap=o;this.viewportScale=1;this.outputScaleX=1;this.outputScaleY=1;this.pageColors=l;this._cachedScaleForStroking=[-1,0];this._cachedGetSinglePixelWidth=null;this._cachedBitmapsMap=new Map}getObject(t,e=null){return"string"==typeof t?t.startsWith("g_")?this.commonObjs.get(t):this.objs.get(t):e}beginDrawing({transform:t,viewport:e,transparency:i=!1,background:s=null}){const n=this.ctx.canvas.width,r=this.ctx.canvas.height,a=this.ctx.fillStyle;this.ctx.fillStyle=s||"#ffffff";this.ctx.fillRect(0,0,n,r);this.ctx.fillStyle=a;if(i){const t=this.cachedCanvases.getCanvas("transparent",n,r);this.compositeCtx=this.ctx;this.transparentCanvas=t.canvas;this.ctx=t.context;this.ctx.save();this.ctx.transform(...getCurrentTransform(this.compositeCtx))}this.ctx.save();resetCtxToDefault(this.ctx);if(t){this.ctx.transform(...t);this.outputScaleX=t[0];this.outputScaleY=t[0]}this.ctx.transform(...e.transform);this.viewportScale=e.scale;this.baseTransform=getCurrentTransform(this.ctx)}executeOperatorList(t,e,i,s){const n=t.argsArray,r=t.fnArray;let a=e||0;const o=n.length;if(o===a)return a;const l=o-a>10&&"function"==typeof i,h=l?Date.now()+15:0;let c=0;const d=this.commonObjs,u=this.objs;let p;for(;;){if(void 0!==s&&a===s.nextBreakPoint){s.breakIt(a,i);return a}p=r[a];if(p!==k.dependency)this[p].apply(this,n[a]);else for(const t of n[a]){const e=t.startsWith("g_")?d:u;if(!e.has(t)){e.get(t,i);return a}}a++;if(a===o)return a;if(l&&++c>10){if(Date.now()>h){i();return a}c=0}}}#Pi(){for(;this.stateStack.length||this.inSMaskMode;)this.restore();this.current.activeSMask=null;this.ctx.restore();if(this.transparentCanvas){this.ctx=this.compositeCtx;this.ctx.save();this.ctx.setTransform(1,0,0,1,0,0);this.ctx.drawImage(this.transparentCanvas,0,0);this.ctx.restore();this.transparentCanvas=null}}endDrawing(){this.#Pi();this.cachedCanvases.clear();this.cachedPatterns.clear();for(const t of this._cachedBitmapsMap.values()){for(const e of t.values())"undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement&&(e.width=e.height=0);t.clear()}this._cachedBitmapsMap.clear();this.#ki()}#ki(){if(this.pageColors){const t=this.filterFactory.addHCMFilter(this.pageColors.foreground,this.pageColors.background);if("none"!==t){const e=this.ctx.filter;this.ctx.filter=t;this.ctx.drawImage(this.ctx.canvas,0,0);this.ctx.filter=e}}}_scaleImage(t,e){const i=t.width??t.displayWidth,s=t.height??t.displayHeight;let n,r,a=Math.max(Math.hypot(e[0],e[1]),1),o=Math.max(Math.hypot(e[2],e[3]),1),l=i,h=s,c="prescale1";for(;a>2&&l>1||o>2&&h>1;){let e=l,i=h;if(a>2&&l>1){e=l>=16384?Math.floor(l/2)-1||1:Math.ceil(l/2);a/=l/e}if(o>2&&h>1){i=h>=16384?Math.floor(h/2)-1||1:Math.ceil(h)/2;o/=h/i}n=this.cachedCanvases.getCanvas(c,e,i);r=n.context;r.clearRect(0,0,e,i);r.drawImage(t,0,0,l,h,0,0,e,i);t=n.canvas;l=e;h=i;c="prescale1"===c?"prescale2":"prescale1"}return{img:t,paintWidth:l,paintHeight:h}}_createMaskCanvas(t){const e=this.ctx,{width:i,height:s}=t,n=this.current.fillColor,r=this.current.patternFill,a=getCurrentTransform(e);let o,l,h,c;if((t.bitmap||t.data)&&t.count>1){const e=t.bitmap||t.data.buffer;l=JSON.stringify(r?a:[a.slice(0,4),n]);o=this._cachedBitmapsMap.get(e);if(!o){o=new Map;this._cachedBitmapsMap.set(e,o)}const i=o.get(l);if(i&&!r){return{canvas:i,offsetX:Math.round(Math.min(a[0],a[2])+a[4]),offsetY:Math.round(Math.min(a[1],a[3])+a[5])}}h=i}if(!h){c=this.cachedCanvases.getCanvas("maskCanvas",i,s);putBinaryImageMask(c.context,t)}let d=Util.transform(a,[1/i,0,0,-1/s,0,0]);d=Util.transform(d,[1,0,0,1,0,-s]);const u=ft.slice();Util.axialAlignedBoundingBox([0,0,i,s],d,u);const[p,g,f,m]=u,b=Math.round(f-p)||1,v=Math.round(m-g)||1,w=this.cachedCanvases.getCanvas("fillCanvas",b,v),y=w.context,A=p,_=g;y.translate(-A,-_);y.transform(...d);if(!h){h=this._scaleImage(c.canvas,getCurrentTransformInverse(y));h=h.img;o&&r&&o.set(l,h)}y.imageSmoothingEnabled=getImageSmoothingEnabled(getCurrentTransform(y),t.interpolate);drawImageAtIntegerCoords(y,h,0,0,h.width,h.height,0,0,i,s);y.globalCompositeOperation="source-in";const x=Util.transform(getCurrentTransformInverse(y),[1,0,0,1,-A,-_]);y.fillStyle=r?n.getPattern(e,this,x,ot):n;y.fillRect(0,0,i,s);if(o&&!r){this.cachedCanvases.delete("fillCanvas");o.set(l,w.canvas)}return{canvas:w.canvas,offsetX:Math.round(A),offsetY:Math.round(_)}}setLineWidth(t){t!==this.current.lineWidth&&(this._cachedScaleForStroking[0]=-1);this.current.lineWidth=t;this.ctx.lineWidth=t}setLineCap(t){this.ctx.lineCap=mt[t]}setLineJoin(t){this.ctx.lineJoin=bt[t]}setMiterLimit(t){this.ctx.miterLimit=t}setDash(t,e){const i=this.ctx;if(void 0!==i.setLineDash){i.setLineDash(t);i.lineDashOffset=e}}setRenderingIntent(t){}setFlatness(t){}setGState(t){for(const[e,i]of t)switch(e){case"LW":this.setLineWidth(i);break;case"LC":this.setLineCap(i);break;case"LJ":this.setLineJoin(i);break;case"ML":this.setMiterLimit(i);break;case"D":this.setDash(i[0],i[1]);break;case"RI":this.setRenderingIntent(i);break;case"FL":this.setFlatness(i);break;case"Font":this.setFont(i[0],i[1]);break;case"CA":this.current.strokeAlpha=i;break;case"ca":this.ctx.globalAlpha=this.current.fillAlpha=i;break;case"BM":this.ctx.globalCompositeOperation=i;break;case"SMask":this.current.activeSMask=i?this.tempSMask:null;this.tempSMask=null;this.checkSMaskState();break;case"TR":this.ctx.filter=this.current.transferMaps=this.filterFactory.addFilter(i)}}get inSMaskMode(){return!!this.suspendedCtx}checkSMaskState(){const t=this.inSMaskMode;this.current.activeSMask&&!t?this.beginSMaskMode():!this.current.activeSMask&&t&&this.endSMaskMode()}beginSMaskMode(){if(this.inSMaskMode)throw new Error("beginSMaskMode called while already in smask mode");const t=this.ctx.canvas.width,e=this.ctx.canvas.height,i="smaskGroupAt"+this.groupLevel,s=this.cachedCanvases.getCanvas(i,t,e);this.suspendedCtx=this.ctx;const n=this.ctx=s.context;n.setTransform(this.suspendedCtx.getTransform());copyCtxState(this.suspendedCtx,n);!function mirrorContextOperations(t,e){if(t._removeMirroring)throw new Error("Context is already forwarding operations.");t.__originalSave=t.save;t.__originalRestore=t.restore;t.__originalRotate=t.rotate;t.__originalScale=t.scale;t.__originalTranslate=t.translate;t.__originalTransform=t.transform;t.__originalSetTransform=t.setTransform;t.__originalResetTransform=t.resetTransform;t.__originalClip=t.clip;t.__originalMoveTo=t.moveTo;t.__originalLineTo=t.lineTo;t.__originalBezierCurveTo=t.bezierCurveTo;t.__originalRect=t.rect;t.__originalClosePath=t.closePath;t.__originalBeginPath=t.beginPath;t._removeMirroring=()=>{t.save=t.__originalSave;t.restore=t.__originalRestore;t.rotate=t.__originalRotate;t.scale=t.__originalScale;t.translate=t.__originalTranslate;t.transform=t.__originalTransform;t.setTransform=t.__originalSetTransform;t.resetTransform=t.__originalResetTransform;t.clip=t.__originalClip;t.moveTo=t.__originalMoveTo;t.lineTo=t.__originalLineTo;t.bezierCurveTo=t.__originalBezierCurveTo;t.rect=t.__originalRect;t.closePath=t.__originalClosePath;t.beginPath=t.__originalBeginPath;delete t._removeMirroring};t.save=function(){e.save();this.__originalSave()};t.restore=function(){e.restore();this.__originalRestore()};t.translate=function(t,i){e.translate(t,i);this.__originalTranslate(t,i)};t.scale=function(t,i){e.scale(t,i);this.__originalScale(t,i)};t.transform=function(t,i,s,n,r,a){e.transform(t,i,s,n,r,a);this.__originalTransform(t,i,s,n,r,a)};t.setTransform=function(t,i,s,n,r,a){e.setTransform(t,i,s,n,r,a);this.__originalSetTransform(t,i,s,n,r,a)};t.resetTransform=function(){e.resetTransform();this.__originalResetTransform()};t.rotate=function(t){e.rotate(t);this.__originalRotate(t)};t.clip=function(t){e.clip(t);this.__originalClip(t)};t.moveTo=function(t,i){e.moveTo(t,i);this.__originalMoveTo(t,i)};t.lineTo=function(t,i){e.lineTo(t,i);this.__originalLineTo(t,i)};t.bezierCurveTo=function(t,i,s,n,r,a){e.bezierCurveTo(t,i,s,n,r,a);this.__originalBezierCurveTo(t,i,s,n,r,a)};t.rect=function(t,i,s,n){e.rect(t,i,s,n);this.__originalRect(t,i,s,n)};t.closePath=function(){e.closePath();this.__originalClosePath()};t.beginPath=function(){e.beginPath();this.__originalBeginPath()}}(n,this.suspendedCtx);this.setGState([["BM","source-over"]])}endSMaskMode(){if(!this.inSMaskMode)throw new Error("endSMaskMode called while not in smask mode");this.ctx._removeMirroring();copyCtxState(this.ctx,this.suspendedCtx);this.ctx=this.suspendedCtx;this.suspendedCtx=null}compose(t){if(!this.current.activeSMask)return;if(t){t[0]=Math.floor(t[0]);t[1]=Math.floor(t[1]);t[2]=Math.ceil(t[2]);t[3]=Math.ceil(t[3])}else t=[0,0,this.ctx.canvas.width,this.ctx.canvas.height];const e=this.current.activeSMask,i=this.suspendedCtx;this.composeSMask(i,e,this.ctx,t);this.ctx.save();this.ctx.setTransform(1,0,0,1,0,0);this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height);this.ctx.restore()}composeSMask(t,e,i,s){const n=s[0],r=s[1],a=s[2]-n,o=s[3]-r;if(0!==a&&0!==o){this.genericComposeSMask(e.context,i,a,o,e.subtype,e.backdrop,e.transferMap,n,r,e.offsetX,e.offsetY);t.save();t.globalAlpha=1;t.globalCompositeOperation="source-over";t.setTransform(1,0,0,1,0,0);t.drawImage(i.canvas,0,0);t.restore()}}genericComposeSMask(t,e,i,s,n,r,a,o,l,h,c){let d=t.canvas,u=o-h,p=l-c;if(r){const e=Util.makeHexColor(...r);if(u<0||p<0||u+i>d.width||p+s>d.height){const t=this.cachedCanvases.getCanvas("maskExtension",i,s),n=t.context;n.drawImage(d,-u,-p);n.globalCompositeOperation="destination-atop";n.fillStyle=e;n.fillRect(0,0,i,s);n.globalCompositeOperation="source-over";d=t.canvas;u=p=0}else{t.save();t.globalAlpha=1;t.setTransform(1,0,0,1,0,0);const n=new Path2D;n.rect(u,p,i,s);t.clip(n);t.globalCompositeOperation="destination-atop";t.fillStyle=e;t.fillRect(u,p,i,s);t.restore()}}e.save();e.globalAlpha=1;e.setTransform(1,0,0,1,0,0);"Alpha"===n&&a?e.filter=this.filterFactory.addAlphaFilter(a):"Luminosity"===n&&(e.filter=this.filterFactory.addLuminosityFilter(a));const g=new Path2D;g.rect(o,l,i,s);e.clip(g);e.globalCompositeOperation="destination-in";e.drawImage(d,u,p,i,s,o,l,i,s);e.restore()}save(){this.inSMaskMode&&copyCtxState(this.ctx,this.suspendedCtx);this.ctx.save();const t=this.current;this.stateStack.push(t);this.current=t.clone()}restore(){if(0!==this.stateStack.length){this.current=this.stateStack.pop();this.ctx.restore();this.inSMaskMode&&copyCtxState(this.suspendedCtx,this.ctx);this.checkSMaskState();this.pendingClip=null;this._cachedScaleForStroking[0]=-1;this._cachedGetSinglePixelWidth=null}else this.inSMaskMode&&this.endSMaskMode()}transform(t,e,i,s,n,r){this.ctx.transform(t,e,i,s,n,r);this._cachedScaleForStroking[0]=-1;this._cachedGetSinglePixelWidth=null}constructPath(t,e,i){let[s]=e;if(i){if(!(s instanceof Path2D)){const t=e[0]=new Path2D;for(let e=0,i=s.length;e<i;)switch(s[e++]){case I:t.moveTo(s[e++],s[e++]);break;case R:t.lineTo(s[e++],s[e++]);break;case L:t.bezierCurveTo(s[e++],s[e++],s[e++],s[e++],s[e++],s[e++]);break;case O:t.closePath();break;default:warn(`Unrecognized drawing path operator: ${s[e-1]}`)}s=t}Util.axialAlignedBoundingBox(i,getCurrentTransform(this.ctx),this.current.minMax);this[t](s)}else{s||=e[0]=new Path2D;this[t](s)}}closePath(){this.ctx.closePath()}stroke(t,e=!0){const i=this.ctx,s=this.current.strokeColor;i.globalAlpha=this.current.strokeAlpha;if(this.contentVisible)if("object"==typeof s&&s?.getPattern){const e=s.isModifyingCurrentTransform()?i.getTransform():null;i.save();i.strokeStyle=s.getPattern(i,this,getCurrentTransformInverse(i),lt);if(e){const s=new Path2D;s.addPath(t,i.getTransform().invertSelf().multiplySelf(e));t=s}this.rescaleAndStroke(t,!1);i.restore()}else this.rescaleAndStroke(t,!0);e&&this.consumePath(t,this.current.getClippedPathBoundingBox(lt,getCurrentTransform(this.ctx)));i.globalAlpha=this.current.fillAlpha}closeStroke(t){this.stroke(t)}fill(t,e=!0){const i=this.ctx,s=this.current.fillColor;let n=!1;if(this.current.patternFill){const e=s.isModifyingCurrentTransform()?i.getTransform():null;i.save();i.fillStyle=s.getPattern(i,this,getCurrentTransformInverse(i),ot);if(e){const s=new Path2D;s.addPath(t,i.getTransform().invertSelf().multiplySelf(e));t=s}n=!0}const r=this.current.getClippedPathBoundingBox();if(this.contentVisible&&null!==r)if(this.pendingEOFill){i.fill(t,"evenodd");this.pendingEOFill=!1}else i.fill(t);n&&i.restore();e&&this.consumePath(t,r)}eoFill(t){this.pendingEOFill=!0;this.fill(t)}fillStroke(t){this.fill(t,!1);this.stroke(t,!1);this.consumePath(t)}eoFillStroke(t){this.pendingEOFill=!0;this.fillStroke(t)}closeFillStroke(t){this.fillStroke(t)}closeEOFillStroke(t){this.pendingEOFill=!0;this.fillStroke(t)}endPath(t){this.consumePath(t)}rawFillPath(t){this.ctx.fill(t)}clip(){this.pendingClip=vt}eoClip(){this.pendingClip=wt}beginText(){this.current.textMatrix=null;this.current.textMatrixScale=1;this.current.x=this.current.lineX=0;this.current.y=this.current.lineY=0}endText(){const t=this.pendingTextPaths,e=this.ctx;if(void 0===t)return;const i=new Path2D,s=e.getTransform().invertSelf();for(const{transform:e,x:n,y:r,fontSize:a,path:o}of t)i.addPath(o,new DOMMatrix(e).preMultiplySelf(s).translate(n,r).scale(a,-a));e.clip(i);delete this.pendingTextPaths}setCharSpacing(t){this.current.charSpacing=t}setWordSpacing(t){this.current.wordSpacing=t}setHScale(t){this.current.textHScale=t/100}setLeading(t){this.current.leading=-t}setFont(t,e){const i=this.commonObjs.get(t),n=this.current;if(!i)throw new Error(`Can't find font for ${t}`);n.fontMatrix=i.fontMatrix||s;0!==n.fontMatrix[0]&&0!==n.fontMatrix[3]||warn("Invalid font matrix for font "+t);if(e<0){e=-e;n.fontDirection=-1}else n.fontDirection=1;this.current.font=i;this.current.fontSize=e;if(i.isType3Font)return;const r=i.loadedName||"sans-serif",a=i.systemFontInfo?.css||`"${r}", ${i.fallbackName}`;let o="normal";i.black?o="900":i.bold&&(o="bold");const l=i.italic?"italic":"normal";let h=e;e<16?h=16:e>100&&(h=100);this.current.fontSizeScale=e/h;this.ctx.font=`${l} ${o} ${h}px ${a}`}setTextRenderingMode(t){this.current.textRenderingMode=t}setTextRise(t){this.current.textRise=t}moveText(t,e){this.current.x=this.current.lineX+=t;this.current.y=this.current.lineY+=e}setLeadingMoveText(t,e){this.setLeading(-e);this.moveText(t,e)}setTextMatrix(t){const{current:e}=this;e.textMatrix=t;e.textMatrixScale=Math.hypot(t[0],t[1]);e.x=e.lineX=0;e.y=e.lineY=0}nextLine(){this.moveText(0,this.current.leading)}#Ii(t,e,i){const s=new Path2D;s.addPath(t,new DOMMatrix(i).invertSelf().multiplySelf(e));return s}paintChar(t,e,i,s,n){const r=this.ctx,a=this.current,o=a.font,l=a.textRenderingMode,h=a.fontSize/a.fontSizeScale,c=l&A,d=!!(l&_),u=a.patternFill&&!o.missingFile,p=a.patternStroke&&!o.missingFile;let g;(o.disableFontFace||d||u||p)&&(g=o.getPathGenerator(this.commonObjs,t));if(o.disableFontFace||u||p){r.save();r.translate(e,i);r.scale(h,-h);let t;if(c===b||c===w)if(s){t=r.getTransform();r.setTransform(...s);r.fill(this.#Ii(g,t,s))}else r.fill(g);if(c===v||c===w)if(n){t||=r.getTransform();r.setTransform(...n);const{a:e,b:i,c:s,d:a}=t,o=Util.inverseTransform(n),l=Util.transform([e,i,s,a,0,0],o);Util.singularValueDecompose2dScale(l,gt);r.lineWidth*=Math.max(gt[0],gt[1])/h;r.stroke(this.#Ii(g,t,n))}else{r.lineWidth/=h;r.stroke(g)}r.restore()}else{c!==b&&c!==w||r.fillText(t,e,i);c!==v&&c!==w||r.strokeText(t,e,i)}if(d){(this.pendingTextPaths||=[]).push({transform:getCurrentTransform(r),x:e,y:i,fontSize:h,path:g})}}get isFontSubpixelAAEnabled(){const{context:t}=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10);t.scale(1.5,1);t.fillText("I",0,10);const e=t.getImageData(0,0,10,10).data;let i=!1;for(let t=3;t<e.length;t+=4)if(e[t]>0&&e[t]<255){i=!0;break}return shadow(this,"isFontSubpixelAAEnabled",i)}showText(t){const e=this.current,i=e.font;if(i.isType3Font)return this.showType3Text(t);const s=e.fontSize;if(0===s)return;const n=this.ctx,r=e.fontSizeScale,a=e.charSpacing,o=e.wordSpacing,l=e.fontDirection,h=e.textHScale*l,c=t.length,d=i.vertical,u=d?1:-1,p=i.defaultVMetrics,g=s*e.fontMatrix[0],f=e.textRenderingMode===b&&!i.disableFontFace&&!e.patternFill;n.save();e.textMatrix&&n.transform(...e.textMatrix);n.translate(e.x,e.y+e.textRise);l>0?n.scale(h,-1):n.scale(h,1);let m,y;if(e.patternFill){n.save();const t=e.fillColor.getPattern(n,this,getCurrentTransformInverse(n),ot);m=getCurrentTransform(n);n.restore();n.fillStyle=t}if(e.patternStroke){n.save();const t=e.strokeColor.getPattern(n,this,getCurrentTransformInverse(n),lt);y=getCurrentTransform(n);n.restore();n.strokeStyle=t}let _=e.lineWidth;const x=e.textMatrixScale;if(0===x||0===_){const t=e.textRenderingMode&A;t!==v&&t!==w||(_=this.getSinglePixelWidth())}else _/=x;if(1!==r){n.scale(r,r);_/=r}n.lineWidth=_;if(i.isInvalidPDFjsFont){const i=[];let s=0;for(const e of t){i.push(e.unicode);s+=e.width}n.fillText(i.join(""),0,0);e.x+=s*g*h;n.restore();this.compose();return}let E,S=0;for(E=0;E<c;++E){const e=t[E];if("number"==typeof e){S+=u*e*s/1e3;continue}let h=!1;const c=(e.isSpace?o:0)+a,b=e.fontChar,v=e.accent;let w,A,_=e.width;if(d){const t=e.vmetric||p,i=-(e.vmetric?t[1]:.5*_)*g,s=t[2]*g;_=t?-t[0]:_;w=i/r;A=(S+s)/r}else{w=S/r;A=0}if(i.remeasure&&_>0){const t=1e3*n.measureText(b).width/s*r;if(_<t&&this.isFontSubpixelAAEnabled){const e=_/t;h=!0;n.save();n.scale(e,1);w/=e}else _!==t&&(w+=(_-t)/2e3*s/r)}if(this.contentVisible&&(e.isInFont||i.missingFile))if(f&&!v)n.fillText(b,w,A);else{this.paintChar(b,w,A,m,y);if(v){const t=w+s*v.offset.x/r,e=A-s*v.offset.y/r;this.paintChar(v.fontChar,t,e,m,y)}}S+=d?_*g-c*l:_*g+c*l;h&&n.restore()}d?e.y-=S:e.x+=S*h;n.restore();this.compose()}showType3Text(t){const e=this.ctx,i=this.current,n=i.font,r=i.fontSize,a=i.fontDirection,o=n.vertical?1:-1,l=i.charSpacing,h=i.wordSpacing,c=i.textHScale*a,d=i.fontMatrix||s,u=t.length;let p,g,f,m;if(!(i.textRenderingMode===y)&&0!==r){this._cachedScaleForStroking[0]=-1;this._cachedGetSinglePixelWidth=null;e.save();i.textMatrix&&e.transform(...i.textMatrix);e.translate(i.x,i.y+i.textRise);e.scale(c,a);for(p=0;p<u;++p){g=t[p];if("number"==typeof g){m=o*g*r/1e3;this.ctx.translate(m,0);i.x+=m*c;continue}const s=(g.isSpace?h:0)+l,a=n.charProcOperatorList[g.operatorListId];if(a){if(this.contentVisible){this.save();e.scale(r,r);e.transform(...d);this.executeOperatorList(a);this.restore()}}else warn(`Type3 character "${g.operatorListId}" is not available.`);const u=[g.width,0];Util.applyTransform(u,d);f=u[0]*r+s;e.translate(f,0);i.x+=f*c}e.restore()}}setCharWidth(t,e){}setCharWidthAndBounds(t,e,i,s,n,r){const a=new Path2D;a.rect(i,s,n-i,r-s);this.ctx.clip(a);this.endPath()}getColorN_Pattern(t){let e;if("TilingPattern"===t[0]){const i=this.baseTransform||getCurrentTransform(this.ctx),s={createCanvasGraphics:t=>new CanvasGraphics(t,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:this.optionalContentConfig,markedContentStack:this.markedContentStack})};e=new TilingPattern(t,this.ctx,s,i)}else e=this._getPattern(t[1],t[2]);return e}setStrokeColorN(){this.current.strokeColor=this.getColorN_Pattern(arguments);this.current.patternStroke=!0}setFillColorN(){this.current.fillColor=this.getColorN_Pattern(arguments);this.current.patternFill=!0}setStrokeRGBColor(t,e,i){this.ctx.strokeStyle=this.current.strokeColor=Util.makeHexColor(t,e,i);this.current.patternStroke=!1}setStrokeTransparent(){this.ctx.strokeStyle=this.current.strokeColor="transparent";this.current.patternStroke=!1}setFillRGBColor(t,e,i){this.ctx.fillStyle=this.current.fillColor=Util.makeHexColor(t,e,i);this.current.patternFill=!1}setFillTransparent(){this.ctx.fillStyle=this.current.fillColor="transparent";this.current.patternFill=!1}_getPattern(t,e=null){let i;if(this.cachedPatterns.has(t))i=this.cachedPatterns.get(t);else{i=function getShadingPattern(t){switch(t[0]){case"RadialAxial":return new RadialAxialShadingPattern(t);case"Mesh":return new MeshShadingPattern(t);case"Dummy":return new DummyShadingPattern}throw new Error(`Unknown IR type: ${t[0]}`)}(this.getObject(t));this.cachedPatterns.set(t,i)}e&&(i.matrix=e);return i}shadingFill(t){if(!this.contentVisible)return;const e=this.ctx;this.save();const i=this._getPattern(t);e.fillStyle=i.getPattern(e,this,getCurrentTransformInverse(e),ht);const s=getCurrentTransformInverse(e);if(s){const{width:t,height:i}=e.canvas,n=ft.slice();Util.axialAlignedBoundingBox([0,0,t,i],s,n);const[r,a,o,l]=n;this.ctx.fillRect(r,a,o-r,l-a)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.compose(this.current.getClippedPathBoundingBox());this.restore()}beginInlineImage(){unreachable("Should not call beginInlineImage")}beginImageData(){unreachable("Should not call beginImageData")}paintFormXObjectBegin(t,e){if(this.contentVisible){this.save();this.baseTransformStack.push(this.baseTransform);t&&this.transform(...t);this.baseTransform=getCurrentTransform(this.ctx);if(e){Util.axialAlignedBoundingBox(e,this.baseTransform,this.current.minMax);const[t,i,s,n]=e,r=new Path2D;r.rect(t,i,s-t,n-i);this.ctx.clip(r);this.endPath()}}}paintFormXObjectEnd(){if(this.contentVisible){this.restore();this.baseTransform=this.baseTransformStack.pop()}}beginGroup(t){if(!this.contentVisible)return;this.save();if(this.inSMaskMode){this.endSMaskMode();this.current.activeSMask=null}const e=this.ctx;t.isolated||info("TODO: Support non-isolated groups.");t.knockout&&warn("Knockout groups not supported.");const i=getCurrentTransform(e);t.matrix&&e.transform(...t.matrix);if(!t.bbox)throw new Error("Bounding box is required.");let s=ft.slice();Util.axialAlignedBoundingBox(t.bbox,getCurrentTransform(e),s);const n=[0,0,e.canvas.width,e.canvas.height];s=Util.intersect(s,n)||[0,0,0,0];const r=Math.floor(s[0]),a=Math.floor(s[1]),o=Math.max(Math.ceil(s[2])-r,1),l=Math.max(Math.ceil(s[3])-a,1);this.current.startNewPathAndClipBox([0,0,o,l]);let h="groupAt"+this.groupLevel;t.smask&&(h+="_smask_"+this.smaskCounter++%2);const c=this.cachedCanvases.getCanvas(h,o,l),d=c.context;d.translate(-r,-a);d.transform(...i);let u=new Path2D;const[p,g,f,m]=t.bbox;u.rect(p,g,f-p,m-g);if(t.matrix){const e=new Path2D;e.addPath(u,new DOMMatrix(t.matrix));u=e}d.clip(u);if(t.smask)this.smaskStack.push({canvas:c.canvas,context:d,offsetX:r,offsetY:a,subtype:t.smask.subtype,backdrop:t.smask.backdrop,transferMap:t.smask.transferMap||null,startTransformInverse:null});else{e.setTransform(1,0,0,1,0,0);e.translate(r,a);e.save()}copyCtxState(e,d);this.ctx=d;this.setGState([["BM","source-over"],["ca",1],["CA",1]]);this.groupStack.push(e);this.groupLevel++}endGroup(t){if(!this.contentVisible)return;this.groupLevel--;const e=this.ctx,i=this.groupStack.pop();this.ctx=i;this.ctx.imageSmoothingEnabled=!1;if(t.smask){this.tempSMask=this.smaskStack.pop();this.restore()}else{this.ctx.restore();const t=getCurrentTransform(this.ctx);this.restore();this.ctx.save();this.ctx.setTransform(...t);const i=ft.slice();Util.axialAlignedBoundingBox([0,0,e.canvas.width,e.canvas.height],t,i);this.ctx.drawImage(e.canvas,0,0);this.ctx.restore();this.compose(i)}}beginAnnotation(t,e,i,s,n){this.#Pi();resetCtxToDefault(this.ctx);this.ctx.save();this.save();this.baseTransform&&this.ctx.setTransform(...this.baseTransform);if(e){const s=e[2]-e[0],r=e[3]-e[1];if(n&&this.annotationCanvasMap){(i=i.slice())[4]-=e[0];i[5]-=e[1];(e=e.slice())[0]=e[1]=0;e[2]=s;e[3]=r;Util.singularValueDecompose2dScale(getCurrentTransform(this.ctx),gt);const{viewportScale:n}=this,a=Math.ceil(s*this.outputScaleX*n),o=Math.ceil(r*this.outputScaleY*n);this.annotationCanvas=this.canvasFactory.create(a,o);const{canvas:l,context:h}=this.annotationCanvas;this.annotationCanvasMap.set(t,l);this.annotationCanvas.savedCtx=this.ctx;this.ctx=h;this.ctx.save();this.ctx.setTransform(gt[0],0,0,-gt[1],0,r*gt[1]);resetCtxToDefault(this.ctx)}else{resetCtxToDefault(this.ctx);this.endPath();const t=new Path2D;t.rect(e[0],e[1],s,r);this.ctx.clip(t)}}this.current=new CanvasExtraState(this.ctx.canvas.width,this.ctx.canvas.height);this.transform(...i);this.transform(...s)}endAnnotation(){if(this.annotationCanvas){this.ctx.restore();this.#ki();this.ctx=this.annotationCanvas.savedCtx;delete this.annotationCanvas.savedCtx;delete this.annotationCanvas}}paintImageMaskXObject(t){if(!this.contentVisible)return;const e=t.count;(t=this.getObject(t.data,t)).count=e;const i=this.ctx,s=this._createMaskCanvas(t),n=s.canvas;i.save();i.setTransform(1,0,0,1,0,0);i.drawImage(n,s.offsetX,s.offsetY);i.restore();this.compose()}paintImageMaskXObjectRepeat(t,e,i=0,s=0,n,r){if(!this.contentVisible)return;t=this.getObject(t.data,t);const a=this.ctx;a.save();const o=getCurrentTransform(a);a.transform(e,i,s,n,0,0);const l=this._createMaskCanvas(t);a.setTransform(1,0,0,1,l.offsetX-o[4],l.offsetY-o[5]);for(let t=0,h=r.length;t<h;t+=2){const h=Util.transform(o,[e,i,s,n,r[t],r[t+1]]);a.drawImage(l.canvas,h[4],h[5])}a.restore();this.compose()}paintImageMaskXObjectGroup(t){if(!this.contentVisible)return;const e=this.ctx,i=this.current.fillColor,s=this.current.patternFill;for(const n of t){const{data:t,width:r,height:a,transform:o}=n,l=this.cachedCanvases.getCanvas("maskCanvas",r,a),h=l.context;h.save();putBinaryImageMask(h,this.getObject(t,n));h.globalCompositeOperation="source-in";h.fillStyle=s?i.getPattern(h,this,getCurrentTransformInverse(e),ot):i;h.fillRect(0,0,r,a);h.restore();e.save();e.transform(...o);e.scale(1,-1);drawImageAtIntegerCoords(e,l.canvas,0,0,r,a,0,-1,1,1);e.restore()}this.compose()}paintImageXObject(t){if(!this.contentVisible)return;const e=this.getObject(t);e?this.paintInlineImageXObject(e):warn("Dependent image isn't ready yet")}paintImageXObjectRepeat(t,e,i,s){if(!this.contentVisible)return;const n=this.getObject(t);if(!n){warn("Dependent image isn't ready yet");return}const r=n.width,a=n.height,o=[];for(let t=0,n=s.length;t<n;t+=2)o.push({transform:[e,0,0,i,s[t],s[t+1]],x:0,y:0,w:r,h:a});this.paintInlineImageXObjectGroup(n,o)}applyTransferMapsToCanvas(t){if("none"!==this.current.transferMaps){t.filter=this.current.transferMaps;t.drawImage(t.canvas,0,0);t.filter="none"}return t.canvas}applyTransferMapsToBitmap(t){if("none"===this.current.transferMaps)return t.bitmap;const{bitmap:e,width:i,height:s}=t,n=this.cachedCanvases.getCanvas("inlineImage",i,s),r=n.context;r.filter=this.current.transferMaps;r.drawImage(e,0,0);r.filter="none";return n.canvas}paintInlineImageXObject(t){if(!this.contentVisible)return;const e=t.width,i=t.height,s=this.ctx;this.save();const{filter:n}=s;"none"!==n&&""!==n&&(s.filter="none");s.scale(1/e,-1/i);let r;if(t.bitmap)r=this.applyTransferMapsToBitmap(t);else if("function"==typeof HTMLElement&&t instanceof HTMLElement||!t.data)r=t;else{const s=this.cachedCanvases.getCanvas("inlineImage",e,i).context;putBinaryImageData(s,t);r=this.applyTransferMapsToCanvas(s)}const a=this._scaleImage(r,getCurrentTransformInverse(s));s.imageSmoothingEnabled=getImageSmoothingEnabled(getCurrentTransform(s),t.interpolate);drawImageAtIntegerCoords(s,a.img,0,0,a.paintWidth,a.paintHeight,0,-i,e,i);this.compose();this.restore()}paintInlineImageXObjectGroup(t,e){if(!this.contentVisible)return;const i=this.ctx;let s;if(t.bitmap)s=t.bitmap;else{const e=t.width,i=t.height,n=this.cachedCanvases.getCanvas("inlineImage",e,i).context;putBinaryImageData(n,t);s=this.applyTransferMapsToCanvas(n)}for(const t of e){i.save();i.transform(...t.transform);i.scale(1,-1);drawImageAtIntegerCoords(i,s,t.x,t.y,t.w,t.h,0,-1,1,1);i.restore()}this.compose()}paintSolidColorImageMask(){if(this.contentVisible){this.ctx.fillRect(0,0,1,1);this.compose()}}markPoint(t){}markPointProps(t,e){}beginMarkedContent(t){this.markedContentStack.push({visible:!0})}beginMarkedContentProps(t,e){"OC"===t?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(e)}):this.markedContentStack.push({visible:!0});this.contentVisible=this.isContentVisible()}endMarkedContent(){this.markedContentStack.pop();this.contentVisible=this.isContentVisible()}beginCompat(){}endCompat(){}consumePath(t,e){const i=this.current.isEmptyClip();this.pendingClip&&this.current.updateClipFromPath();this.pendingClip||this.compose(e);const s=this.ctx;if(this.pendingClip){i||(this.pendingClip===wt?s.clip(t,"evenodd"):s.clip(t));this.pendingClip=null}this.current.startNewPathAndClipBox(this.current.clipBox)}getSinglePixelWidth(){if(!this._cachedGetSinglePixelWidth){const t=getCurrentTransform(this.ctx);if(0===t[1]&&0===t[2])this._cachedGetSinglePixelWidth=1/Math.min(Math.abs(t[0]),Math.abs(t[3]));else{const e=Math.abs(t[0]*t[3]-t[2]*t[1]),i=Math.hypot(t[0],t[2]),s=Math.hypot(t[1],t[3]);this._cachedGetSinglePixelWidth=Math.max(i,s)/e}}return this._cachedGetSinglePixelWidth}getScaleForStroking(){if(-1===this._cachedScaleForStroking[0]){const{lineWidth:t}=this.current,{a:e,b:i,c:s,d:n}=this.ctx.getTransform();let r,a;if(0===i&&0===s){const i=Math.abs(e),s=Math.abs(n);if(i===s)if(0===t)r=a=1/i;else{const e=i*t;r=a=e<1?1/e:1}else if(0===t){r=1/i;a=1/s}else{const e=i*t,n=s*t;r=e<1?1/e:1;a=n<1?1/n:1}}else{const o=Math.abs(e*n-i*s),l=Math.hypot(e,i),h=Math.hypot(s,n);if(0===t){r=h/o;a=l/o}else{const e=t*o;r=h>e?h/e:1;a=l>e?l/e:1}}this._cachedScaleForStroking[0]=r;this._cachedScaleForStroking[1]=a}return this._cachedScaleForStroking}rescaleAndStroke(t,e){const{ctx:i,current:{lineWidth:s}}=this,[n,r]=this.getScaleForStroking();if(n===r){i.lineWidth=(s||1)*n;i.stroke(t);return}const a=i.getLineDash();e&&i.save();i.scale(n,r);pt.a=1/n;pt.d=1/r;const o=new Path2D;o.addPath(t,pt);if(a.length>0){const t=Math.max(n,r);i.setLineDash(a.map((e=>e/t)));i.lineDashOffset/=t}i.lineWidth=s||1;i.stroke(o);e&&i.restore()}isContentVisible(){for(let t=this.markedContentStack.length-1;t>=0;t--)if(!this.markedContentStack[t].visible)return!1;return!0}}for(const t in k)void 0!==CanvasGraphics.prototype[t]&&(CanvasGraphics.prototype[k[t]]=CanvasGraphics.prototype[t]);class GlobalWorkerOptions{static#Ri=null;static#Fi="";static get workerPort(){return this.#Ri}static set workerPort(t){if(!("undefined"!=typeof Worker&&t instanceof Worker)&&null!==t)throw new Error("Invalid `workerPort` type.");this.#Ri=t}static get workerSrc(){return this.#Fi}static set workerSrc(t){if("string"!=typeof t)throw new Error("Invalid `workerSrc` type.");this.#Fi=t}}class Metadata{#Li;#Oi;constructor({parsedData:t,rawData:e}){this.#Li=t;this.#Oi=e}getRaw(){return this.#Oi}get(t){return this.#Li.get(t)??null}[Symbol.iterator](){return this.#Li.entries()}}const yt=Symbol("INTERNAL");class OptionalContentGroup{#Ni=!1;#Bi=!1;#Ui=!1;#Hi=!0;constructor(t,{name:e,intent:i,usage:s,rbGroups:n}){this.#Ni=!!(t&a);this.#Bi=!!(t&o);this.name=e;this.intent=i;this.usage=s;this.rbGroups=n}get visible(){if(this.#Ui)return this.#Hi;if(!this.#Hi)return!1;const{print:t,view:e}=this.usage;return this.#Ni?"OFF"!==e?.viewState:!this.#Bi||"OFF"!==t?.printState}_setVisible(t,e,i=!1){t!==yt&&unreachable("Internal method `_setVisible` called.");this.#Ui=i;this.#Hi=e}}class OptionalContentConfig{#zi=null;#ji=new Map;#Gi=null;#Wi=null;constructor(t,e=a){this.renderingIntent=e;this.name=null;this.creator=null;if(null!==t){this.name=t.name;this.creator=t.creator;this.#Wi=t.order;for(const i of t.groups)this.#ji.set(i.id,new OptionalContentGroup(e,i));if("OFF"===t.baseState)for(const t of this.#ji.values())t._setVisible(yt,!1);for(const e of t.on)this.#ji.get(e)._setVisible(yt,!0);for(const e of t.off)this.#ji.get(e)._setVisible(yt,!1);this.#Gi=this.getHash()}}#$i(t){const e=t.length;if(e<2)return!0;const i=t[0];for(let s=1;s<e;s++){const e=t[s];let n;if(Array.isArray(e))n=this.#$i(e);else{if(!this.#ji.has(e)){warn(`Optional content group not found: ${e}`);return!0}n=this.#ji.get(e).visible}switch(i){case"And":if(!n)return!1;break;case"Or":if(n)return!0;break;case"Not":return!n;default:return!0}}return"And"===i}isVisible(t){if(0===this.#ji.size)return!0;if(!t){info("Optional content group not defined.");return!0}if("OCG"===t.type){if(!this.#ji.has(t.id)){warn(`Optional content group not found: ${t.id}`);return!0}return this.#ji.get(t.id).visible}if("OCMD"===t.type){if(t.expression)return this.#$i(t.expression);if(!t.policy||"AnyOn"===t.policy){for(const e of t.ids){if(!this.#ji.has(e)){warn(`Optional content group not found: ${e}`);return!0}if(this.#ji.get(e).visible)return!0}return!1}if("AllOn"===t.policy){for(const e of t.ids){if(!this.#ji.has(e)){warn(`Optional content group not found: ${e}`);return!0}if(!this.#ji.get(e).visible)return!1}return!0}if("AnyOff"===t.policy){for(const e of t.ids){if(!this.#ji.has(e)){warn(`Optional content group not found: ${e}`);return!0}if(!this.#ji.get(e).visible)return!0}return!1}if("AllOff"===t.policy){for(const e of t.ids){if(!this.#ji.has(e)){warn(`Optional content group not found: ${e}`);return!0}if(this.#ji.get(e).visible)return!1}return!0}warn(`Unknown optional content policy ${t.policy}.`);return!0}warn(`Unknown group type ${t.type}.`);return!0}setVisibility(t,e=!0,i=!0){const s=this.#ji.get(t);if(s){if(i&&e&&s.rbGroups.length)for(const e of s.rbGroups)for(const i of e)i!==t&&this.#ji.get(i)?._setVisible(yt,!1,!0);s._setVisible(yt,!!e,!0);this.#zi=null}else warn(`Optional content group not found: ${t}`)}setOCGState({state:t,preserveRB:e}){let i;for(const s of t){switch(s){case"ON":case"OFF":case"Toggle":i=s;continue}const t=this.#ji.get(s);if(t)switch(i){case"ON":this.setVisibility(s,!0,e);break;case"OFF":this.setVisibility(s,!1,e);break;case"Toggle":this.setVisibility(s,!t.visible,e)}}this.#zi=null}get hasInitialVisibility(){return null===this.#Gi||this.getHash()===this.#Gi}getOrder(){return this.#ji.size?this.#Wi?this.#Wi.slice():[...this.#ji.keys()]:null}getGroup(t){return this.#ji.get(t)||null}getHash(){if(null!==this.#zi)return this.#zi;const t=new MurmurHash3_64;for(const[e,i]of this.#ji)t.update(`${e}:${i.visible}`);return this.#zi=t.hexdigest()}[Symbol.iterator](){return this.#ji.entries()}}class PDFDataTransportStream{constructor(t,{disableRange:e=!1,disableStream:i=!1}){assert(t,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.');const{length:s,initialData:n,progressiveDone:r,contentDispositionFilename:a}=t;this._queuedChunks=[];this._progressiveDone=r;this._contentDispositionFilename=a;if(n?.length>0){const t=n instanceof Uint8Array&&n.byteLength===n.buffer.byteLength?n.buffer:new Uint8Array(n).buffer;this._queuedChunks.push(t)}this._pdfDataRangeTransport=t;this._isStreamingSupported=!i;this._isRangeSupported=!e;this._contentLength=s;this._fullRequestReader=null;this._rangeReaders=[];t.addRangeListener(((t,e)=>{this._onReceiveData({begin:t,chunk:e})}));t.addProgressListener(((t,e)=>{this._onProgress({loaded:t,total:e})}));t.addProgressiveReadListener((t=>{this._onReceiveData({chunk:t})}));t.addProgressiveDoneListener((()=>{this._onProgressiveDone()}));t.transportReady()}_onReceiveData({begin:t,chunk:e}){const i=e instanceof Uint8Array&&e.byteLength===e.buffer.byteLength?e.buffer:new Uint8Array(e).buffer;if(void 0===t)this._fullRequestReader?this._fullRequestReader._enqueue(i):this._queuedChunks.push(i);else{assert(this._rangeReaders.some((function(e){if(e._begin!==t)return!1;e._enqueue(i);return!0})),"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}_onProgress(t){void 0===t.total?this._rangeReaders[0]?.onProgress?.({loaded:t.loaded}):this._fullRequestReader?.onProgress?.({loaded:t.loaded,total:t.total})}_onProgressiveDone(){this._fullRequestReader?.progressiveDone();this._progressiveDone=!0}_removeRangeReader(t){const e=this._rangeReaders.indexOf(t);e>=0&&this._rangeReaders.splice(e,1)}getFullReader(){assert(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");const t=this._queuedChunks;this._queuedChunks=null;return new PDFDataTransportStreamReader(this,t,this._progressiveDone,this._contentDispositionFilename)}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=new PDFDataTransportStreamRangeReader(this,t,e);this._pdfDataRangeTransport.requestDataRange(t,e);this._rangeReaders.push(i);return i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeReaders.slice(0))e.cancel(t);this._pdfDataRangeTransport.abort()}}class PDFDataTransportStreamReader{constructor(t,e,i=!1,s=null){this._stream=t;this._done=i||!1;this._filename=isPdfFile(s)?s:null;this._queuedChunks=e||[];this._loaded=0;for(const t of this._queuedChunks)this._loaded+=t.byteLength;this._requests=[];this._headersReady=Promise.resolve();t._fullRequestReader=this;this.onProgress=null}_enqueue(t){if(!this._done){if(this._requests.length>0){this._requests.shift().resolve({value:t,done:!1})}else this._queuedChunks.push(t);this._loaded+=t.byteLength}}get headersReady(){return this._headersReady}get filename(){return this._filename}get isRangeSupported(){return this._stream._isRangeSupported}get isStreamingSupported(){return this._stream._isStreamingSupported}get contentLength(){return this._stream._contentLength}async read(){if(this._queuedChunks.length>0){return{value:this._queuedChunks.shift(),done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();this._requests.push(t);return t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}progressiveDone(){this._done||(this._done=!0)}}class PDFDataTransportStreamRangeReader{constructor(t,e,i){this._stream=t;this._begin=e;this._end=i;this._queuedChunk=null;this._requests=[];this._done=!1;this.onProgress=null}_enqueue(t){if(!this._done){if(0===this._requests.length)this._queuedChunk=t;else{this._requests.shift().resolve({value:t,done:!1});for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}this._done=!0;this._stream._removeRangeReader(this)}}get isStreamingSupported(){return!1}async read(){if(this._queuedChunk){const t=this._queuedChunk;this._queuedChunk=null;return{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();this._requests.push(t);return t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._stream._removeRangeReader(this)}}function createHeaders(t,e){const i=new Headers;if(!t||!e||"object"!=typeof e)return i;for(const t in e){const s=e[t];void 0!==s&&i.append(t,s)}return i}function getResponseOrigin(t){return URL.parse(t)?.origin??null}function validateRangeRequestCapabilities({responseHeaders:t,isHttp:e,rangeChunkSize:i,disableRange:s}){const n={allowRangeRequests:!1,suggestedLength:void 0},r=parseInt(t.get("Content-Length"),10);if(!Number.isInteger(r))return n;n.suggestedLength=r;if(r<=2*i)return n;if(s||!e)return n;if("bytes"!==t.get("Accept-Ranges"))return n;if("identity"!==(t.get("Content-Encoding")||"identity"))return n;n.allowRangeRequests=!0;return n}function extractFilenameFromHeader(t){const e=t.get("Content-Disposition");if(e){let t=function getFilenameFromContentDispositionHeader(t){let e=!0,i=toParamRegExp("filename\\*","i").exec(t);if(i){i=i[1];let t=rfc2616unquote(i);t=unescape(t);t=rfc5987decode(t);t=rfc2047decode(t);return fixupEncoding(t)}i=function rfc2231getparam(t){const e=[];let i;const s=toParamRegExp("filename\\*((?!0\\d)\\d+)(\\*?)","ig");for(;null!==(i=s.exec(t));){let[,t,s,n]=i;t=parseInt(t,10);if(t in e){if(0===t)break}else e[t]=[s,n]}const n=[];for(let t=0;t<e.length&&t in e;++t){let[i,s]=e[t];s=rfc2616unquote(s);if(i){s=unescape(s);0===t&&(s=rfc5987decode(s))}n.push(s)}return n.join("")}(t);if(i)return fixupEncoding(rfc2047decode(i));i=toParamRegExp("filename","i").exec(t);if(i){i=i[1];let t=rfc2616unquote(i);t=rfc2047decode(t);return fixupEncoding(t)}function toParamRegExp(t,e){return new RegExp("(?:^|;)\\s*"+t+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',e)}function textdecode(t,i){if(t){if(!/^[\x00-\xFF]+$/.test(i))return i;try{const s=new TextDecoder(t,{fatal:!0}),n=stringToBytes(i);i=s.decode(n);e=!1}catch{}}return i}function fixupEncoding(t){if(e&&/[\x80-\xff]/.test(t)){t=textdecode("utf-8",t);e&&(t=textdecode("iso-8859-1",t))}return t}function rfc2616unquote(t){if(t.startsWith('"')){const e=t.slice(1).split('\\"');for(let t=0;t<e.length;++t){const i=e[t].indexOf('"');if(-1!==i){e[t]=e[t].slice(0,i);e.length=t+1}e[t]=e[t].replaceAll(/\\(.)/g,"$1")}t=e.join('"')}return t}function rfc5987decode(t){const e=t.indexOf("'");return-1===e?t:textdecode(t.slice(0,e),t.slice(e+1).replace(/^[^']*'/,""))}function rfc2047decode(t){return!t.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(t)?t:t.replaceAll(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,(function(t,e,i,s){if("q"===i||"Q"===i)return textdecode(e,s=(s=s.replaceAll("_"," ")).replaceAll(/=([0-9a-fA-F]{2})/g,(function(t,e){return String.fromCharCode(parseInt(e,16))})));try{s=atob(s)}catch{}return textdecode(e,s)}))}return""}(e);if(t.includes("%"))try{t=decodeURIComponent(t)}catch{}if(isPdfFile(t))return t}return null}function createResponseError(t,e){return new ResponseException(`Unexpected server response (${t}) while retrieving PDF "${e}".`,t,404===t||0===t&&e.startsWith("file:"))}function validateResponseStatus(t){return 200===t||206===t}function createFetchOptions(t,e,i){return{method:"GET",headers:t,signal:i.signal,mode:"cors",credentials:e?"include":"same-origin",redirect:"follow"}}function getArrayBuffer(t){if(t instanceof Uint8Array)return t.buffer;if(t instanceof ArrayBuffer)return t;warn(`getArrayBuffer - unexpected data format: ${t}`);return new Uint8Array(t).buffer}class PDFFetchStream{_responseOrigin=null;constructor(t){this.source=t;this.isHttp=/^https?:/i.test(t.url);this.headers=createHeaders(this.isHttp,t.httpHeaders);this._fullRequestReader=null;this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){assert(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once.");this._fullRequestReader=new PDFFetchStreamReader(this);return this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=new PDFFetchStreamRangeReader(this,t,e);this._rangeRequestReaders.push(i);return i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class PDFFetchStreamReader{constructor(t){this._stream=t;this._reader=null;this._loaded=0;this._filename=null;const e=t.source;this._withCredentials=e.withCredentials||!1;this._contentLength=e.length;this._headersCapability=Promise.withResolvers();this._disableRange=e.disableRange||!1;this._rangeChunkSize=e.rangeChunkSize;this._rangeChunkSize||this._disableRange||(this._disableRange=!0);this._abortController=new AbortController;this._isStreamingSupported=!e.disableStream;this._isRangeSupported=!e.disableRange;const i=new Headers(t.headers),s=e.url;fetch(s,createFetchOptions(i,this._withCredentials,this._abortController)).then((e=>{t._responseOrigin=getResponseOrigin(e.url);if(!validateResponseStatus(e.status))throw createResponseError(e.status,s);this._reader=e.body.getReader();this._headersCapability.resolve();const i=e.headers,{allowRangeRequests:n,suggestedLength:r}=validateRangeRequestCapabilities({responseHeaders:i,isHttp:t.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=n;this._contentLength=r||this._contentLength;this._filename=extractFilenameFromHeader(i);!this._isStreamingSupported&&this._isRangeSupported&&this.cancel(new AbortException("Streaming is disabled."))})).catch(this._headersCapability.reject);this.onProgress=null}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._headersCapability.promise;const{value:t,done:e}=await this._reader.read();if(e)return{value:t,done:e};this._loaded+=t.byteLength;this.onProgress?.({loaded:this._loaded,total:this._contentLength});return{value:getArrayBuffer(t),done:!1}}cancel(t){this._reader?.cancel(t);this._abortController.abort()}}class PDFFetchStreamRangeReader{constructor(t,e,i){this._stream=t;this._reader=null;this._loaded=0;const s=t.source;this._withCredentials=s.withCredentials||!1;this._readCapability=Promise.withResolvers();this._isStreamingSupported=!s.disableStream;this._abortController=new AbortController;const n=new Headers(t.headers);n.append("Range",`bytes=${e}-${i-1}`);const r=s.url;fetch(r,createFetchOptions(n,this._withCredentials,this._abortController)).then((e=>{const i=getResponseOrigin(e.url);if(i!==t._responseOrigin)throw new Error(`Expected range response-origin "${i}" to match "${t._responseOrigin}".`);if(!validateResponseStatus(e.status))throw createResponseError(e.status,r);this._readCapability.resolve();this._reader=e.body.getReader()})).catch(this._readCapability.reject);this.onProgress=null}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;const{value:t,done:e}=await this._reader.read();if(e)return{value:t,done:e};this._loaded+=t.byteLength;this.onProgress?.({loaded:this._loaded});return{value:getArrayBuffer(t),done:!1}}cancel(t){this._reader?.cancel(t);this._abortController.abort()}}class NetworkManager{_responseOrigin=null;constructor({url:t,httpHeaders:e,withCredentials:i}){this.url=t;this.isHttp=/^https?:/i.test(t);this.headers=createHeaders(this.isHttp,e);this.withCredentials=i||!1;this.currXhrId=0;this.pendingRequests=Object.create(null)}request(t){const e=new XMLHttpRequest,i=this.currXhrId++,s=this.pendingRequests[i]={xhr:e};e.open("GET",this.url);e.withCredentials=this.withCredentials;for(const[t,i]of this.headers)e.setRequestHeader(t,i);if(this.isHttp&&"begin"in t&&"end"in t){e.setRequestHeader("Range",`bytes=${t.begin}-${t.end-1}`);s.expectedStatus=206}else s.expectedStatus=200;e.responseType="arraybuffer";assert(t.onError,"Expected `onError` callback to be provided.");e.onerror=()=>{t.onError(e.status)};e.onreadystatechange=this.onStateChange.bind(this,i);e.onprogress=this.onProgress.bind(this,i);s.onHeadersReceived=t.onHeadersReceived;s.onDone=t.onDone;s.onError=t.onError;s.onProgress=t.onProgress;e.send(null);return i}onProgress(t,e){const i=this.pendingRequests[t];i&&i.onProgress?.(e)}onStateChange(t,e){const i=this.pendingRequests[t];if(!i)return;const s=i.xhr;if(s.readyState>=2&&i.onHeadersReceived){i.onHeadersReceived();delete i.onHeadersReceived}if(4!==s.readyState)return;if(!(t in this.pendingRequests))return;delete this.pendingRequests[t];if(0===s.status&&this.isHttp){i.onError(s.status);return}const n=s.status||200;if(!(200===n&&206===i.expectedStatus)&&n!==i.expectedStatus){i.onError(s.status);return}const r=function network_getArrayBuffer(t){const e=t.response;return"string"!=typeof e?e:stringToBytes(e).buffer}(s);if(206===n){const t=s.getResponseHeader("Content-Range"),e=/bytes (\d+)-(\d+)\/(\d+)/.exec(t);if(e)i.onDone({begin:parseInt(e[1],10),chunk:r});else{warn('Missing or invalid "Content-Range" header.');i.onError(0)}}else r?i.onDone({begin:0,chunk:r}):i.onError(s.status)}getRequestXhr(t){return this.pendingRequests[t].xhr}isPendingRequest(t){return t in this.pendingRequests}abortRequest(t){const e=this.pendingRequests[t].xhr;delete this.pendingRequests[t];e.abort()}}class PDFNetworkStream{constructor(t){this._source=t;this._manager=new NetworkManager(t);this._rangeChunkSize=t.rangeChunkSize;this._fullRequestReader=null;this._rangeRequestReaders=[]}_onRangeRequestReaderClosed(t){const e=this._rangeRequestReaders.indexOf(t);e>=0&&this._rangeRequestReaders.splice(e,1)}getFullReader(){assert(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once.");this._fullRequestReader=new PDFNetworkStreamFullRequestReader(this._manager,this._source);return this._fullRequestReader}getRangeReader(t,e){const i=new PDFNetworkStreamRangeRequestReader(this._manager,t,e);i.onClosed=this._onRangeRequestReaderClosed.bind(this);this._rangeRequestReaders.push(i);return i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class PDFNetworkStreamFullRequestReader{constructor(t,e){this._manager=t;this._url=e.url;this._fullRequestId=t.request({onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)});this._headersCapability=Promise.withResolvers();this._disableRange=e.disableRange||!1;this._contentLength=e.length;this._rangeChunkSize=e.rangeChunkSize;this._rangeChunkSize||this._disableRange||(this._disableRange=!0);this._isStreamingSupported=!1;this._isRangeSupported=!1;this._cachedChunks=[];this._requests=[];this._done=!1;this._storedError=void 0;this._filename=null;this.onProgress=null}_onHeadersReceived(){const t=this._fullRequestId,e=this._manager.getRequestXhr(t);this._manager._responseOrigin=getResponseOrigin(e.responseURL);const i=e.getAllResponseHeaders(),s=new Headers(i?i.trimStart().replace(/[^\S ]+$/,"").split(/[\r\n]+/).map((t=>{const[e,...i]=t.split(": ");return[e,i.join(": ")]})):[]),{allowRangeRequests:n,suggestedLength:r}=validateRangeRequestCapabilities({responseHeaders:s,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});n&&(this._isRangeSupported=!0);this._contentLength=r||this._contentLength;this._filename=extractFilenameFromHeader(s);this._isRangeSupported&&this._manager.abortRequest(t);this._headersCapability.resolve()}_onDone(t){if(t)if(this._requests.length>0){this._requests.shift().resolve({value:t.chunk,done:!1})}else this._cachedChunks.push(t.chunk);this._done=!0;if(!(this._cachedChunks.length>0)){for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}}_onError(t){this._storedError=createResponseError(t,this._url);this._headersCapability.reject(this._storedError);for(const t of this._requests)t.reject(this._storedError);this._requests.length=0;this._cachedChunks.length=0}_onProgress(t){this.onProgress?.({loaded:t.loaded,total:t.lengthComputable?t.total:this._contentLength})}get filename(){return this._filename}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}get contentLength(){return this._contentLength}get headersReady(){return this._headersCapability.promise}async read(){await this._headersCapability.promise;if(this._storedError)throw this._storedError;if(this._cachedChunks.length>0){return{value:this._cachedChunks.shift(),done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();this._requests.push(t);return t.promise}cancel(t){this._done=!0;this._headersCapability.reject(t);for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId);this._fullRequestReader=null}}class PDFNetworkStreamRangeRequestReader{constructor(t,e,i){this._manager=t;this._url=t.url;this._requestId=t.request({begin:e,end:i,onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)});this._requests=[];this._queuedChunk=null;this._done=!1;this._storedError=void 0;this.onProgress=null;this.onClosed=null}_onHeadersReceived(){const t=getResponseOrigin(this._manager.getRequestXhr(this._requestId)?.responseURL);if(t!==this._manager._responseOrigin){this._storedError=new Error(`Expected range response-origin "${t}" to match "${this._manager._responseOrigin}".`);this._onError(0)}}_close(){this.onClosed?.(this)}_onDone(t){const e=t.chunk;if(this._requests.length>0){this._requests.shift().resolve({value:e,done:!1})}else this._queuedChunk=e;this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._close()}_onError(t){this._storedError??=createResponseError(t,this._url);for(const t of this._requests)t.reject(this._storedError);this._requests.length=0;this._queuedChunk=null}_onProgress(t){this.isStreamingSupported||this.onProgress?.({loaded:t.loaded})}get isStreamingSupported(){return!1}async read(){if(this._storedError)throw this._storedError;if(null!==this._queuedChunk){const t=this._queuedChunk;this._queuedChunk=null;return{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();this._requests.push(t);return t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId);this._close()}}const At=/^[a-z][a-z0-9\-+.]+:/i;class PDFNodeStream{constructor(t){this.source=t;this.url=function parseUrlOrPath(t){if(At.test(t))return new URL(t);const e=process.getBuiltinModule("url");return new URL(e.pathToFileURL(t))}(t.url);assert("file:"===this.url.protocol,"PDFNodeStream only supports file:// URLs.");this._fullRequestReader=null;this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){assert(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once.");this._fullRequestReader=new PDFNodeStreamFsFullReader(this);return this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=new PDFNodeStreamFsRangeReader(this,t,e);this._rangeRequestReaders.push(i);return i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class PDFNodeStreamFsFullReader{constructor(t){this._url=t.url;this._done=!1;this._storedError=null;this.onProgress=null;const e=t.source;this._contentLength=e.length;this._loaded=0;this._filename=null;this._disableRange=e.disableRange||!1;this._rangeChunkSize=e.rangeChunkSize;this._rangeChunkSize||this._disableRange||(this._disableRange=!0);this._isStreamingSupported=!e.disableStream;this._isRangeSupported=!e.disableRange;this._readableStream=null;this._readCapability=Promise.withResolvers();this._headersCapability=Promise.withResolvers();const i=process.getBuiltinModule("fs");i.promises.lstat(this._url).then((t=>{this._contentLength=t.size;this._setReadableStream(i.createReadStream(this._url));this._headersCapability.resolve()}),(t=>{"ENOENT"===t.code&&(t=createResponseError(0,this._url.href));this._storedError=t;this._headersCapability.reject(t)}))}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;if(this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();if(null===t){this._readCapability=Promise.withResolvers();return this.read()}this._loaded+=t.length;this.onProgress?.({loaded:this._loaded,total:this._contentLength});return{value:new Uint8Array(t).buffer,done:!1}}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t;this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t;t.on("readable",(()=>{this._readCapability.resolve()}));t.on("end",(()=>{t.destroy();this._done=!0;this._readCapability.resolve()}));t.on("error",(t=>{this._error(t)}));!this._isStreamingSupported&&this._isRangeSupported&&this._error(new AbortException("streaming is disabled"));this._storedError&&this._readableStream.destroy(this._storedError)}}class PDFNodeStreamFsRangeReader{constructor(t,e,i){this._url=t.url;this._done=!1;this._storedError=null;this.onProgress=null;this._loaded=0;this._readableStream=null;this._readCapability=Promise.withResolvers();const s=t.source;this._isStreamingSupported=!s.disableStream;const n=process.getBuiltinModule("fs");this._setReadableStream(n.createReadStream(this._url,{start:e,end:i-1}))}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;if(this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();if(null===t){this._readCapability=Promise.withResolvers();return this.read()}this._loaded+=t.length;this.onProgress?.({loaded:this._loaded});return{value:new Uint8Array(t).buffer,done:!1}}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t;this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t;t.on("readable",(()=>{this._readCapability.resolve()}));t.on("end",(()=>{t.destroy();this._done=!0;this._readCapability.resolve()}));t.on("error",(t=>{this._error(t)}));this._storedError&&this._readableStream.destroy(this._storedError)}}class TextLayer{#Vi=Promise.withResolvers();#mt=null;#qi=!1;#Xi=!!globalThis.FontInspector?.enabled;#Ki=null;#Yi=null;#Qi=0;#Ji=0;#Zi=null;#ts=null;#es=0;#is=0;#ss=Object.create(null);#ns=[];#rs=null;#as=[];#os=new WeakMap;#ls=null;static#hs=new Map;static#cs=new Map;static#ds=new WeakMap;static#us=null;static#ps=new Set;constructor({textContentSource:t,container:e,viewport:i}){if(t instanceof ReadableStream)this.#rs=t;else{if("object"!=typeof t)throw new Error('No "textContentSource" parameter specified.');this.#rs=new ReadableStream({start(e){e.enqueue(t);e.close()}})}this.#mt=this.#ts=e;this.#is=i.scale*OutputScale.pixelRatio;this.#es=i.rotation;this.#Yi={div:null,properties:null,ctx:null};const{pageWidth:s,pageHeight:n,pageX:r,pageY:a}=i.rawDims;this.#ls=[1,0,0,-1,-r,a+n];this.#Ji=s;this.#Qi=n;TextLayer.#gs();setLayerDimensions(e,i);this.#Vi.promise.finally((()=>{TextLayer.#ps.delete(this);this.#Yi=null;this.#ss=null})).catch((()=>{}))}static get fontFamilyMap(){const{isWindows:t,isFirefox:e}=util_FeatureTest.platform;return shadow(this,"fontFamilyMap",new Map([["sans-serif",(t&&e?"Calibri, ":"")+"sans-serif"],["monospace",(t&&e?"Lucida Console, ":"")+"monospace"]]))}render(){const pump=()=>{this.#Zi.read().then((({value:t,done:e})=>{if(e)this.#Vi.resolve();else{this.#Ki??=t.lang;Object.assign(this.#ss,t.styles);this.#fs(t.items);pump()}}),this.#Vi.reject)};this.#Zi=this.#rs.getReader();TextLayer.#ps.add(this);pump();return this.#Vi.promise}update({viewport:t,onBefore:e=null}){const i=t.scale*OutputScale.pixelRatio,s=t.rotation;if(s!==this.#es){e?.();this.#es=s;setLayerDimensions(this.#ts,{rotation:s})}if(i!==this.#is){e?.();this.#is=i;const t={div:null,properties:null,ctx:TextLayer.#ms(this.#Ki)};for(const e of this.#as){t.properties=this.#os.get(e);t.div=e;this.#bs(t)}}}cancel(){const t=new AbortException("TextLayer task cancelled.");this.#Zi?.cancel(t).catch((()=>{}));this.#Zi=null;this.#Vi.reject(t)}get textDivs(){return this.#as}get textContentItemsStr(){return this.#ns}#fs(t){if(this.#qi)return;this.#Yi.ctx??=TextLayer.#ms(this.#Ki);const e=this.#as,i=this.#ns;for(const s of t){if(e.length>1e5){warn("Ignoring additional textDivs for performance reasons.");this.#qi=!0;return}if(void 0!==s.str){i.push(s.str);this.#vs(s)}else if("beginMarkedContentProps"===s.type||"beginMarkedContent"===s.type){const t=this.#mt;this.#mt=document.createElement("span");this.#mt.classList.add("markedContent");null!==s.id&&this.#mt.setAttribute("id",`${s.id}`);t.append(this.#mt)}else"endMarkedContent"===s.type&&(this.#mt=this.#mt.parentNode)}}#vs(t){const e=document.createElement("span"),i={angle:0,canvasWidth:0,hasText:""!==t.str,hasEOL:t.hasEOL,fontSize:0};this.#as.push(e);const s=Util.transform(this.#ls,t.transform);let n=Math.atan2(s[1],s[0]);const r=this.#ss[t.fontName];r.vertical&&(n+=Math.PI/2);let a=this.#Xi&&r.fontSubstitution||r.fontFamily;a=TextLayer.fontFamilyMap.get(a)||a;const o=Math.hypot(s[2],s[3]),l=o*TextLayer.#ws(a,r,this.#Ki);let h,c;if(0===n){h=s[4];c=s[5]-l}else{h=s[4]+l*Math.sin(n);c=s[5]-l*Math.cos(n)}const d="calc(var(--total-scale-factor) *",u=e.style;if(this.#mt===this.#ts){u.left=`${(100*h/this.#Ji).toFixed(2)}%`;u.top=`${(100*c/this.#Qi).toFixed(2)}%`}else{u.left=`${d}${h.toFixed(2)}px)`;u.top=`${d}${c.toFixed(2)}px)`}u.fontSize=`${d}${(TextLayer.#us*o).toFixed(2)}px)`;u.fontFamily=a;i.fontSize=o;e.setAttribute("role","presentation");e.textContent=t.str;e.dir=t.dir;this.#Xi&&(e.dataset.fontName=r.fontSubstitutionLoadedName||t.fontName);0!==n&&(i.angle=n*(180/Math.PI));let p=!1;if(t.str.length>1)p=!0;else if(" "!==t.str&&t.transform[0]!==t.transform[3]){const e=Math.abs(t.transform[0]),i=Math.abs(t.transform[3]);e!==i&&Math.max(e,i)/Math.min(e,i)>1.5&&(p=!0)}p&&(i.canvasWidth=r.vertical?t.height:t.width);this.#os.set(e,i);this.#Yi.div=e;this.#Yi.properties=i;this.#bs(this.#Yi);i.hasText&&this.#mt.append(e);if(i.hasEOL){const t=document.createElement("br");t.setAttribute("role","presentation");this.#mt.append(t)}}#bs(t){const{div:e,properties:i,ctx:s}=t,{style:n}=e;let r="";TextLayer.#us>1&&(r=`scale(${1/TextLayer.#us})`);if(0!==i.canvasWidth&&i.hasText){const{fontFamily:t}=n,{canvasWidth:a,fontSize:o}=i;TextLayer.#ys(s,o*this.#is,t);const{width:l}=s.measureText(e.textContent);l>0&&(r=`scaleX(${a*this.#is/l}) ${r}`)}0!==i.angle&&(r=`rotate(${i.angle}deg) ${r}`);r.length>0&&(n.transform=r)}static cleanup(){if(!(this.#ps.size>0)){this.#hs.clear();for(const{canvas:t}of this.#cs.values())t.remove();this.#cs.clear()}}static#ms(t=null){let e=this.#cs.get(t||="");if(!e){const i=document.createElement("canvas");i.className="hiddenCanvasElement";i.lang=t;document.body.append(i);e=i.getContext("2d",{alpha:!1,willReadFrequently:!0});this.#cs.set(t,e);this.#ds.set(e,{size:0,family:""})}return e}static#ys(t,e,i){const s=this.#ds.get(t);if(e!==s.size||i!==s.family){t.font=`${e}px ${i}`;s.size=e;s.family=i}}static#gs(){if(null!==this.#us)return;const t=document.createElement("div");t.style.opacity=0;t.style.lineHeight=1;t.style.fontSize="1px";t.style.position="absolute";t.textContent="X";document.body.append(t);this.#us=t.getBoundingClientRect().height;t.remove()}static#ws(t,e,i){const s=this.#hs.get(t);if(s)return s;const n=this.#ms(i);n.canvas.width=n.canvas.height=30;this.#ys(n,30,t);const r=n.measureText(""),a=r.fontBoundingBoxAscent,o=Math.abs(r.fontBoundingBoxDescent);n.canvas.width=n.canvas.height=0;let l=.8;if(a)l=a/(a+o);else{util_FeatureTest.platform.isFirefox&&warn("Enable the `dom.textMetrics.fontBoundingBox.enabled` preference in `about:config` to improve TextLayer rendering.");e.ascent?l=e.ascent:e.descent&&(l=1+e.descent)}this.#hs.set(t,l);return l}}class XfaText{static textContent(t){const e=[],i={items:e,styles:Object.create(null)};!function walk(t){if(!t)return;let i=null;const s=t.name;if("#text"===s)i=t.value;else{if(!XfaText.shouldBuildText(s))return;t?.attributes?.textContent?i=t.attributes.textContent:t.value&&(i=t.value)}null!==i&&e.push({str:i});if(t.children)for(const e of t.children)walk(e)}(t);return i}static shouldBuildText(t){return!("textarea"===t||"input"===t||"option"===t||"select"===t)}}function getDocument(t={}){"string"==typeof t||t instanceof URL?t={url:t}:(t instanceof ArrayBuffer||ArrayBuffer.isView(t))&&(t={data:t});const e=new PDFDocumentLoadingTask,{docId:s}=e,n=t.url?function getUrlProp(t){if(t instanceof URL)return t.href;if("string"==typeof t){if(i)return t;const e=URL.parse(t,window.location);if(e)return e.href}throw new Error("Invalid PDF url data: either string or URL-object is expected in the url property.")}(t.url):null,r=t.data?function getDataProp(t){if(i&&"undefined"!=typeof Buffer&&t instanceof Buffer)throw new Error("Please provide binary data as `Uint8Array`, rather than `Buffer`.");if(t instanceof Uint8Array&&t.byteLength===t.buffer.byteLength)return t;if("string"==typeof t)return stringToBytes(t);if(t instanceof ArrayBuffer||ArrayBuffer.isView(t)||"object"==typeof t&&!isNaN(t?.length))return new Uint8Array(t);throw new Error("Invalid PDF binary data: either TypedArray, string, or array-like object is expected in the data property.")}(t.data):null,a=t.httpHeaders||null,o=!0===t.withCredentials,l=t.password??null,h=t.range instanceof PDFDataRangeTransport?t.range:null,c=Number.isInteger(t.rangeChunkSize)&&t.rangeChunkSize>0?t.rangeChunkSize:65536;let d=t.worker instanceof PDFWorker?t.worker:null;const u=t.verbosity,p="string"!=typeof t.docBaseUrl||isDataScheme(t.docBaseUrl)?null:t.docBaseUrl,g=getFactoryUrlProp(t.cMapUrl),f=!1!==t.cMapPacked,m=t.CMapReaderFactory||(i?NodeCMapReaderFactory:DOMCMapReaderFactory),b=getFactoryUrlProp(t.iccUrl),v=getFactoryUrlProp(t.standardFontDataUrl),w=t.StandardFontDataFactory||(i?NodeStandardFontDataFactory:DOMStandardFontDataFactory),y=getFactoryUrlProp(t.wasmUrl),A=t.WasmFactory||(i?NodeWasmFactory:DOMWasmFactory),_=!0!==t.stopAtErrors,x=Number.isInteger(t.maxImageSize)&&t.maxImageSize>-1?t.maxImageSize:-1,E=!1!==t.isEvalSupported,S="boolean"==typeof t.isOffscreenCanvasSupported?t.isOffscreenCanvasSupported:!i,C="boolean"==typeof t.isImageDecoderSupported?t.isImageDecoderSupported:!i&&(util_FeatureTest.platform.isFirefox||!globalThis.chrome),T=Number.isInteger(t.canvasMaxAreaInBytes)?t.canvasMaxAreaInBytes:-1,D="boolean"==typeof t.disableFontFace?t.disableFontFace:i,M=!0===t.fontExtraProperties,P=!0===t.enableXfa,k=t.ownerDocument||globalThis.document,I=!0===t.disableRange,R=!0===t.disableStream,L=!0===t.disableAutoFetch,O=!0===t.pdfBug,N=t.CanvasFactory||(i?NodeCanvasFactory:DOMCanvasFactory),B=t.FilterFactory||(i?NodeFilterFactory:DOMFilterFactory),U=!0===t.enableHWA,H=!1!==t.useWasm,z=h?h.length:t.length??NaN,j="boolean"==typeof t.useSystemFonts?t.useSystemFonts:!i&&!D,G="boolean"==typeof t.useWorkerFetch?t.useWorkerFetch:!!(m===DOMCMapReaderFactory&&w===DOMStandardFontDataFactory&&A===DOMWasmFactory&&g&&v&&y&&isValidFetchUrl(g,document.baseURI)&&isValidFetchUrl(v,document.baseURI)&&isValidFetchUrl(y,document.baseURI));setVerbosityLevel(u);const W={canvasFactory:new N({ownerDocument:k,enableHWA:U}),filterFactory:new B({docId:s,ownerDocument:k}),cMapReaderFactory:G?null:new m({baseUrl:g,isCompressed:f}),standardFontDataFactory:G?null:new w({baseUrl:v}),wasmFactory:G?null:new A({baseUrl:y})};if(!d){const t={verbosity:u,port:GlobalWorkerOptions.workerPort};d=t.port?PDFWorker.fromPort(t):new PDFWorker(t);e._worker=d}const $={docId:s,apiVersion:"5.2.133",data:r,password:l,disableAutoFetch:L,rangeChunkSize:c,length:z,docBaseUrl:p,enableXfa:P,evaluatorOptions:{maxImageSize:x,disableFontFace:D,ignoreErrors:_,isEvalSupported:E,isOffscreenCanvasSupported:S,isImageDecoderSupported:C,canvasMaxAreaInBytes:T,fontExtraProperties:M,useSystemFonts:j,useWasm:H,useWorkerFetch:G,cMapUrl:g,iccUrl:b,standardFontDataUrl:v,wasmUrl:y}},V={ownerDocument:k,pdfBug:O,styleElement:null,loadingParams:{disableAutoFetch:L,enableXfa:P}};d.promise.then((function(){if(e.destroyed)throw new Error("Loading aborted");if(d.destroyed)throw new Error("Worker was destroyed");const t=d.messageHandler.sendWithPromise("GetDocRequest",$,r?[r.buffer]:null);let l;if(h)l=new PDFDataTransportStream(h,{disableRange:I,disableStream:R});else if(!r){if(!n)throw new Error("getDocument - no `url` parameter provided.");let t;if(i)if(isValidFetchUrl(n)){if("undefined"==typeof fetch||"undefined"==typeof Response||!("body"in Response.prototype))throw new Error("getDocument - the Fetch API was disabled in Node.js, see `--no-experimental-fetch`.");t=PDFFetchStream}else t=PDFNodeStream;else t=isValidFetchUrl(n)?PDFFetchStream:PDFNetworkStream;l=new t({url:n,length:z,httpHeaders:a,withCredentials:o,rangeChunkSize:c,disableRange:I,disableStream:R})}return t.then((t=>{if(e.destroyed)throw new Error("Loading aborted");if(d.destroyed)throw new Error("Worker was destroyed");const i=new MessageHandler(s,t,d.port),n=new WorkerTransport(i,e,l,V,W);e._transport=n;i.send("Ready",null)}))})).catch(e._capability.reject);return e}function getFactoryUrlProp(t){if("string"!=typeof t)return null;if(t.endsWith("/"))return t;throw new Error(`Invalid factory url: "${t}" must include trailing slash.`)}const isRefProxy=t=>"object"==typeof t&&Number.isInteger(t?.num)&&t.num>=0&&Number.isInteger(t?.gen)&&t.gen>=0,_t=function _isValidExplicitDest(t,e,i){if(!Array.isArray(i)||i.length<2)return!1;const[s,n,...r]=i;if(!t(s)&&!Number.isInteger(s))return!1;if(!e(n))return!1;const a=r.length;let o=!0;switch(n.name){case"XYZ":if(a<2||a>3)return!1;break;case"Fit":case"FitB":return 0===a;case"FitH":case"FitBH":case"FitV":case"FitBV":if(a>1)return!1;break;case"FitR":if(4!==a)return!1;o=!1;break;default:return!1}for(const t of r)if(!("number"==typeof t||o&&null===t))return!1;return!0}.bind(null,isRefProxy,(t=>"object"==typeof t&&"string"==typeof t?.name));class PDFDocumentLoadingTask{static#mi=0;_capability=Promise.withResolvers();_transport=null;_worker=null;docId="d"+PDFDocumentLoadingTask.#mi++;destroyed=!1;onPassword=null;onProgress=null;get promise(){return this._capability.promise}async destroy(){this.destroyed=!0;try{this._worker?.port&&(this._worker._pendingDestroy=!0);await(this._transport?.destroy())}catch(t){this._worker?.port&&delete this._worker._pendingDestroy;throw t}this._transport=null;this._worker?.destroy();this._worker=null}async getData(){return this._transport.getData()}}class PDFDataRangeTransport{constructor(t,e,i=!1,s=null){this.length=t;this.initialData=e;this.progressiveDone=i;this.contentDispositionFilename=s;this._rangeListeners=[];this._progressListeners=[];this._progressiveReadListeners=[];this._progressiveDoneListeners=[];this._readyCapability=Promise.withResolvers()}addRangeListener(t){this._rangeListeners.push(t)}addProgressListener(t){this._progressListeners.push(t)}addProgressiveReadListener(t){this._progressiveReadListeners.push(t)}addProgressiveDoneListener(t){this._progressiveDoneListeners.push(t)}onDataRange(t,e){for(const i of this._rangeListeners)i(t,e)}onDataProgress(t,e){this._readyCapability.promise.then((()=>{for(const i of this._progressListeners)i(t,e)}))}onDataProgressiveRead(t){this._readyCapability.promise.then((()=>{for(const e of this._progressiveReadListeners)e(t)}))}onDataProgressiveDone(){this._readyCapability.promise.then((()=>{for(const t of this._progressiveDoneListeners)t()}))}transportReady(){this._readyCapability.resolve()}requestDataRange(t,e){unreachable("Abstract method PDFDataRangeTransport.requestDataRange")}abort(){}}class PDFDocumentProxy{constructor(t,e){this._pdfInfo=t;this._transport=e}get annotationStorage(){return this._transport.annotationStorage}get canvasFactory(){return this._transport.canvasFactory}get filterFactory(){return this._transport.filterFactory}get numPages(){return this._pdfInfo.numPages}get fingerprints(){return this._pdfInfo.fingerprints}get isPureXfa(){return shadow(this,"isPureXfa",!!this._transport._htmlForXfa)}get allXfaHtml(){return this._transport._htmlForXfa}getPage(t){return this._transport.getPage(t)}getPageIndex(t){return this._transport.getPageIndex(t)}getDestinations(){return this._transport.getDestinations()}getDestination(t){return this._transport.getDestination(t)}getPageLabels(){return this._transport.getPageLabels()}getPageLayout(){return this._transport.getPageLayout()}getPageMode(){return this._transport.getPageMode()}getViewerPreferences(){return this._transport.getViewerPreferences()}getOpenAction(){return this._transport.getOpenAction()}getAttachments(){return this._transport.getAttachments()}getJSActions(){return this._transport.getDocJSActions()}getOutline(){return this._transport.getOutline()}getOptionalContentConfig({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getOptionalContentConfig(e)}getPermissions(){return this._transport.getPermissions()}getMetadata(){return this._transport.getMetadata()}getMarkInfo(){return this._transport.getMarkInfo()}getData(){return this._transport.getData()}saveDocument(){return this._transport.saveDocument()}getDownloadInfo(){return this._transport.downloadInfoCapability.promise}cleanup(t=!1){return this._transport.startCleanup(t||this.isPureXfa)}destroy(){return this.loadingTask.destroy()}cachedPageNumber(t){return this._transport.cachedPageNumber(t)}get loadingParams(){return this._transport.loadingParams}get loadingTask(){return this._transport.loadingTask}getFieldObjects(){return this._transport.getFieldObjects()}hasJSActions(){return this._transport.hasJSActions()}getCalculationOrderIds(){return this._transport.getCalculationOrderIds()}}class PDFPageProxy{#As=!1;constructor(t,e,i,s=!1){this._pageIndex=t;this._pageInfo=e;this._transport=i;this._stats=s?new StatTimer:null;this._pdfBug=s;this.commonObjs=i.commonObjs;this.objs=new PDFObjects;this._intentStates=new Map;this.destroyed=!1}get pageNumber(){return this._pageIndex+1}get rotate(){return this._pageInfo.rotate}get ref(){return this._pageInfo.ref}get userUnit(){return this._pageInfo.userUnit}get view(){return this._pageInfo.view}getViewport({scale:t,rotation:e=this.rotate,offsetX:i=0,offsetY:s=0,dontFlip:n=!1}={}){return new PageViewport({viewBox:this.view,userUnit:this.userUnit,scale:t,rotation:e,offsetX:i,offsetY:s,dontFlip:n})}getAnnotations({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getAnnotations(this._pageIndex,e)}getJSActions(){return this._transport.getPageJSActions(this._pageIndex)}get filterFactory(){return this._transport.filterFactory}get isPureXfa(){return shadow(this,"isPureXfa",!!this._transport._htmlForXfa)}async getXfa(){return this._transport._htmlForXfa?.children[this._pageIndex]||null}render({canvasContext:t,viewport:e,intent:i="display",annotationMode:s=p.ENABLE,transform:n=null,background:r=null,optionalContentConfigPromise:a=null,annotationCanvasMap:l=null,pageColors:h=null,printAnnotationStorage:c=null,isEditing:d=!1}){this._stats?.time("Overall");const u=this._transport.getRenderingIntent(i,s,c,d),{renderingIntent:g,cacheKey:f}=u;this.#As=!1;a||=this._transport.getOptionalContentConfig(g);let m=this._intentStates.get(f);if(!m){m=Object.create(null);this._intentStates.set(f,m)}if(m.streamReaderCancelTimeout){clearTimeout(m.streamReaderCancelTimeout);m.streamReaderCancelTimeout=null}const b=!!(g&o);if(!m.displayReadyCapability){m.displayReadyCapability=Promise.withResolvers();m.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null};this._stats?.time("Page Request");this._pumpOperatorList(u)}const complete=t=>{m.renderTasks.delete(v);b&&(this.#As=!0);this.#_s();if(t){v.capability.reject(t);this._abortOperatorList({intentState:m,reason:t instanceof Error?t:new Error(t)})}else v.capability.resolve();if(this._stats){this._stats.timeEnd("Rendering");this._stats.timeEnd("Overall");globalThis.Stats?.enabled&&globalThis.Stats.add(this.pageNumber,this._stats)}},v=new InternalRenderTask({callback:complete,params:{canvasContext:t,viewport:e,transform:n,background:r},objs:this.objs,commonObjs:this.commonObjs,annotationCanvasMap:l,operatorList:m.operatorList,pageIndex:this._pageIndex,canvasFactory:this._transport.canvasFactory,filterFactory:this._transport.filterFactory,useRequestAnimationFrame:!b,pdfBug:this._pdfBug,pageColors:h});(m.renderTasks||=new Set).add(v);const w=v.task;Promise.all([m.displayReadyCapability.promise,a]).then((([t,e])=>{if(this.destroyed)complete();else{this._stats?.time("Rendering");if(!(e.renderingIntent&g))throw new Error("Must use the same `intent`-argument when calling the `PDFPageProxy.render` and `PDFDocumentProxy.getOptionalContentConfig` methods.");v.initializeGraphics({transparency:t,optionalContentConfig:e});v.operatorListChanged()}})).catch(complete);return w}getOperatorList({intent:t="display",annotationMode:e=p.ENABLE,printAnnotationStorage:i=null,isEditing:s=!1}={}){const n=this._transport.getRenderingIntent(t,e,i,s,!0);let r,a=this._intentStates.get(n.cacheKey);if(!a){a=Object.create(null);this._intentStates.set(n.cacheKey,a)}if(!a.opListReadCapability){r=Object.create(null);r.operatorListChanged=function operatorListChanged(){if(a.operatorList.lastChunk){a.opListReadCapability.resolve(a.operatorList);a.renderTasks.delete(r)}};a.opListReadCapability=Promise.withResolvers();(a.renderTasks||=new Set).add(r);a.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null};this._stats?.time("Page Request");this._pumpOperatorList(n)}return a.opListReadCapability.promise}streamTextContent({includeMarkedContent:t=!1,disableNormalization:e=!1}={}){return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,includeMarkedContent:!0===t,disableNormalization:!0===e},{highWaterMark:100,size:t=>t.items.length})}getTextContent(t={}){if(this._transport._htmlForXfa)return this.getXfa().then((t=>XfaText.textContent(t)));const e=this.streamTextContent(t);return new Promise((function(t,i){const s=e.getReader(),n={items:[],styles:Object.create(null),lang:null};!function pump(){s.read().then((function({value:e,done:i}){if(i)t(n);else{n.lang??=e.lang;Object.assign(n.styles,e.styles);n.items.push(...e.items);pump()}}),i)}()}))}getStructTree(){return this._transport.getStructTree(this._pageIndex)}_destroy(){this.destroyed=!0;const t=[];for(const e of this._intentStates.values()){this._abortOperatorList({intentState:e,reason:new Error("Page was destroyed."),force:!0});if(!e.opListReadCapability)for(const i of e.renderTasks){t.push(i.completed);i.cancel()}}this.objs.clear();this.#As=!1;return Promise.all(t)}cleanup(t=!1){this.#As=!0;const e=this.#_s();t&&e&&(this._stats&&=new StatTimer);return e}#_s(){if(!this.#As||this.destroyed)return!1;for(const{renderTasks:t,operatorList:e}of this._intentStates.values())if(t.size>0||!e.lastChunk)return!1;this._intentStates.clear();this.objs.clear();this.#As=!1;return!0}_startRenderPage(t,e){const i=this._intentStates.get(e);if(i){this._stats?.timeEnd("Page Request");i.displayReadyCapability?.resolve(t)}}_renderPageChunk(t,e){for(let i=0,s=t.length;i<s;i++){e.operatorList.fnArray.push(t.fnArray[i]);e.operatorList.argsArray.push(t.argsArray[i])}e.operatorList.lastChunk=t.lastChunk;e.operatorList.separateAnnots=t.separateAnnots;for(const t of e.renderTasks)t.operatorListChanged();t.lastChunk&&this.#_s()}_pumpOperatorList({renderingIntent:t,cacheKey:e,annotationStorageSerializable:i,modifiedIds:s}){const{map:n,transfer:r}=i,a=this._transport.messageHandler.sendWithStream("GetOperatorList",{pageIndex:this._pageIndex,intent:t,cacheKey:e,annotationStorage:n,modifiedIds:s},r).getReader(),o=this._intentStates.get(e);o.streamReader=a;const pump=()=>{a.read().then((({value:t,done:e})=>{if(e)o.streamReader=null;else if(!this._transport.destroyed){this._renderPageChunk(t,o);pump()}}),(t=>{o.streamReader=null;if(!this._transport.destroyed){if(o.operatorList){o.operatorList.lastChunk=!0;for(const t of o.renderTasks)t.operatorListChanged();this.#_s()}if(o.displayReadyCapability)o.displayReadyCapability.reject(t);else{if(!o.opListReadCapability)throw t;o.opListReadCapability.reject(t)}}}))};pump()}_abortOperatorList({intentState:t,reason:e,force:i=!1}){if(t.streamReader){if(t.streamReaderCancelTimeout){clearTimeout(t.streamReaderCancelTimeout);t.streamReaderCancelTimeout=null}if(!i){if(t.renderTasks.size>0)return;if(e instanceof RenderingCancelledException){let i=100;e.extraDelay>0&&e.extraDelay<1e3&&(i+=e.extraDelay);t.streamReaderCancelTimeout=setTimeout((()=>{t.streamReaderCancelTimeout=null;this._abortOperatorList({intentState:t,reason:e,force:!0})}),i);return}}t.streamReader.cancel(new AbortException(e.message)).catch((()=>{}));t.streamReader=null;if(!this._transport.destroyed){for(const[e,i]of this._intentStates)if(i===t){this._intentStates.delete(e);break}this.cleanup()}}}get stats(){return this._stats}}class LoopbackPort{#xs=new Map;#Es=Promise.resolve();postMessage(t,e){const i={data:structuredClone(t,e?{transfer:e}:null)};this.#Es.then((()=>{for(const[t]of this.#xs)t.call(this,i)}))}addEventListener(t,e,i=null){let s=null;if(i?.signal instanceof AbortSignal){const{signal:n}=i;if(n.aborted){warn("LoopbackPort - cannot use an `aborted` signal.");return}const onAbort=()=>this.removeEventListener(t,e);s=()=>n.removeEventListener("abort",onAbort);n.addEventListener("abort",onAbort)}this.#xs.set(e,s)}removeEventListener(t,e){const i=this.#xs.get(e);i?.();this.#xs.delete(e)}terminate(){for(const[,t]of this.#xs)t?.();this.#xs.clear()}}class PDFWorker{static#Ss=0;static#Cs=!1;static#Ts;static{if(i){this.#Cs=!0;GlobalWorkerOptions.workerSrc||="./pdf.worker.mjs"}this._isSameOrigin=(t,e)=>{const i=URL.parse(t);if(!i?.origin||"null"===i.origin)return!1;const s=new URL(e,i);return i.origin===s.origin};this._createCDNWrapper=t=>{const e=`await import("${t}");`;return URL.createObjectURL(new Blob([e],{type:"text/javascript"}))}}constructor({name:t=null,port:e=null,verbosity:i=getVerbosityLevel()}={}){this.name=t;this.destroyed=!1;this.verbosity=i;this._readyCapability=Promise.withResolvers();this._port=null;this._webWorker=null;this._messageHandler=null;if(e){if(PDFWorker.#Ts?.has(e))throw new Error("Cannot use more than one PDFWorker per port.");(PDFWorker.#Ts||=new WeakMap).set(e,this);this._initializeFromPort(e)}else this._initialize()}get promise(){return this._readyCapability.promise}#Ds(){this._readyCapability.resolve();this._messageHandler.send("configure",{verbosity:this.verbosity})}get port(){return this._port}get messageHandler(){return this._messageHandler}_initializeFromPort(t){this._port=t;this._messageHandler=new MessageHandler("main","worker",t);this._messageHandler.on("ready",(function(){}));this.#Ds()}_initialize(){if(PDFWorker.#Cs||PDFWorker.#Ms){this._setupFakeWorker();return}let{workerSrc:t}=PDFWorker;try{PDFWorker._isSameOrigin(window.location,t)||(t=PDFWorker._createCDNWrapper(new URL(t,window.location).href));const e=new Worker(t,{type:"module"}),i=new MessageHandler("main","worker",e),terminateEarly=()=>{s.abort();i.destroy();e.terminate();this.destroyed?this._readyCapability.reject(new Error("Worker was destroyed")):this._setupFakeWorker()},s=new AbortController;e.addEventListener("error",(()=>{this._webWorker||terminateEarly()}),{signal:s.signal});i.on("test",(t=>{s.abort();if(!this.destroyed&&t){this._messageHandler=i;this._port=e;this._webWorker=e;this.#Ds()}else terminateEarly()}));i.on("ready",(t=>{s.abort();if(this.destroyed)terminateEarly();else try{sendTest()}catch{this._setupFakeWorker()}}));const sendTest=()=>{const t=new Uint8Array;i.send("test",t,[t.buffer])};sendTest();return}catch{info("The worker has been disabled.")}this._setupFakeWorker()}_setupFakeWorker(){if(!PDFWorker.#Cs){warn("Setting up fake worker.");PDFWorker.#Cs=!0}PDFWorker._setupFakeWorkerGlobal.then((t=>{if(this.destroyed){this._readyCapability.reject(new Error("Worker was destroyed"));return}const e=new LoopbackPort;this._port=e;const i="fake"+PDFWorker.#Ss++,s=new MessageHandler(i+"_worker",i,e);t.setup(s,e);this._messageHandler=new MessageHandler(i,i+"_worker",e);this.#Ds()})).catch((t=>{this._readyCapability.reject(new Error(`Setting up fake worker failed: "${t.message}".`))}))}destroy(){this.destroyed=!0;this._webWorker?.terminate();this._webWorker=null;PDFWorker.#Ts?.delete(this._port);this._port=null;this._messageHandler?.destroy();this._messageHandler=null}static fromPort(t){if(!t?.port)throw new Error("PDFWorker.fromPort - invalid method signature.");const e=this.#Ts?.get(t.port);if(e){if(e._pendingDestroy)throw new Error("PDFWorker.fromPort - the worker is being destroyed.\nPlease remember to await `PDFDocumentLoadingTask.destroy()`-calls.");return e}return new PDFWorker(t)}static get workerSrc(){if(GlobalWorkerOptions.workerSrc)return GlobalWorkerOptions.workerSrc;throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}static get#Ms(){try{return globalThis.pdfjsWorker?.WorkerMessageHandler||null}catch{return null}}static get _setupFakeWorkerGlobal(){return shadow(this,"_setupFakeWorkerGlobal",(async()=>{if(this.#Ms)return this.#Ms;return(await import(this.workerSrc)).WorkerMessageHandler})())}}class WorkerTransport{#Ps=new Map;#ks=new Map;#Is=new Map;#Rs=new Map;#Fs=null;constructor(t,e,i,s,n){this.messageHandler=t;this.loadingTask=e;this.commonObjs=new PDFObjects;this.fontLoader=new FontLoader({ownerDocument:s.ownerDocument,styleElement:s.styleElement});this.loadingParams=s.loadingParams;this._params=s;this.canvasFactory=n.canvasFactory;this.filterFactory=n.filterFactory;this.cMapReaderFactory=n.cMapReaderFactory;this.standardFontDataFactory=n.standardFontDataFactory;this.wasmFactory=n.wasmFactory;this.destroyed=!1;this.destroyCapability=null;this._networkStream=i;this._fullReader=null;this._lastProgress=null;this.downloadInfoCapability=Promise.withResolvers();this.setupMessageHandler()}#Ls(t,e=null){const i=this.#Ps.get(t);if(i)return i;const s=this.messageHandler.sendWithPromise(t,e);this.#Ps.set(t,s);return s}get annotationStorage(){return shadow(this,"annotationStorage",new AnnotationStorage)}getRenderingIntent(t,e=p.ENABLE,i=null,s=!1,n=!1){let g=a,f=K;switch(t){case"any":g=r;break;case"display":break;case"print":g=o;break;default:warn(`getRenderingIntent - invalid intent: ${t}`)}const m=g&o&&i instanceof PrintAnnotationStorage?i:this.annotationStorage;switch(e){case p.DISABLE:g+=c;break;case p.ENABLE:break;case p.ENABLE_FORMS:g+=l;break;case p.ENABLE_STORAGE:g+=h;f=m.serializable;break;default:warn(`getRenderingIntent - invalid annotationMode: ${e}`)}s&&(g+=d);n&&(g+=u);const{ids:b,hash:v}=m.modifiedIds;return{renderingIntent:g,cacheKey:[g,f.hash,v].join("_"),annotationStorageSerializable:f,modifiedIds:b}}destroy(){if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0;this.destroyCapability=Promise.withResolvers();this.#Fs?.reject(new Error("Worker was destroyed during onPassword callback"));const t=[];for(const e of this.#ks.values())t.push(e._destroy());this.#ks.clear();this.#Is.clear();this.#Rs.clear();this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();const e=this.messageHandler.sendWithPromise("Terminate",null);t.push(e);Promise.all(t).then((()=>{this.commonObjs.clear();this.fontLoader.clear();this.#Ps.clear();this.filterFactory.destroy();TextLayer.cleanup();this._networkStream?.cancelAllRequests(new AbortException("Worker was terminated."));this.messageHandler?.destroy();this.messageHandler=null;this.destroyCapability.resolve()}),this.destroyCapability.reject);return this.destroyCapability.promise}setupMessageHandler(){const{messageHandler:t,loadingTask:e}=this;t.on("GetReader",((t,e)=>{assert(this._networkStream,"GetReader - no `IPDFStream` instance available.");this._fullReader=this._networkStream.getFullReader();this._fullReader.onProgress=t=>{this._lastProgress={loaded:t.loaded,total:t.total}};e.onPull=()=>{this._fullReader.read().then((function({value:t,done:i}){if(i)e.close();else{assert(t instanceof ArrayBuffer,"GetReader - expected an ArrayBuffer.");e.enqueue(new Uint8Array(t),1,[t])}})).catch((t=>{e.error(t)}))};e.onCancel=t=>{this._fullReader.cancel(t);e.ready.catch((t=>{if(!this.destroyed)throw t}))}}));t.on("ReaderHeadersReady",(async t=>{await this._fullReader.headersReady;const{isStreamingSupported:i,isRangeSupported:s,contentLength:n}=this._fullReader;if(!i||!s){this._lastProgress&&e.onProgress?.(this._lastProgress);this._fullReader.onProgress=t=>{e.onProgress?.({loaded:t.loaded,total:t.total})}}return{isStreamingSupported:i,isRangeSupported:s,contentLength:n}}));t.on("GetRangeReader",((t,e)=>{assert(this._networkStream,"GetRangeReader - no `IPDFStream` instance available.");const i=this._networkStream.getRangeReader(t.begin,t.end);if(i){e.onPull=()=>{i.read().then((function({value:t,done:i}){if(i)e.close();else{assert(t instanceof ArrayBuffer,"GetRangeReader - expected an ArrayBuffer.");e.enqueue(new Uint8Array(t),1,[t])}})).catch((t=>{e.error(t)}))};e.onCancel=t=>{i.cancel(t);e.ready.catch((t=>{if(!this.destroyed)throw t}))}}else e.close()}));t.on("GetDoc",(({pdfInfo:t})=>{this._numPages=t.numPages;this._htmlForXfa=t.htmlForXfa;delete t.htmlForXfa;e._capability.resolve(new PDFDocumentProxy(t,this))}));t.on("DocException",(t=>{e._capability.reject(wrapReason(t))}));t.on("PasswordRequest",(t=>{this.#Fs=Promise.withResolvers();try{if(!e.onPassword)throw wrapReason(t);const updatePassword=t=>{t instanceof Error?this.#Fs.reject(t):this.#Fs.resolve({password:t})};e.onPassword(updatePassword,t.code)}catch(t){this.#Fs.reject(t)}return this.#Fs.promise}));t.on("DataLoaded",(t=>{e.onProgress?.({loaded:t.length,total:t.length});this.downloadInfoCapability.resolve(t)}));t.on("StartRenderPage",(t=>{if(this.destroyed)return;this.#ks.get(t.pageIndex)._startRenderPage(t.transparency,t.cacheKey)}));t.on("commonobj",(([e,i,s])=>{if(this.destroyed)return null;if(this.commonObjs.has(e))return null;switch(i){case"Font":if("error"in s){const t=s.error;warn(`Error during font loading: ${t}`);this.commonObjs.resolve(e,t);break}const n=this._params.pdfBug&&globalThis.FontInspector?.enabled?(t,e)=>globalThis.FontInspector.fontAdded(t,e):null,r=new FontFaceObject(s,n);this.fontLoader.bind(r).catch((()=>t.sendWithPromise("FontFallback",{id:e}))).finally((()=>{!r.fontExtraProperties&&r.data&&(r.data=null);this.commonObjs.resolve(e,r)}));break;case"CopyLocalImage":const{imageRef:a}=s;assert(a,"The imageRef must be defined.");for(const t of this.#ks.values())for(const[,i]of t.objs)if(i?.ref===a){if(!i.dataLen)return null;this.commonObjs.resolve(e,structuredClone(i));return i.dataLen}break;case"FontPath":case"Image":case"Pattern":this.commonObjs.resolve(e,s);break;default:throw new Error(`Got unknown common object type ${i}`)}return null}));t.on("obj",(([t,e,i,s])=>{if(this.destroyed)return;const n=this.#ks.get(e);if(!n.objs.has(t))if(0!==n._intentStates.size)switch(i){case"Image":case"Pattern":n.objs.resolve(t,s);break;default:throw new Error(`Got unknown object type ${i}`)}else s?.bitmap?.close()}));t.on("DocProgress",(t=>{this.destroyed||e.onProgress?.({loaded:t.loaded,total:t.total})}));t.on("FetchBinaryData",(async t=>{if(this.destroyed)throw new Error("Worker was destroyed.");const e=this[t.type];if(!e)throw new Error(`${t.type} not initialized, see the \`useWorkerFetch\` parameter.`);return e.fetch(t)}))}getData(){return this.messageHandler.sendWithPromise("GetData",null)}saveDocument(){this.annotationStorage.size<=0&&warn("saveDocument called while `annotationStorage` is empty, please use the getData-method instead.");const{map:t,transfer:e}=this.annotationStorage.serializable;return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:t,filename:this._fullReader?.filename??null},e).finally((()=>{this.annotationStorage.resetModified()}))}getPage(t){if(!Number.isInteger(t)||t<=0||t>this._numPages)return Promise.reject(new Error("Invalid page request."));const e=t-1,i=this.#Is.get(e);if(i)return i;const s=this.messageHandler.sendWithPromise("GetPage",{pageIndex:e}).then((i=>{if(this.destroyed)throw new Error("Transport destroyed");i.refStr&&this.#Rs.set(i.refStr,t);const s=new PDFPageProxy(e,i,this,this._params.pdfBug);this.#ks.set(e,s);return s}));this.#Is.set(e,s);return s}getPageIndex(t){return isRefProxy(t)?this.messageHandler.sendWithPromise("GetPageIndex",{num:t.num,gen:t.gen}):Promise.reject(new Error("Invalid pageIndex request."))}getAnnotations(t,e){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:t,intent:e})}getFieldObjects(){return this.#Ls("GetFieldObjects")}hasJSActions(){return this.#Ls("HasJSActions")}getCalculationOrderIds(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}getDestinations(){return this.messageHandler.sendWithPromise("GetDestinations",null)}getDestination(t){return"string"!=typeof t?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:t})}getPageLabels(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}getPageLayout(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}getPageMode(){return this.messageHandler.sendWithPromise("GetPageMode",null)}getViewerPreferences(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}getOpenAction(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}getAttachments(){return this.messageHandler.sendWithPromise("GetAttachments",null)}getDocJSActions(){return this.#Ls("GetDocJSActions")}getPageJSActions(t){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:t})}getStructTree(t){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:t})}getOutline(){return this.messageHandler.sendWithPromise("GetOutline",null)}getOptionalContentConfig(t){return this.#Ls("GetOptionalContentConfig").then((e=>new OptionalContentConfig(e,t)))}getPermissions(){return this.messageHandler.sendWithPromise("GetPermissions",null)}getMetadata(){const t="GetMetadata",e=this.#Ps.get(t);if(e)return e;const i=this.messageHandler.sendWithPromise(t,null).then((t=>({info:t[0],metadata:t[1]?new Metadata(t[1]):null,contentDispositionFilename:this._fullReader?.filename??null,contentLength:this._fullReader?.contentLength??null})));this.#Ps.set(t,i);return i}getMarkInfo(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}async startCleanup(t=!1){if(!this.destroyed){await this.messageHandler.sendWithPromise("Cleanup",null);for(const t of this.#ks.values()){if(!t.cleanup())throw new Error(`startCleanup: Page ${t.pageNumber} is currently rendering.`)}this.commonObjs.clear();t||this.fontLoader.clear();this.#Ps.clear();this.filterFactory.destroy(!0);TextLayer.cleanup()}}cachedPageNumber(t){if(!isRefProxy(t))return null;const e=0===t.gen?`${t.num}R`:`${t.num}R${t.gen}`;return this.#Rs.get(e)??null}}const xt=Symbol("INITIAL_DATA");class PDFObjects{#Os=Object.create(null);#Ns(t){return this.#Os[t]||={...Promise.withResolvers(),data:xt}}get(t,e=null){if(e){const i=this.#Ns(t);i.promise.then((()=>e(i.data)));return null}const i=this.#Os[t];if(!i||i.data===xt)throw new Error(`Requesting object that isn't resolved yet ${t}.`);return i.data}has(t){const e=this.#Os[t];return!!e&&e.data!==xt}delete(t){const e=this.#Os[t];if(!e||e.data===xt)return!1;delete this.#Os[t];return!0}resolve(t,e=null){const i=this.#Ns(t);i.data=e;i.resolve()}clear(){for(const t in this.#Os){const{data:e}=this.#Os[t];e?.bitmap?.close()}this.#Os=Object.create(null)}*[Symbol.iterator](){for(const t in this.#Os){const{data:e}=this.#Os[t];e!==xt&&(yield[t,e])}}}class RenderTask{#Bs=null;onContinue=null;onError=null;constructor(t){this.#Bs=t}get promise(){return this.#Bs.capability.promise}cancel(t=0){this.#Bs.cancel(null,t)}get separateAnnots(){const{separateAnnots:t}=this.#Bs.operatorList;if(!t)return!1;const{annotationCanvasMap:e}=this.#Bs;return t.form||t.canvas&&e?.size>0}}class InternalRenderTask{#Us=null;static#Hs=new WeakSet;constructor({callback:t,params:e,objs:i,commonObjs:s,annotationCanvasMap:n,operatorList:r,pageIndex:a,canvasFactory:o,filterFactory:l,useRequestAnimationFrame:h=!1,pdfBug:c=!1,pageColors:d=null}){this.callback=t;this.params=e;this.objs=i;this.commonObjs=s;this.annotationCanvasMap=n;this.operatorListIdx=null;this.operatorList=r;this._pageIndex=a;this.canvasFactory=o;this.filterFactory=l;this._pdfBug=c;this.pageColors=d;this.running=!1;this.graphicsReadyCallback=null;this.graphicsReady=!1;this._useRequestAnimationFrame=!0===h&&"undefined"!=typeof window;this.cancelled=!1;this.capability=Promise.withResolvers();this.task=new RenderTask(this);this._cancelBound=this.cancel.bind(this);this._continueBound=this._continue.bind(this);this._scheduleNextBound=this._scheduleNext.bind(this);this._nextBound=this._next.bind(this);this._canvas=e.canvasContext.canvas}get completed(){return this.capability.promise.catch((function(){}))}initializeGraphics({transparency:t=!1,optionalContentConfig:e}){if(this.cancelled)return;if(this._canvas){if(InternalRenderTask.#Hs.has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");InternalRenderTask.#Hs.add(this._canvas)}if(this._pdfBug&&globalThis.StepperManager?.enabled){this.stepper=globalThis.StepperManager.create(this._pageIndex);this.stepper.init(this.operatorList);this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint()}const{canvasContext:i,viewport:s,transform:n,background:r}=this.params;this.gfx=new CanvasGraphics(i,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:e},this.annotationCanvasMap,this.pageColors);this.gfx.beginDrawing({transform:n,viewport:s,transparency:t,background:r});this.operatorListIdx=0;this.graphicsReady=!0;this.graphicsReadyCallback?.()}cancel(t=null,e=0){this.running=!1;this.cancelled=!0;this.gfx?.endDrawing();if(this.#Us){window.cancelAnimationFrame(this.#Us);this.#Us=null}InternalRenderTask.#Hs.delete(this._canvas);t||=new RenderingCancelledException(`Rendering cancelled, page ${this._pageIndex+1}`,e);this.callback(t);this.task.onError?.(t)}operatorListChanged(){if(this.graphicsReady){this.stepper?.updateOperatorList(this.operatorList);this.running||this._continue()}else this.graphicsReadyCallback||=this._continueBound}_continue(){this.running=!0;this.cancelled||(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}_scheduleNext(){this._useRequestAnimationFrame?this.#Us=window.requestAnimationFrame((()=>{this.#Us=null;this._nextBound().catch(this._cancelBound)})):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}async _next(){if(!this.cancelled){this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper);if(this.operatorListIdx===this.operatorList.argsArray.length){this.running=!1;if(this.operatorList.lastChunk){this.gfx.endDrawing();InternalRenderTask.#Hs.delete(this._canvas);this.callback()}}}}}const Et="5.2.133",St="4f7761353";__webpack_require__(531);function makeColorComp(t){return Math.floor(255*Math.max(0,Math.min(1,t))).toString(16).padStart(2,"0")}function scaleAndClamp(t){return Math.max(0,Math.min(255,255*t))}class ColorConverters{static CMYK_G([t,e,i,s]){return["G",1-Math.min(1,.3*t+.59*i+.11*e+s)]}static G_CMYK([t]){return["CMYK",0,0,0,1-t]}static G_RGB([t]){return["RGB",t,t,t]}static G_rgb([t]){return[t=scaleAndClamp(t),t,t]}static G_HTML([t]){const e=makeColorComp(t);return`#${e}${e}${e}`}static RGB_G([t,e,i]){return["G",.3*t+.59*e+.11*i]}static RGB_rgb(t){return t.map(scaleAndClamp)}static RGB_HTML(t){return`#${t.map(makeColorComp).join("")}`}static T_HTML(){return"#00000000"}static T_rgb(){return[null]}static CMYK_RGB([t,e,i,s]){return["RGB",1-Math.min(1,t+s),1-Math.min(1,i+s),1-Math.min(1,e+s)]}static CMYK_rgb([t,e,i,s]){return[scaleAndClamp(1-Math.min(1,t+s)),scaleAndClamp(1-Math.min(1,i+s)),scaleAndClamp(1-Math.min(1,e+s))]}static CMYK_HTML(t){const e=this.CMYK_RGB(t).slice(1);return this.RGB_HTML(e)}static RGB_CMYK([t,e,i]){const s=1-t,n=1-e,r=1-i;return["CMYK",s,n,r,Math.min(s,n,r)]}}class BaseSVGFactory{create(t,e,i=!1){if(t<=0||e<=0)throw new Error("Invalid SVG dimensions");const s=this._createSVG("svg:svg");s.setAttribute("version","1.1");if(!i){s.setAttribute("width",`${t}px`);s.setAttribute("height",`${e}px`)}s.setAttribute("preserveAspectRatio","none");s.setAttribute("viewBox",`0 0 ${t} ${e}`);return s}createElement(t){if("string"!=typeof t)throw new Error("Invalid SVG element type");return this._createSVG(t)}_createSVG(t){unreachable("Abstract method `_createSVG` called.")}}class DOMSVGFactory extends BaseSVGFactory{_createSVG(t){return document.createElementNS(W,t)}}class XfaLayer{static setupStorage(t,e,i,s,n){const r=s.getValue(e,{value:null});switch(i.name){case"textarea":null!==r.value&&(t.textContent=r.value);if("print"===n)break;t.addEventListener("input",(t=>{s.setValue(e,{value:t.target.value})}));break;case"input":if("radio"===i.attributes.type||"checkbox"===i.attributes.type){r.value===i.attributes.xfaOn?t.setAttribute("checked",!0):r.value===i.attributes.xfaOff&&t.removeAttribute("checked");if("print"===n)break;t.addEventListener("change",(t=>{s.setValue(e,{value:t.target.checked?t.target.getAttribute("xfaOn"):t.target.getAttribute("xfaOff")})}))}else{null!==r.value&&t.setAttribute("value",r.value);if("print"===n)break;t.addEventListener("input",(t=>{s.setValue(e,{value:t.target.value})}))}break;case"select":if(null!==r.value){t.setAttribute("value",r.value);for(const t of i.children)t.attributes.value===r.value?t.attributes.selected=!0:t.attributes.hasOwnProperty("selected")&&delete t.attributes.selected}t.addEventListener("input",(t=>{const i=t.target.options,n=-1===i.selectedIndex?"":i[i.selectedIndex].value;s.setValue(e,{value:n})}))}}static setAttributes({html:t,element:e,storage:i=null,intent:s,linkService:n}){const{attributes:r}=e,a=t instanceof HTMLAnchorElement;"radio"===r.type&&(r.name=`${r.name}-${s}`);for(const[e,i]of Object.entries(r))if(null!=i)switch(e){case"class":i.length&&t.setAttribute(e,i.join(" "));break;case"dataId":break;case"id":t.setAttribute("data-element-id",i);break;case"style":Object.assign(t.style,i);break;case"textContent":t.textContent=i;break;default:(!a||"href"!==e&&"newWindow"!==e)&&t.setAttribute(e,i)}a&&n.addLinkAttributes(t,r.href,r.newWindow);i&&r.dataId&&this.setupStorage(t,r.dataId,e,i)}static render(t){const e=t.annotationStorage,i=t.linkService,s=t.xfaHtml,n=t.intent||"display",r=document.createElement(s.name);s.attributes&&this.setAttributes({html:r,element:s,intent:n,linkService:i});const a="richText"!==n,o=t.div;o.append(r);if(t.viewport){const e=`matrix(${t.viewport.transform.join(",")})`;o.style.transform=e}a&&o.setAttribute("class","xfaLayer xfaFont");const l=[];if(0===s.children.length){if(s.value){const t=document.createTextNode(s.value);r.append(t);a&&XfaText.shouldBuildText(s.name)&&l.push(t)}return{textDivs:l}}const h=[[s,-1,r]];for(;h.length>0;){const[t,s,r]=h.at(-1);if(s+1===t.children.length){h.pop();continue}const o=t.children[++h.at(-1)[1]];if(null===o)continue;const{name:c}=o;if("#text"===c){const t=document.createTextNode(o.value);l.push(t);r.append(t);continue}const d=o?.attributes?.xmlns?document.createElementNS(o.attributes.xmlns,c):document.createElement(c);r.append(d);o.attributes&&this.setAttributes({html:d,element:o,storage:e,intent:n,linkService:i});if(o.children?.length>0)h.push([o,-1,d]);else if(o.value){const t=document.createTextNode(o.value);a&&XfaText.shouldBuildText(c)&&l.push(t);d.append(t)}}for(const t of o.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"))t.setAttribute("readOnly",!0);return{textDivs:l}}static update(t){const e=`matrix(${t.viewport.transform.join(",")})`;t.div.style.transform=e;t.div.hidden=!1}}const Ct=1e3,Tt=new WeakSet;class AnnotationElementFactory{static create(t){switch(t.data.annotationType){case E.LINK:return new LinkAnnotationElement(t);case E.TEXT:return new TextAnnotationElement(t);case E.WIDGET:switch(t.data.fieldType){case"Tx":return new TextWidgetAnnotationElement(t);case"Btn":return t.data.radioButton?new RadioButtonWidgetAnnotationElement(t):t.data.checkBox?new CheckboxWidgetAnnotationElement(t):new PushButtonWidgetAnnotationElement(t);case"Ch":return new ChoiceWidgetAnnotationElement(t);case"Sig":return new SignatureWidgetAnnotationElement(t)}return new WidgetAnnotationElement(t);case E.POPUP:return new PopupAnnotationElement(t);case E.FREETEXT:return new FreeTextAnnotationElement(t);case E.LINE:return new LineAnnotationElement(t);case E.SQUARE:return new SquareAnnotationElement(t);case E.CIRCLE:return new CircleAnnotationElement(t);case E.POLYLINE:return new PolylineAnnotationElement(t);case E.CARET:return new CaretAnnotationElement(t);case E.INK:return new InkAnnotationElement(t);case E.POLYGON:return new PolygonAnnotationElement(t);case E.HIGHLIGHT:return new HighlightAnnotationElement(t);case E.UNDERLINE:return new UnderlineAnnotationElement(t);case E.SQUIGGLY:return new SquigglyAnnotationElement(t);case E.STRIKEOUT:return new StrikeOutAnnotationElement(t);case E.STAMP:return new StampAnnotationElement(t);case E.FILEATTACHMENT:return new FileAttachmentAnnotationElement(t);default:return new AnnotationElement(t)}}}class AnnotationElement{#zs=null;#js=!1;#Gs=null;constructor(t,{isRenderable:e=!1,ignoreBorder:i=!1,createQuadrilaterals:s=!1}={}){this.isRenderable=e;this.data=t.data;this.layer=t.layer;this.linkService=t.linkService;this.downloadManager=t.downloadManager;this.imageResourcesPath=t.imageResourcesPath;this.renderForms=t.renderForms;this.svgFactory=t.svgFactory;this.annotationStorage=t.annotationStorage;this.enableScripting=t.enableScripting;this.hasJSActions=t.hasJSActions;this._fieldObjects=t.fieldObjects;this.parent=t.parent;e&&(this.container=this._createContainer(i));s&&this._createQuadrilaterals()}static _hasPopupData({titleObj:t,contentsObj:e,richText:i}){return!!(t?.str||e?.str||i?.str)}get _isEditable(){return this.data.isEditable}get hasPopupData(){return AnnotationElement._hasPopupData(this.data)}updateEdited(t){if(!this.container)return;this.#zs||={rect:this.data.rect.slice(0)};const{rect:e}=t;e&&this.#Ws(e);this.#Gs?.popup.updateEdited(t)}resetEdited(){if(this.#zs){this.#Ws(this.#zs.rect);this.#Gs?.popup.resetEdited();this.#zs=null}}#Ws(t){const{container:{style:e},data:{rect:i,rotation:s},parent:{viewport:{rawDims:{pageWidth:n,pageHeight:r,pageX:a,pageY:o}}}}=this;i?.splice(0,4,...t);e.left=100*(t[0]-a)/n+"%";e.top=100*(r-t[3]+o)/r+"%";if(0===s){e.width=100*(t[2]-t[0])/n+"%";e.height=100*(t[3]-t[1])/r+"%"}else this.setRotation(s)}_createContainer(t){const{data:e,parent:{page:i,viewport:s}}=this,n=document.createElement("section");n.setAttribute("data-annotation-id",e.id);this instanceof WidgetAnnotationElement||(n.tabIndex=Ct);const{style:r}=n;r.zIndex=this.parent.zIndex++;e.alternativeText&&(n.title=e.alternativeText);e.noRotate&&n.classList.add("norotate");if(!e.rect||this instanceof PopupAnnotationElement){const{rotation:t}=e;e.hasOwnCanvas||0===t||this.setRotation(t,n);return n}const{width:a,height:o}=this;if(!t&&e.borderStyle.width>0){r.borderWidth=`${e.borderStyle.width}px`;const t=e.borderStyle.horizontalCornerRadius,i=e.borderStyle.verticalCornerRadius;if(t>0||i>0){const e=`calc(${t}px * var(--total-scale-factor)) / calc(${i}px * var(--total-scale-factor))`;r.borderRadius=e}else if(this instanceof RadioButtonWidgetAnnotationElement){const t=`calc(${a}px * var(--total-scale-factor)) / calc(${o}px * var(--total-scale-factor))`;r.borderRadius=t}switch(e.borderStyle.style){case S:r.borderStyle="solid";break;case C:r.borderStyle="dashed";break;case T:warn("Unimplemented border style: beveled");break;case D:warn("Unimplemented border style: inset");break;case M:r.borderBottomStyle="solid"}const s=e.borderColor||null;if(s){this.#js=!0;r.borderColor=Util.makeHexColor(0|s[0],0|s[1],0|s[2])}else r.borderWidth=0}const l=Util.normalizeRect([e.rect[0],i.view[3]-e.rect[1]+i.view[1],e.rect[2],i.view[3]-e.rect[3]+i.view[1]]),{pageWidth:h,pageHeight:c,pageX:d,pageY:u}=s.rawDims;r.left=100*(l[0]-d)/h+"%";r.top=100*(l[1]-u)/c+"%";const{rotation:p}=e;if(e.hasOwnCanvas||0===p){r.width=100*a/h+"%";r.height=100*o/c+"%"}else this.setRotation(p,n);return n}setRotation(t,e=this.container){if(!this.data.rect)return;const{pageWidth:i,pageHeight:s}=this.parent.viewport.rawDims;let{width:n,height:r}=this;t%180!=0&&([n,r]=[r,n]);e.style.width=100*n/i+"%";e.style.height=100*r/s+"%";e.setAttribute("data-main-rotation",(360-t)%360)}get _commonActions(){const setColor=(t,e,i)=>{const s=i.detail[t],n=s[0],r=s.slice(1);i.target.style[e]=ColorConverters[`${n}_HTML`](r);this.annotationStorage.setValue(this.data.id,{[e]:ColorConverters[`${n}_rgb`](r)})};return shadow(this,"_commonActions",{display:t=>{const{display:e}=t.detail,i=e%2==1;this.container.style.visibility=i?"hidden":"visible";this.annotationStorage.setValue(this.data.id,{noView:i,noPrint:1===e||2===e})},print:t=>{this.annotationStorage.setValue(this.data.id,{noPrint:!t.detail.print})},hidden:t=>{const{hidden:e}=t.detail;this.container.style.visibility=e?"hidden":"visible";this.annotationStorage.setValue(this.data.id,{noPrint:e,noView:e})},focus:t=>{setTimeout((()=>t.target.focus({preventScroll:!1})),0)},userName:t=>{t.target.title=t.detail.userName},readonly:t=>{t.target.disabled=t.detail.readonly},required:t=>{this._setRequired(t.target,t.detail.required)},bgColor:t=>{setColor("bgColor","backgroundColor",t)},fillColor:t=>{setColor("fillColor","backgroundColor",t)},fgColor:t=>{setColor("fgColor","color",t)},textColor:t=>{setColor("textColor","color",t)},borderColor:t=>{setColor("borderColor","borderColor",t)},strokeColor:t=>{setColor("strokeColor","borderColor",t)},rotation:t=>{const e=t.detail.rotation;this.setRotation(e);this.annotationStorage.setValue(this.data.id,{rotation:e})}})}_dispatchEventFromSandbox(t,e){const i=this._commonActions;for(const s of Object.keys(e.detail)){const n=t[s]||i[s];n?.(e)}}_setDefaultPropertiesFromJS(t){if(!this.enableScripting)return;const e=this.annotationStorage.getRawValue(this.data.id);if(!e)return;const i=this._commonActions;for(const[s,n]of Object.entries(e)){const r=i[s];if(r){r({detail:{[s]:n},target:t});delete e[s]}}}_createQuadrilaterals(){if(!this.container)return;const{quadPoints:t}=this.data;if(!t)return;const[e,i,s,n]=this.data.rect.map((t=>Math.fround(t)));if(8===t.length){const[r,a,o,l]=t.subarray(2,6);if(s===r&&n===a&&e===o&&i===l)return}const{style:r}=this.container;let a;if(this.#js){const{borderColor:t,borderWidth:e}=r;r.borderWidth=0;a=["url('data:image/svg+xml;utf8,",'<svg xmlns="http://www.w3.org/2000/svg"',' preserveAspectRatio="none" viewBox="0 0 1 1">',`<g fill="transparent" stroke="${t}" stroke-width="${e}">`];this.container.classList.add("hasBorder")}const o=s-e,l=n-i,{svgFactory:h}=this,c=h.createElement("svg");c.classList.add("quadrilateralsContainer");c.setAttribute("width",0);c.setAttribute("height",0);const d=h.createElement("defs");c.append(d);const u=h.createElement("clipPath"),p=`clippath_${this.data.id}`;u.setAttribute("id",p);u.setAttribute("clipPathUnits","objectBoundingBox");d.append(u);for(let i=2,s=t.length;i<s;i+=8){const s=t[i],r=t[i+1],c=t[i+2],d=t[i+3],p=h.createElement("rect"),g=(c-e)/o,f=(n-r)/l,m=(s-c)/o,b=(r-d)/l;p.setAttribute("x",g);p.setAttribute("y",f);p.setAttribute("width",m);p.setAttribute("height",b);u.append(p);a?.push(`<rect vector-effect="non-scaling-stroke" x="${g}" y="${f}" width="${m}" height="${b}"/>`)}if(this.#js){a.push("</g></svg>')");r.backgroundImage=a.join("")}this.container.append(c);this.container.style.clipPath=`url(#${p})`}_createPopup(){const{data:t}=this,e=this.#Gs=new PopupAnnotationElement({data:{color:t.color,titleObj:t.titleObj,modificationDate:t.modificationDate,contentsObj:t.contentsObj,richText:t.richText,parentRect:t.rect,borderStyle:0,id:`popup_${t.id}`,rotation:t.rotation},parent:this.parent,elements:[this]});this.parent.div.append(e.render())}render(){unreachable("Abstract method `AnnotationElement.render` called")}_getElementsByName(t,e=null){const i=[];if(this._fieldObjects){const s=this._fieldObjects[t];if(s)for(const{page:t,id:n,exportValues:r}of s){if(-1===t)continue;if(n===e)continue;const s="string"==typeof r?r:null,a=document.querySelector(`[data-element-id="${n}"]`);!a||Tt.has(a)?i.push({id:n,exportValue:s,domElement:a}):warn(`_getElementsByName - element not allowed: ${n}`)}return i}for(const s of document.getElementsByName(t)){const{exportValue:t}=s,n=s.getAttribute("data-element-id");n!==e&&(Tt.has(s)&&i.push({id:n,exportValue:t,domElement:s}))}return i}show(){this.container&&(this.container.hidden=!1);this.popup?.maybeShow()}hide(){this.container&&(this.container.hidden=!0);this.popup?.forceHide()}getElementsToTriggerPopup(){return this.container}addHighlightArea(){const t=this.getElementsToTriggerPopup();if(Array.isArray(t))for(const e of t)e.classList.add("highlightArea");else t.classList.add("highlightArea")}_editOnDoubleClick(){if(!this._isEditable)return;const{annotationEditorType:t,data:{id:e}}=this;this.container.addEventListener("dblclick",(()=>{this.linkService.eventBus?.dispatch("switchannotationeditormode",{source:this,mode:t,editId:e})}))}get width(){return this.data.rect[2]-this.data.rect[0]}get height(){return this.data.rect[3]-this.data.rect[1]}}class LinkAnnotationElement extends AnnotationElement{constructor(t,e=null){super(t,{isRenderable:!0,ignoreBorder:!!e?.ignoreBorder,createQuadrilaterals:!0});this.isTooltipOnly=t.data.isTooltipOnly}render(){const{data:t,linkService:e}=this,i=document.createElement("a");i.setAttribute("data-element-id",t.id);let s=!1;if(t.url){e.addLinkAttributes(i,t.url,t.newWindow);s=!0}else if(t.action){this._bindNamedAction(i,t.action);s=!0}else if(t.attachment){this.#$s(i,t.attachment,t.attachmentDest);s=!0}else if(t.setOCGState){this.#Vs(i,t.setOCGState);s=!0}else if(t.dest){this._bindLink(i,t.dest);s=!0}else{if(t.actions&&(t.actions.Action||t.actions["Mouse Up"]||t.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions){this._bindJSAction(i,t);s=!0}if(t.resetForm){this._bindResetFormAction(i,t.resetForm);s=!0}else if(this.isTooltipOnly&&!s){this._bindLink(i,"");s=!0}}this.container.classList.add("linkAnnotation");s&&this.container.append(i);return this.container}#qs(){this.container.setAttribute("data-internal-link","")}_bindLink(t,e){t.href=this.linkService.getDestinationHash(e);t.onclick=()=>{e&&this.linkService.goToDestination(e);return!1};(e||""===e)&&this.#qs()}_bindNamedAction(t,e){t.href=this.linkService.getAnchorUrl("");t.onclick=()=>{this.linkService.executeNamedAction(e);return!1};this.#qs()}#$s(t,e,i=null){t.href=this.linkService.getAnchorUrl("");e.description&&(t.title=e.description);t.onclick=()=>{this.downloadManager?.openOrDownloadData(e.content,e.filename,i);return!1};this.#qs()}#Vs(t,e){t.href=this.linkService.getAnchorUrl("");t.onclick=()=>{this.linkService.executeSetOCGState(e);return!1};this.#qs()}_bindJSAction(t,e){t.href=this.linkService.getAnchorUrl("");const i=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]);for(const s of Object.keys(e.actions)){const n=i.get(s);n&&(t[n]=()=>{this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e.id,name:s}});return!1})}t.onclick||(t.onclick=()=>!1);this.#qs()}_bindResetFormAction(t,e){const i=t.onclick;i||(t.href=this.linkService.getAnchorUrl(""));this.#qs();if(this._fieldObjects)t.onclick=()=>{i?.();const{fields:t,refs:s,include:n}=e,r=[];if(0!==t.length||0!==s.length){const e=new Set(s);for(const i of t){const t=this._fieldObjects[i]||[];for(const{id:i}of t)e.add(i)}for(const t of Object.values(this._fieldObjects))for(const i of t)e.has(i.id)===n&&r.push(i)}else for(const t of Object.values(this._fieldObjects))r.push(...t);const a=this.annotationStorage,o=[];for(const t of r){const{id:e}=t;o.push(e);switch(t.type){case"text":{const i=t.defaultValue||"";a.setValue(e,{value:i});break}case"checkbox":case"radiobutton":{const i=t.defaultValue===t.exportValues;a.setValue(e,{value:i});break}case"combobox":case"listbox":{const i=t.defaultValue||"";a.setValue(e,{value:i});break}default:continue}const i=document.querySelector(`[data-element-id="${e}"]`);i&&(Tt.has(i)?i.dispatchEvent(new Event("resetform")):warn(`_bindResetFormAction - element not allowed: ${e}`))}this.enableScripting&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:"app",ids:o,name:"ResetForm"}});return!1};else{warn('_bindResetFormAction - "resetForm" action not supported, ensure that the `fieldObjects` parameter is provided.');i||(t.onclick=()=>!1)}}}class TextAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0})}render(){this.container.classList.add("textAnnotation");const t=document.createElement("img");t.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg";t.setAttribute("data-l10n-id","pdfjs-text-annotation-type");t.setAttribute("data-l10n-args",JSON.stringify({type:this.data.name}));!this.data.popupRef&&this.hasPopupData&&this._createPopup();this.container.append(t);return this.container}}class WidgetAnnotationElement extends AnnotationElement{render(){return this.container}showElementAndHideCanvas(t){if(this.data.hasOwnCanvas){"CANVAS"===t.previousSibling?.nodeName&&(t.previousSibling.hidden=!0);t.hidden=!1}}_getKeyModifier(t){return util_FeatureTest.platform.isMac?t.metaKey:t.ctrlKey}_setEventListener(t,e,i,s,n){i.includes("mouse")?t.addEventListener(i,(t=>{this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:s,value:n(t),shift:t.shiftKey,modifier:this._getKeyModifier(t)}})})):t.addEventListener(i,(t=>{if("blur"===i){if(!e.focused||!t.relatedTarget)return;e.focused=!1}else if("focus"===i){if(e.focused)return;e.focused=!0}n&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:s,value:n(t)}})}))}_setEventListeners(t,e,i,s){for(const[n,r]of i)if("Action"===r||this.data.actions?.[r]){"Focus"!==r&&"Blur"!==r||(e||={focused:!1});this._setEventListener(t,e,n,r,s);"Focus"!==r||this.data.actions?.Blur?"Blur"!==r||this.data.actions?.Focus||this._setEventListener(t,e,"focus","Focus",null):this._setEventListener(t,e,"blur","Blur",null)}}_setBackgroundColor(t){const e=this.data.backgroundColor||null;t.style.backgroundColor=null===e?"transparent":Util.makeHexColor(e[0],e[1],e[2])}_setTextStyle(t){const e=["left","center","right"],{fontColor:i}=this.data.defaultAppearanceData,s=this.data.defaultAppearanceData.fontSize||9,r=t.style;let a;const roundToOneDecimal=t=>Math.round(10*t)/10;if(this.data.multiLine){const t=Math.abs(this.data.rect[3]-this.data.rect[1]-2),e=t/(Math.round(t/(n*s))||1);a=Math.min(s,roundToOneDecimal(e/n))}else{const t=Math.abs(this.data.rect[3]-this.data.rect[1]-2);a=Math.min(s,roundToOneDecimal(t/n))}r.fontSize=`calc(${a}px * var(--total-scale-factor))`;r.color=Util.makeHexColor(i[0],i[1],i[2]);null!==this.data.textAlignment&&(r.textAlign=e[this.data.textAlignment])}_setRequired(t,e){e?t.setAttribute("required",!0):t.removeAttribute("required");t.setAttribute("aria-required",e)}}class TextWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms||t.data.hasOwnCanvas||!t.data.hasAppearance&&!!t.data.fieldValue})}setPropertyOnSiblings(t,e,i,s){const n=this.annotationStorage;for(const r of this._getElementsByName(t.name,t.id)){r.domElement&&(r.domElement[e]=i);n.setValue(r.id,{[s]:i})}}render(){const t=this.annotationStorage,e=this.data.id;this.container.classList.add("textWidgetAnnotation");let i=null;if(this.renderForms){const s=t.getValue(e,{value:this.data.fieldValue});let n=s.value||"";const r=t.getValue(e,{charLimit:this.data.maxLen}).charLimit;r&&n.length>r&&(n=n.slice(0,r));let a=s.formattedValue||this.data.textContent?.join("\n")||null;a&&this.data.comb&&(a=a.replaceAll(/\s+/g,""));const o={userValue:n,formattedValue:a,lastCommittedValue:null,commitKey:1,focused:!1};if(this.data.multiLine){i=document.createElement("textarea");i.textContent=a??n;this.data.doNotScroll&&(i.style.overflowY="hidden")}else{i=document.createElement("input");i.type=this.data.password?"password":"text";i.setAttribute("value",a??n);this.data.doNotScroll&&(i.style.overflowX="hidden")}this.data.hasOwnCanvas&&(i.hidden=!0);Tt.add(i);i.setAttribute("data-element-id",e);i.disabled=this.data.readOnly;i.name=this.data.fieldName;i.tabIndex=Ct;this._setRequired(i,this.data.required);r&&(i.maxLength=r);i.addEventListener("input",(s=>{t.setValue(e,{value:s.target.value});this.setPropertyOnSiblings(i,"value",s.target.value,"value");o.formattedValue=null}));i.addEventListener("resetform",(t=>{const e=this.data.defaultFieldValue??"";i.value=o.userValue=e;o.formattedValue=null}));let blurListener=t=>{const{formattedValue:e}=o;null!=e&&(t.target.value=e);t.target.scrollLeft=0};if(this.enableScripting&&this.hasJSActions){i.addEventListener("focus",(t=>{if(o.focused)return;const{target:e}=t;o.userValue&&(e.value=o.userValue);o.lastCommittedValue=e.value;o.commitKey=1;this.data.actions?.Focus||(o.focused=!0)}));i.addEventListener("updatefromsandbox",(i=>{this.showElementAndHideCanvas(i.target);const s={value(i){o.userValue=i.detail.value??"";t.setValue(e,{value:o.userValue.toString()});i.target.value=o.userValue},formattedValue(i){const{formattedValue:s}=i.detail;o.formattedValue=s;null!=s&&i.target!==document.activeElement&&(i.target.value=s);t.setValue(e,{formattedValue:s})},selRange(t){t.target.setSelectionRange(...t.detail.selRange)},charLimit:i=>{const{charLimit:s}=i.detail,{target:n}=i;if(0===s){n.removeAttribute("maxLength");return}n.setAttribute("maxLength",s);let r=o.userValue;if(r&&!(r.length<=s)){r=r.slice(0,s);n.value=o.userValue=r;t.setValue(e,{value:r});this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:r,willCommit:!0,commitKey:1,selStart:n.selectionStart,selEnd:n.selectionEnd}})}}};this._dispatchEventFromSandbox(s,i)}));i.addEventListener("keydown",(t=>{o.commitKey=1;let i=-1;"Escape"===t.key?i=0:"Enter"!==t.key||this.data.multiLine?"Tab"===t.key&&(o.commitKey=3):i=2;if(-1===i)return;const{value:s}=t.target;if(o.lastCommittedValue!==s){o.lastCommittedValue=s;o.userValue=s;this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:s,willCommit:!0,commitKey:i,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}})}}));const s=blurListener;blurListener=null;i.addEventListener("blur",(t=>{if(!o.focused||!t.relatedTarget)return;this.data.actions?.Blur||(o.focused=!1);const{value:i}=t.target;o.userValue=i;o.lastCommittedValue!==i&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:i,willCommit:!0,commitKey:o.commitKey,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}});s(t)}));this.data.actions?.Keystroke&&i.addEventListener("beforeinput",(t=>{o.lastCommittedValue=null;const{data:i,target:s}=t,{value:n,selectionStart:r,selectionEnd:a}=s;let l=r,h=a;switch(t.inputType){case"deleteWordBackward":{const t=n.substring(0,r).match(/\w*[^\w]*$/);t&&(l-=t[0].length);break}case"deleteWordForward":{const t=n.substring(r).match(/^[^\w]*\w*/);t&&(h+=t[0].length);break}case"deleteContentBackward":r===a&&(l-=1);break;case"deleteContentForward":r===a&&(h+=1)}t.preventDefault();this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:n,change:i||"",willCommit:!1,selStart:l,selEnd:h}})}));this._setEventListeners(i,o,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.value))}blurListener&&i.addEventListener("blur",blurListener);if(this.data.comb){const t=(this.data.rect[2]-this.data.rect[0])/r;i.classList.add("comb");i.style.letterSpacing=`calc(${t}px * var(--total-scale-factor) - 1ch)`}}else{i=document.createElement("div");i.textContent=this.data.fieldValue;i.style.verticalAlign="middle";i.style.display="table-cell";this.data.hasOwnCanvas&&(i.hidden=!0)}this._setTextStyle(i);this._setBackgroundColor(i);this._setDefaultPropertiesFromJS(i);this.container.append(i);return this.container}}class SignatureWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:!!t.data.hasOwnCanvas})}}class CheckboxWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms})}render(){const t=this.annotationStorage,e=this.data,i=e.id;let s=t.getValue(i,{value:e.exportValue===e.fieldValue}).value;if("string"==typeof s){s="Off"!==s;t.setValue(i,{value:s})}this.container.classList.add("buttonWidgetAnnotation","checkBox");const n=document.createElement("input");Tt.add(n);n.setAttribute("data-element-id",i);n.disabled=e.readOnly;this._setRequired(n,this.data.required);n.type="checkbox";n.name=e.fieldName;s&&n.setAttribute("checked",!0);n.setAttribute("exportValue",e.exportValue);n.tabIndex=Ct;n.addEventListener("change",(s=>{const{name:n,checked:r}=s.target;for(const s of this._getElementsByName(n,i)){const i=r&&s.exportValue===e.exportValue;s.domElement&&(s.domElement.checked=i);t.setValue(s.id,{value:i})}t.setValue(i,{value:r})}));n.addEventListener("resetform",(t=>{const i=e.defaultFieldValue||"Off";t.target.checked=i===e.exportValue}));if(this.enableScripting&&this.hasJSActions){n.addEventListener("updatefromsandbox",(e=>{const s={value(e){e.target.checked="Off"!==e.detail.value;t.setValue(i,{value:e.target.checked})}};this._dispatchEventFromSandbox(s,e)}));this._setEventListeners(n,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.checked))}this._setBackgroundColor(n);this._setDefaultPropertiesFromJS(n);this.container.append(n);return this.container}}class RadioButtonWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("buttonWidgetAnnotation","radioButton");const t=this.annotationStorage,e=this.data,i=e.id;let s=t.getValue(i,{value:e.fieldValue===e.buttonValue}).value;if("string"==typeof s){s=s!==e.buttonValue;t.setValue(i,{value:s})}if(s)for(const s of this._getElementsByName(e.fieldName,i))t.setValue(s.id,{value:!1});const n=document.createElement("input");Tt.add(n);n.setAttribute("data-element-id",i);n.disabled=e.readOnly;this._setRequired(n,this.data.required);n.type="radio";n.name=e.fieldName;s&&n.setAttribute("checked",!0);n.tabIndex=Ct;n.addEventListener("change",(e=>{const{name:s,checked:n}=e.target;for(const e of this._getElementsByName(s,i))t.setValue(e.id,{value:!1});t.setValue(i,{value:n})}));n.addEventListener("resetform",(t=>{const i=e.defaultFieldValue;t.target.checked=null!=i&&i===e.buttonValue}));if(this.enableScripting&&this.hasJSActions){const s=e.buttonValue;n.addEventListener("updatefromsandbox",(e=>{const n={value:e=>{const n=s===e.detail.value;for(const s of this._getElementsByName(e.target.name)){const e=n&&s.id===i;s.domElement&&(s.domElement.checked=e);t.setValue(s.id,{value:e})}}};this._dispatchEventFromSandbox(n,e)}));this._setEventListeners(n,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.checked))}this._setBackgroundColor(n);this._setDefaultPropertiesFromJS(n);this.container.append(n);return this.container}}class PushButtonWidgetAnnotationElement extends LinkAnnotationElement{constructor(t){super(t,{ignoreBorder:t.data.hasAppearance})}render(){const t=super.render();t.classList.add("buttonWidgetAnnotation","pushButton");const e=t.lastChild;if(this.enableScripting&&this.hasJSActions&&e){this._setDefaultPropertiesFromJS(e);e.addEventListener("updatefromsandbox",(t=>{this._dispatchEventFromSandbox({},t)}))}return t}}class ChoiceWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("choiceWidgetAnnotation");const t=this.annotationStorage,e=this.data.id,i=t.getValue(e,{value:this.data.fieldValue}),s=document.createElement("select");Tt.add(s);s.setAttribute("data-element-id",e);s.disabled=this.data.readOnly;this._setRequired(s,this.data.required);s.name=this.data.fieldName;s.tabIndex=Ct;let n=this.data.combo&&this.data.options.length>0;if(!this.data.combo){s.size=this.data.options.length;this.data.multiSelect&&(s.multiple=!0)}s.addEventListener("resetform",(t=>{const e=this.data.defaultFieldValue;for(const t of s.options)t.selected=t.value===e}));for(const t of this.data.options){const e=document.createElement("option");e.textContent=t.displayValue;e.value=t.exportValue;if(i.value.includes(t.exportValue)){e.setAttribute("selected",!0);n=!1}s.append(e)}let r=null;if(n){const t=document.createElement("option");t.value=" ";t.setAttribute("hidden",!0);t.setAttribute("selected",!0);s.prepend(t);r=()=>{t.remove();s.removeEventListener("input",r);r=null};s.addEventListener("input",r)}const getValue=t=>{const e=t?"value":"textContent",{options:i,multiple:n}=s;return n?Array.prototype.filter.call(i,(t=>t.selected)).map((t=>t[e])):-1===i.selectedIndex?null:i[i.selectedIndex][e]};let a=getValue(!1);const getItems=t=>{const e=t.target.options;return Array.prototype.map.call(e,(t=>({displayValue:t.textContent,exportValue:t.value})))};if(this.enableScripting&&this.hasJSActions){s.addEventListener("updatefromsandbox",(i=>{const n={value(i){r?.();const n=i.detail.value,o=new Set(Array.isArray(n)?n:[n]);for(const t of s.options)t.selected=o.has(t.value);t.setValue(e,{value:getValue(!0)});a=getValue(!1)},multipleSelection(t){s.multiple=!0},remove(i){const n=s.options,r=i.detail.remove;n[r].selected=!1;s.remove(r);if(n.length>0){-1===Array.prototype.findIndex.call(n,(t=>t.selected))&&(n[0].selected=!0)}t.setValue(e,{value:getValue(!0),items:getItems(i)});a=getValue(!1)},clear(i){for(;0!==s.length;)s.remove(0);t.setValue(e,{value:null,items:[]});a=getValue(!1)},insert(i){const{index:n,displayValue:r,exportValue:o}=i.detail.insert,l=s.children[n],h=document.createElement("option");h.textContent=r;h.value=o;l?l.before(h):s.append(h);t.setValue(e,{value:getValue(!0),items:getItems(i)});a=getValue(!1)},items(i){const{items:n}=i.detail;for(;0!==s.length;)s.remove(0);for(const t of n){const{displayValue:e,exportValue:i}=t,n=document.createElement("option");n.textContent=e;n.value=i;s.append(n)}s.options.length>0&&(s.options[0].selected=!0);t.setValue(e,{value:getValue(!0),items:getItems(i)});a=getValue(!1)},indices(i){const s=new Set(i.detail.indices);for(const t of i.target.options)t.selected=s.has(t.index);t.setValue(e,{value:getValue(!0)});a=getValue(!1)},editable(t){t.target.disabled=!t.detail.editable}};this._dispatchEventFromSandbox(n,i)}));s.addEventListener("input",(i=>{const s=getValue(!0),n=getValue(!1);t.setValue(e,{value:s});i.preventDefault();this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:a,change:n,changeEx:s,willCommit:!1,commitKey:1,keyDown:!1}})}));this._setEventListeners(s,null,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"],["input","Validate"]],(t=>t.target.value))}else s.addEventListener("input",(function(i){t.setValue(e,{value:getValue(!0)})}));this.data.combo&&this._setTextStyle(s);this._setBackgroundColor(s);this._setDefaultPropertiesFromJS(s);this.container.append(s);return this.container}}class PopupAnnotationElement extends AnnotationElement{constructor(t){const{data:e,elements:i}=t;super(t,{isRenderable:AnnotationElement._hasPopupData(e)});this.elements=i;this.popup=null}render(){this.container.classList.add("popupAnnotation");const t=this.popup=new PopupElement({container:this.container,color:this.data.color,titleObj:this.data.titleObj,modificationDate:this.data.modificationDate,contentsObj:this.data.contentsObj,richText:this.data.richText,rect:this.data.rect,parentRect:this.data.parentRect||null,parent:this.parent,elements:this.elements,open:this.data.open}),e=[];for(const i of this.elements){i.popup=t;i.container.ariaHasPopup="dialog";e.push(i.data.id);i.addHighlightArea()}this.container.setAttribute("aria-controls",e.map((t=>`${G}${t}`)).join(","));return this.container}}class PopupElement{#Xs=this.#Ks.bind(this);#Ys=this.#Qs.bind(this);#Js=this.#Zs.bind(this);#tn=this.#en.bind(this);#in=null;#mt=null;#sn=null;#nn=null;#rn=null;#an=null;#on=null;#ln=!1;#hn=null;#T=null;#cn=null;#dn=null;#un=null;#zs=null;#pn=!1;constructor({container:t,color:e,elements:i,titleObj:s,modificationDate:n,contentsObj:r,richText:a,parent:o,rect:l,parentRect:h,open:c}){this.#mt=t;this.#un=s;this.#sn=r;this.#dn=a;this.#an=o;this.#in=e;this.#cn=l;this.#on=h;this.#rn=i;this.#nn=PDFDateString.toDateObject(n);this.trigger=i.flatMap((t=>t.getElementsToTriggerPopup()));for(const t of this.trigger){t.addEventListener("click",this.#tn);t.addEventListener("mouseenter",this.#Js);t.addEventListener("mouseleave",this.#Ys);t.classList.add("popupTriggerArea")}for(const t of i)t.container?.addEventListener("keydown",this.#Xs);this.#mt.hidden=!0;c&&this.#en()}render(){if(this.#hn)return;const t=this.#hn=document.createElement("div");t.className="popup";if(this.#in){const e=t.style.outlineColor=Util.makeHexColor(...this.#in);t.style.backgroundColor=`color-mix(in srgb, ${e} 30%, white)`}const e=document.createElement("span");e.className="header";const i=document.createElement("h1");e.append(i);({dir:i.dir,str:i.textContent}=this.#un);t.append(e);if(this.#nn){const t=document.createElement("span");t.classList.add("popupDate");t.setAttribute("data-l10n-id","pdfjs-annotation-date-time-string");t.setAttribute("data-l10n-args",JSON.stringify({dateObj:this.#nn.valueOf()}));e.append(t)}const s=this.#gn;if(s){XfaLayer.render({xfaHtml:s,intent:"richText",div:t});t.lastChild.classList.add("richText","popupContent")}else{const e=this._formatContents(this.#sn);t.append(e)}this.#mt.append(t)}get#gn(){const t=this.#dn,e=this.#sn;return!t?.str||e?.str&&e.str!==t.str?null:this.#dn.html||null}get#fn(){return this.#gn?.attributes?.style?.fontSize||0}get#mn(){return this.#gn?.attributes?.style?.color||null}#bn(t){const e=[],i={str:t,html:{name:"div",attributes:{dir:"auto"},children:[{name:"p",children:e}]}},s={style:{color:this.#mn,fontSize:this.#fn?`calc(${this.#fn}px * var(--total-scale-factor))`:""}};for(const i of t.split("\n"))e.push({name:"span",value:i,attributes:s});return i}_formatContents({str:t,dir:e}){const i=document.createElement("p");i.classList.add("popupContent");i.dir=e;const s=t.split(/(?:\r\n?|\n)/);for(let t=0,e=s.length;t<e;++t){const n=s[t];i.append(document.createTextNode(n));t<e-1&&i.append(document.createElement("br"))}return i}#Ks(t){t.altKey||t.shiftKey||t.ctrlKey||t.metaKey||("Enter"===t.key||"Escape"===t.key&&this.#ln)&&this.#en()}updateEdited({rect:t,popupContent:e}){this.#zs||={contentsObj:this.#sn,richText:this.#dn};t&&(this.#T=null);if(e){this.#dn=this.#bn(e);this.#sn=null}this.#hn?.remove();this.#hn=null}resetEdited(){if(this.#zs){({contentsObj:this.#sn,richText:this.#dn}=this.#zs);this.#zs=null;this.#hn?.remove();this.#hn=null;this.#T=null}}#vn(){if(null!==this.#T)return;const{page:{view:t},viewport:{rawDims:{pageWidth:e,pageHeight:i,pageX:s,pageY:n}}}=this.#an;let r=!!this.#on,a=r?this.#on:this.#cn;for(const t of this.#rn)if(!a||null!==Util.intersect(t.data.rect,a)){a=t.data.rect;r=!0;break}const o=Util.normalizeRect([a[0],t[3]-a[1]+t[1],a[2],t[3]-a[3]+t[1]]),l=r?a[2]-a[0]+5:0,h=o[0]+l,c=o[1];this.#T=[100*(h-s)/e,100*(c-n)/i];const{style:d}=this.#mt;d.left=`${this.#T[0]}%`;d.top=`${this.#T[1]}%`}#en(){this.#ln=!this.#ln;if(this.#ln){this.#Zs();this.#mt.addEventListener("click",this.#tn);this.#mt.addEventListener("keydown",this.#Xs)}else{this.#Qs();this.#mt.removeEventListener("click",this.#tn);this.#mt.removeEventListener("keydown",this.#Xs)}}#Zs(){this.#hn||this.render();if(this.isVisible)this.#ln&&this.#mt.classList.add("focused");else{this.#vn();this.#mt.hidden=!1;this.#mt.style.zIndex=parseInt(this.#mt.style.zIndex)+1e3}}#Qs(){this.#mt.classList.remove("focused");if(!this.#ln&&this.isVisible){this.#mt.hidden=!0;this.#mt.style.zIndex=parseInt(this.#mt.style.zIndex)-1e3}}forceHide(){this.#pn=this.isVisible;this.#pn&&(this.#mt.hidden=!0)}maybeShow(){if(this.#pn){this.#hn||this.#Zs();this.#pn=!1;this.#mt.hidden=!1}}get isVisible(){return!1===this.#mt.hidden}}class FreeTextAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0});this.textContent=t.data.textContent;this.textPosition=t.data.textPosition;this.annotationEditorType=g.FREETEXT}render(){this.container.classList.add("freeTextAnnotation");if(this.textContent){const t=document.createElement("div");t.classList.add("annotationTextContent");t.setAttribute("role","comment");for(const e of this.textContent){const i=document.createElement("span");i.textContent=e;t.append(i)}this.container.append(t)}!this.data.popupRef&&this.hasPopupData&&this._createPopup();this._editOnDoubleClick();return this.container}}class LineAnnotationElement extends AnnotationElement{#wn=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("lineAnnotation");const{data:t,width:e,height:i}=this,s=this.svgFactory.create(e,i,!0),n=this.#wn=this.svgFactory.createElement("svg:line");n.setAttribute("x1",t.rect[2]-t.lineCoordinates[0]);n.setAttribute("y1",t.rect[3]-t.lineCoordinates[1]);n.setAttribute("x2",t.rect[2]-t.lineCoordinates[2]);n.setAttribute("y2",t.rect[3]-t.lineCoordinates[3]);n.setAttribute("stroke-width",t.borderStyle.width||1);n.setAttribute("stroke","transparent");n.setAttribute("fill","transparent");s.append(n);this.container.append(s);!t.popupRef&&this.hasPopupData&&this._createPopup();return this.container}getElementsToTriggerPopup(){return this.#wn}addHighlightArea(){this.container.classList.add("highlightArea")}}class SquareAnnotationElement extends AnnotationElement{#yn=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("squareAnnotation");const{data:t,width:e,height:i}=this,s=this.svgFactory.create(e,i,!0),n=t.borderStyle.width,r=this.#yn=this.svgFactory.createElement("svg:rect");r.setAttribute("x",n/2);r.setAttribute("y",n/2);r.setAttribute("width",e-n);r.setAttribute("height",i-n);r.setAttribute("stroke-width",n||1);r.setAttribute("stroke","transparent");r.setAttribute("fill","transparent");s.append(r);this.container.append(s);!t.popupRef&&this.hasPopupData&&this._createPopup();return this.container}getElementsToTriggerPopup(){return this.#yn}addHighlightArea(){this.container.classList.add("highlightArea")}}class CircleAnnotationElement extends AnnotationElement{#An=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("circleAnnotation");const{data:t,width:e,height:i}=this,s=this.svgFactory.create(e,i,!0),n=t.borderStyle.width,r=this.#An=this.svgFactory.createElement("svg:ellipse");r.setAttribute("cx",e/2);r.setAttribute("cy",i/2);r.setAttribute("rx",e/2-n/2);r.setAttribute("ry",i/2-n/2);r.setAttribute("stroke-width",n||1);r.setAttribute("stroke","transparent");r.setAttribute("fill","transparent");s.append(r);this.container.append(s);!t.popupRef&&this.hasPopupData&&this._createPopup();return this.container}getElementsToTriggerPopup(){return this.#An}addHighlightArea(){this.container.classList.add("highlightArea")}}class PolylineAnnotationElement extends AnnotationElement{#_n=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0});this.containerClassName="polylineAnnotation";this.svgElementName="svg:polyline"}render(){this.container.classList.add(this.containerClassName);const{data:{rect:t,vertices:e,borderStyle:i,popupRef:s},width:n,height:r}=this;if(!e)return this.container;const a=this.svgFactory.create(n,r,!0);let o=[];for(let i=0,s=e.length;i<s;i+=2){const s=e[i]-t[0],n=t[3]-e[i+1];o.push(`${s},${n}`)}o=o.join(" ");const l=this.#_n=this.svgFactory.createElement(this.svgElementName);l.setAttribute("points",o);l.setAttribute("stroke-width",i.width||1);l.setAttribute("stroke","transparent");l.setAttribute("fill","transparent");a.append(l);this.container.append(a);!s&&this.hasPopupData&&this._createPopup();return this.container}getElementsToTriggerPopup(){return this.#_n}addHighlightArea(){this.container.classList.add("highlightArea")}}class PolygonAnnotationElement extends PolylineAnnotationElement{constructor(t){super(t);this.containerClassName="polygonAnnotation";this.svgElementName="svg:polygon"}}class CaretAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("caretAnnotation");!this.data.popupRef&&this.hasPopupData&&this._createPopup();return this.container}}class InkAnnotationElement extends AnnotationElement{#xn=null;#En=[];constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0});this.containerClassName="inkAnnotation";this.svgElementName="svg:polyline";this.annotationEditorType="InkHighlight"===this.data.it?g.HIGHLIGHT:g.INK}#Sn(t,e){switch(t){case 90:return{transform:`rotate(90) translate(${-e[0]},${e[1]}) scale(1,-1)`,width:e[3]-e[1],height:e[2]-e[0]};case 180:return{transform:`rotate(180) translate(${-e[2]},${e[1]}) scale(1,-1)`,width:e[2]-e[0],height:e[3]-e[1]};case 270:return{transform:`rotate(270) translate(${-e[2]},${e[3]}) scale(1,-1)`,width:e[3]-e[1],height:e[2]-e[0]};default:return{transform:`translate(${-e[0]},${e[3]}) scale(1,-1)`,width:e[2]-e[0],height:e[3]-e[1]}}}render(){this.container.classList.add(this.containerClassName);const{data:{rect:t,rotation:e,inkLists:i,borderStyle:s,popupRef:n}}=this,{transform:r,width:a,height:o}=this.#Sn(e,t),l=this.svgFactory.create(a,o,!0),h=this.#xn=this.svgFactory.createElement("svg:g");l.append(h);h.setAttribute("stroke-width",s.width||1);h.setAttribute("stroke-linecap","round");h.setAttribute("stroke-linejoin","round");h.setAttribute("stroke-miterlimit",10);h.setAttribute("stroke","transparent");h.setAttribute("fill","transparent");h.setAttribute("transform",r);for(let t=0,e=i.length;t<e;t++){const e=this.svgFactory.createElement(this.svgElementName);this.#En.push(e);e.setAttribute("points",i[t].join(","));h.append(e)}!n&&this.hasPopupData&&this._createPopup();this.container.append(l);this._editOnDoubleClick();return this.container}updateEdited(t){super.updateEdited(t);const{thickness:e,points:i,rect:s}=t,n=this.#xn;e>=0&&n.setAttribute("stroke-width",e||1);if(i)for(let t=0,e=this.#En.length;t<e;t++)this.#En[t].setAttribute("points",i[t].join(","));if(s){const{transform:t,width:e,height:i}=this.#Sn(this.data.rotation,s);n.parentElement.setAttribute("viewBox",`0 0 ${e} ${i}`);n.setAttribute("transform",t)}}getElementsToTriggerPopup(){return this.#En}addHighlightArea(){this.container.classList.add("highlightArea")}}class HighlightAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0});this.annotationEditorType=g.HIGHLIGHT}render(){!this.data.popupRef&&this.hasPopupData&&this._createPopup();this.container.classList.add("highlightAnnotation");this._editOnDoubleClick();return this.container}}class UnderlineAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){!this.data.popupRef&&this.hasPopupData&&this._createPopup();this.container.classList.add("underlineAnnotation");return this.container}}class SquigglyAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){!this.data.popupRef&&this.hasPopupData&&this._createPopup();this.container.classList.add("squigglyAnnotation");return this.container}}class StrikeOutAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){!this.data.popupRef&&this.hasPopupData&&this._createPopup();this.container.classList.add("strikeoutAnnotation");return this.container}}class StampAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0});this.annotationEditorType=g.STAMP}render(){this.container.classList.add("stampAnnotation");this.container.setAttribute("role","img");!this.data.popupRef&&this.hasPopupData&&this._createPopup();this._editOnDoubleClick();return this.container}}class FileAttachmentAnnotationElement extends AnnotationElement{#Cn=null;constructor(t){super(t,{isRenderable:!0});const{file:e}=this.data;this.filename=e.filename;this.content=e.content;this.linkService.eventBus?.dispatch("fileattachmentannotation",{source:this,...e})}render(){this.container.classList.add("fileAttachmentAnnotation");const{container:t,data:e}=this;let i;if(e.hasAppearance||0===e.fillAlpha)i=document.createElement("div");else{i=document.createElement("img");i.src=`${this.imageResourcesPath}annotation-${/paperclip/i.test(e.name)?"paperclip":"pushpin"}.svg`;e.fillAlpha&&e.fillAlpha<1&&(i.style=`filter: opacity(${Math.round(100*e.fillAlpha)}%);`)}i.addEventListener("dblclick",this.#Tn.bind(this));this.#Cn=i;const{isMac:s}=util_FeatureTest.platform;t.addEventListener("keydown",(t=>{"Enter"===t.key&&(s?t.metaKey:t.ctrlKey)&&this.#Tn()}));!e.popupRef&&this.hasPopupData?this._createPopup():i.classList.add("popupTriggerArea");t.append(i);return t}getElementsToTriggerPopup(){return this.#Cn}addHighlightArea(){this.container.classList.add("highlightArea")}#Tn(){this.downloadManager?.openOrDownloadData(this.content,this.filename)}}class AnnotationLayer{#Dn=null;#Mn=null;#Pn=new Map;#kn=null;constructor({div:t,accessibilityManager:e,annotationCanvasMap:i,annotationEditorUIManager:s,page:n,viewport:r,structTreeLayer:a}){this.div=t;this.#Dn=e;this.#Mn=i;this.#kn=a||null;this.page=n;this.viewport=r;this.zIndex=0;this._annotationEditorUIManager=s}hasEditableAnnotations(){return this.#Pn.size>0}async#In(t,e){const i=t.firstChild||t,s=i.id=`${G}${e}`,n=await(this.#kn?.getAriaAttributes(s));if(n)for(const[t,e]of n)i.setAttribute(t,e);this.div.append(t);this.#Dn?.moveElementInDOM(this.div,t,i,!1)}async render(t){const{annotations:e}=t,i=this.div;setLayerDimensions(i,this.viewport);const s=new Map,n={data:null,layer:i,linkService:t.linkService,downloadManager:t.downloadManager,imageResourcesPath:t.imageResourcesPath||"",renderForms:!1!==t.renderForms,svgFactory:new DOMSVGFactory,annotationStorage:t.annotationStorage||new AnnotationStorage,enableScripting:!0===t.enableScripting,hasJSActions:t.hasJSActions,fieldObjects:t.fieldObjects,parent:this,elements:null};for(const t of e){if(t.noHTML)continue;const e=t.annotationType===E.POPUP;if(e){const e=s.get(t.id);if(!e)continue;n.elements=e}else if(t.rect[2]===t.rect[0]||t.rect[3]===t.rect[1])continue;n.data=t;const i=AnnotationElementFactory.create(n);if(!i.isRenderable)continue;if(!e&&t.popupRef){const e=s.get(t.popupRef);e?e.push(i):s.set(t.popupRef,[i])}const r=i.render();t.hidden&&(r.style.visibility="hidden");await this.#In(r,t.id);if(i._isEditable){this.#Pn.set(i.data.id,i);this._annotationEditorUIManager?.renderAnnotationElement(i)}}this.#Rn()}async addLinkAnnotations(t,e){const i={data:null,layer:this.div,linkService:e,svgFactory:new DOMSVGFactory,parent:this};for(const e of t){e.borderStyle||=AnnotationLayer._defaultBorderStyle;i.data=e;const t=AnnotationElementFactory.create(i);if(!t.isRenderable)continue;const s=t.render();await this.#In(s,e.id)}}update({viewport:t}){const e=this.div;this.viewport=t;setLayerDimensions(e,{rotation:t.rotation});this.#Rn();e.hidden=!1}#Rn(){if(!this.#Mn)return;const t=this.div;for(const[e,i]of this.#Mn){const s=t.querySelector(`[data-annotation-id="${e}"]`);if(!s)continue;i.className="annotationContent";const{firstChild:n}=s;n?"CANVAS"===n.nodeName?n.replaceWith(i):n.classList.contains("annotationContent")?n.after(i):n.before(i):s.append(i);const r=this.#Pn.get(e);if(r)if(r._hasNoCanvas){this._annotationEditorUIManager?.setMissingCanvas(e,s.id,i);r._hasNoCanvas=!1}else r.canvas=i}this.#Mn.clear()}getEditableAnnotations(){return Array.from(this.#Pn.values())}getEditableAnnotation(t){return this.#Pn.get(t)}static get _defaultBorderStyle(){return shadow(this,"_defaultBorderStyle",Object.freeze({width:1,rawWidth:1,style:S,dashArray:[3],horizontalCornerRadius:0,verticalCornerRadius:0}))}}const Dt=/\r\n?|\n/g;class FreeTextEditor extends AnnotationEditor{#in;#Fn="";#Ln=`${this.id}-editor`;#On=null;#fn;static _freeTextDefaultContent="";static _internalPadding=0;static _defaultColor=null;static _defaultFontSize=10;static get _keyboardManager(){const t=FreeTextEditor.prototype,arrowChecker=t=>t.isEmpty(),e=AnnotationEditorUIManager.TRANSLATE_SMALL,i=AnnotationEditorUIManager.TRANSLATE_BIG;return shadow(this,"_keyboardManager",new KeyboardManager([[["ctrl+s","mac+meta+s","ctrl+p","mac+meta+p"],t.commitOrRemove,{bubbles:!0}],[["ctrl+Enter","mac+meta+Enter","Escape","mac+Escape"],t.commitOrRemove],[["ArrowLeft","mac+ArrowLeft"],t._translateEmpty,{args:[-e,0],checker:arrowChecker}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t._translateEmpty,{args:[-i,0],checker:arrowChecker}],[["ArrowRight","mac+ArrowRight"],t._translateEmpty,{args:[e,0],checker:arrowChecker}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t._translateEmpty,{args:[i,0],checker:arrowChecker}],[["ArrowUp","mac+ArrowUp"],t._translateEmpty,{args:[0,-e],checker:arrowChecker}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t._translateEmpty,{args:[0,-i],checker:arrowChecker}],[["ArrowDown","mac+ArrowDown"],t._translateEmpty,{args:[0,e],checker:arrowChecker}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t._translateEmpty,{args:[0,i],checker:arrowChecker}]]))}static _type="freetext";static _editorType=g.FREETEXT;constructor(t){super({...t,name:"freeTextEditor"});this.#in=t.color||FreeTextEditor._defaultColor||AnnotationEditor._defaultLineColor;this.#fn=t.fontSize||FreeTextEditor._defaultFontSize}static initialize(t,e){AnnotationEditor.initialize(t,e);const i=getComputedStyle(document.documentElement);this._internalPadding=parseFloat(i.getPropertyValue("--freetext-padding"))}static updateDefaultParams(t,e){switch(t){case f.FREETEXT_SIZE:FreeTextEditor._defaultFontSize=e;break;case f.FREETEXT_COLOR:FreeTextEditor._defaultColor=e}}updateParams(t,e){switch(t){case f.FREETEXT_SIZE:this.#Nn(e);break;case f.FREETEXT_COLOR:this.#Bn(e)}}static get defaultPropertiesToUpdate(){return[[f.FREETEXT_SIZE,FreeTextEditor._defaultFontSize],[f.FREETEXT_COLOR,FreeTextEditor._defaultColor||AnnotationEditor._defaultLineColor]]}get propertiesToUpdate(){return[[f.FREETEXT_SIZE,this.#fn],[f.FREETEXT_COLOR,this.#in]]}#Nn(t){const setFontsize=t=>{this.editorDiv.style.fontSize=`calc(${t}px * var(--total-scale-factor))`;this.translate(0,-(t-this.#fn)*this.parentScale);this.#fn=t;this.#Un()},e=this.#fn;this.addCommands({cmd:setFontsize.bind(this,t),undo:setFontsize.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:f.FREETEXT_SIZE,overwriteIfSameType:!0,keepUndo:!0})}#Bn(t){const setColor=t=>{this.#in=this.editorDiv.style.color=t},e=this.#in;this.addCommands({cmd:setColor.bind(this,t),undo:setColor.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:f.FREETEXT_COLOR,overwriteIfSameType:!0,keepUndo:!0})}_translateEmpty(t,e){this._uiManager.translateSelectedEditors(t,e,!0)}getInitialTranslation(){const t=this.parentScale;return[-FreeTextEditor._internalPadding*t,-(FreeTextEditor._internalPadding+this.#fn)*t]}rebuild(){if(this.parent){super.rebuild();null!==this.div&&(this.isAttachedToDOM||this.parent.add(this))}}enableEditMode(){if(this.isInEditMode())return;this.parent.setEditingState(!1);this.parent.updateToolbar(g.FREETEXT);super.enableEditMode();this.overlayDiv.classList.remove("enabled");this.editorDiv.contentEditable=!0;this._isDraggable=!1;this.div.removeAttribute("aria-activedescendant");this.#On=new AbortController;const t=this._uiManager.combinedSignal(this.#On);this.editorDiv.addEventListener("keydown",this.editorDivKeydown.bind(this),{signal:t});this.editorDiv.addEventListener("focus",this.editorDivFocus.bind(this),{signal:t});this.editorDiv.addEventListener("blur",this.editorDivBlur.bind(this),{signal:t});this.editorDiv.addEventListener("input",this.editorDivInput.bind(this),{signal:t});this.editorDiv.addEventListener("paste",this.editorDivPaste.bind(this),{signal:t})}disableEditMode(){if(this.isInEditMode()){this.parent.setEditingState(!0);super.disableEditMode();this.overlayDiv.classList.add("enabled");this.editorDiv.contentEditable=!1;this.div.setAttribute("aria-activedescendant",this.#Ln);this._isDraggable=!0;this.#On?.abort();this.#On=null;this.div.focus({preventScroll:!0});this.isEditing=!1;this.parent.div.classList.add("freetextEditing")}}focusin(t){if(this._focusEventsAllowed){super.focusin(t);t.target!==this.editorDiv&&this.editorDiv.focus()}}onceAdded(t){if(!this.width){this.enableEditMode();t&&this.editorDiv.focus();this._initialOptions?.isCentered&&this.center();this._initialOptions=null}}isEmpty(){return!this.editorDiv||""===this.editorDiv.innerText.trim()}remove(){this.isEditing=!1;if(this.parent){this.parent.setEditingState(!0);this.parent.div.classList.add("freetextEditing")}super.remove()}#Hn(){const t=[];this.editorDiv.normalize();let e=null;for(const i of this.editorDiv.childNodes)if(e?.nodeType!==Node.TEXT_NODE||"BR"!==i.nodeName){t.push(FreeTextEditor.#zn(i));e=i}return t.join("\n")}#Un(){const[t,e]=this.parentDimensions;let i;if(this.isAttachedToDOM)i=this.div.getBoundingClientRect();else{const{currentLayer:t,div:e}=this,s=e.style.display,n=e.classList.contains("hidden");e.classList.remove("hidden");e.style.display="hidden";t.div.append(this.div);i=e.getBoundingClientRect();e.remove();e.style.display=s;e.classList.toggle("hidden",n)}if(this.rotation%180==this.parentRotation%180){this.width=i.width/t;this.height=i.height/e}else{this.width=i.height/t;this.height=i.width/e}this.fixAndSetPosition()}commit(){if(!this.isInEditMode())return;super.commit();this.disableEditMode();const t=this.#Fn,e=this.#Fn=this.#Hn().trimEnd();if(t===e)return;const setText=t=>{this.#Fn=t;if(t){this.#jn();this._uiManager.rebuild(this);this.#Un()}else this.remove()};this.addCommands({cmd:()=>{setText(e)},undo:()=>{setText(t)},mustExec:!1});this.#Un()}shouldGetKeyboardEvents(){return this.isInEditMode()}enterInEditMode(){this.enableEditMode();this.editorDiv.focus()}dblclick(t){this.enterInEditMode()}keydown(t){if(t.target===this.div&&"Enter"===t.key){this.enterInEditMode();t.preventDefault()}}editorDivKeydown(t){FreeTextEditor._keyboardManager.exec(this,t)}editorDivFocus(t){this.isEditing=!0}editorDivBlur(t){this.isEditing=!1}editorDivInput(t){this.parent.div.classList.toggle("freetextEditing",this.isEmpty())}disableEditing(){this.editorDiv.setAttribute("role","comment");this.editorDiv.removeAttribute("aria-multiline")}enableEditing(){this.editorDiv.setAttribute("role","textbox");this.editorDiv.setAttribute("aria-multiline",!0)}render(){if(this.div)return this.div;let t,e;if(this._isCopy||this.annotationElementId){t=this.x;e=this.y}super.render();this.editorDiv=document.createElement("div");this.editorDiv.className="internal";this.editorDiv.setAttribute("id",this.#Ln);this.editorDiv.setAttribute("data-l10n-id","pdfjs-free-text2");this.editorDiv.setAttribute("data-l10n-attrs","default-content");this.enableEditing();this.editorDiv.contentEditable=!0;const{style:i}=this.editorDiv;i.fontSize=`calc(${this.#fn}px * var(--total-scale-factor))`;i.color=this.#in;this.div.append(this.editorDiv);this.overlayDiv=document.createElement("div");this.overlayDiv.classList.add("overlay","enabled");this.div.append(this.overlayDiv);bindEvents(this,this.div,["dblclick","keydown"]);if(this._isCopy||this.annotationElementId){const[i,s]=this.parentDimensions;if(this.annotationElementId){const{position:n}=this._initialData;let[r,a]=this.getInitialTranslation();[r,a]=this.pageTranslationToScreen(r,a);const[o,l]=this.pageDimensions,[h,c]=this.pageTranslation;let d,u;switch(this.rotation){case 0:d=t+(n[0]-h)/o;u=e+this.height-(n[1]-c)/l;break;case 90:d=t+(n[0]-h)/o;u=e-(n[1]-c)/l;[r,a]=[a,-r];break;case 180:d=t-this.width+(n[0]-h)/o;u=e-(n[1]-c)/l;[r,a]=[-r,-a];break;case 270:d=t+(n[0]-h-this.height*l)/o;u=e+(n[1]-c-this.width*o)/l;[r,a]=[-a,r]}this.setAt(d*i,u*s,r,a)}else this._moveAfterPaste(t,e);this.#jn();this._isDraggable=!0;this.editorDiv.contentEditable=!1}else{this._isDraggable=!1;this.editorDiv.contentEditable=!0}return this.div}static#zn(t){return(t.nodeType===Node.TEXT_NODE?t.nodeValue:t.innerText).replaceAll(Dt,"")}editorDivPaste(t){const e=t.clipboardData||window.clipboardData,{types:i}=e;if(1===i.length&&"text/plain"===i[0])return;t.preventDefault();const s=FreeTextEditor.#Gn(e.getData("text")||"").replaceAll(Dt,"\n");if(!s)return;const n=window.getSelection();if(!n.rangeCount)return;this.editorDiv.normalize();n.deleteFromDocument();const r=n.getRangeAt(0);if(!s.includes("\n")){r.insertNode(document.createTextNode(s));this.editorDiv.normalize();n.collapseToStart();return}const{startContainer:a,startOffset:o}=r,l=[],h=[];if(a.nodeType===Node.TEXT_NODE){const t=a.parentElement;h.push(a.nodeValue.slice(o).replaceAll(Dt,""));if(t!==this.editorDiv){let e=l;for(const i of this.editorDiv.childNodes)i!==t?e.push(FreeTextEditor.#zn(i)):e=h}l.push(a.nodeValue.slice(0,o).replaceAll(Dt,""))}else if(a===this.editorDiv){let t=l,e=0;for(const i of this.editorDiv.childNodes){e++===o&&(t=h);t.push(FreeTextEditor.#zn(i))}}this.#Fn=`${l.join("\n")}${s}${h.join("\n")}`;this.#jn();const c=new Range;let d=Math.sumPrecise(l.map((t=>t.length)));for(const{firstChild:t}of this.editorDiv.childNodes)if(t.nodeType===Node.TEXT_NODE){const e=t.nodeValue.length;if(d<=e){c.setStart(t,d);c.setEnd(t,d);break}d-=e}n.removeAllRanges();n.addRange(c)}#jn(){this.editorDiv.replaceChildren();if(this.#Fn)for(const t of this.#Fn.split("\n")){const e=document.createElement("div");e.append(t?document.createTextNode(t):document.createElement("br"));this.editorDiv.append(e)}}#Wn(){return this.#Fn.replaceAll(" "," ")}static#Gn(t){return t.replaceAll(" "," ")}get contentDiv(){return this.editorDiv}static async deserialize(t,e,i){let s=null;if(t instanceof FreeTextAnnotationElement){const{data:{defaultAppearanceData:{fontSize:e,fontColor:i},rect:n,rotation:r,id:a,popupRef:o},textContent:l,textPosition:h,parent:{page:{pageNumber:c}}}=t;if(!l||0===l.length)return null;s=t={annotationType:g.FREETEXT,color:Array.from(i),fontSize:e,value:l.join("\n"),position:h,pageIndex:c-1,rect:n.slice(0),rotation:r,id:a,deleted:!1,popupRef:o}}const n=await super.deserialize(t,e,i);n.#fn=t.fontSize;n.#in=Util.makeHexColor(...t.color);n.#Fn=FreeTextEditor.#Gn(t.value);n.annotationElementId=t.id||null;n._initialData=s;return n}serialize(t=!1){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const e=FreeTextEditor._internalPadding*this.parentScale,i=this.getRect(e,e),s=AnnotationEditor._colorManager.convert(this.isAttachedToDOM?getComputedStyle(this.editorDiv).color:this.#in),n={annotationType:g.FREETEXT,color:s,fontSize:this.#fn,value:this.#Wn(),pageIndex:this.pageIndex,rect:i,rotation:this.rotation,structTreeParentId:this._structTreeParentId};if(t){n.isCopy=!0;return n}if(this.annotationElementId&&!this.#$n(n))return null;n.id=this.annotationElementId;return n}#$n(t){const{value:e,fontSize:i,color:s,pageIndex:n}=this._initialData;return this._hasBeenMoved||t.value!==e||t.fontSize!==i||t.color.some(((t,e)=>t!==s[e]))||t.pageIndex!==n}renderAnnotationElement(t){const e=super.renderAnnotationElement(t);if(this.deleted)return e;const{style:i}=e;i.fontSize=`calc(${this.#fn}px * var(--total-scale-factor))`;i.color=this.#in;e.replaceChildren();for(const t of this.#Fn.split("\n")){const i=document.createElement("div");i.append(t?document.createTextNode(t):document.createElement("br"));e.append(i)}const s=FreeTextEditor._internalPadding*this.parentScale;t.updateEdited({rect:this.getRect(s,s),popupContent:this.#Fn});return e}resetAnnotationElement(t){super.resetAnnotationElement(t);t.resetEdited()}}class Outline{static PRECISION=1e-4;toSVGPath(){unreachable("Abstract method `toSVGPath` must be implemented.")}get box(){unreachable("Abstract getter `box` must be implemented.")}serialize(t,e){unreachable("Abstract method `serialize` must be implemented.")}static _rescale(t,e,i,s,n,r){r||=new Float32Array(t.length);for(let a=0,o=t.length;a<o;a+=2){r[a]=e+t[a]*s;r[a+1]=i+t[a+1]*n}return r}static _rescaleAndSwap(t,e,i,s,n,r){r||=new Float32Array(t.length);for(let a=0,o=t.length;a<o;a+=2){r[a]=e+t[a+1]*s;r[a+1]=i+t[a]*n}return r}static _translate(t,e,i,s){s||=new Float32Array(t.length);for(let n=0,r=t.length;n<r;n+=2){s[n]=e+t[n];s[n+1]=i+t[n+1]}return s}static svgRound(t){return Math.round(1e4*t)}static _normalizePoint(t,e,i,s,n){switch(n){case 90:return[1-e/i,t/s];case 180:return[1-t/i,1-e/s];case 270:return[e/i,1-t/s];default:return[t/i,e/s]}}static _normalizePagePoint(t,e,i){switch(i){case 90:return[1-e,t];case 180:return[1-t,1-e];case 270:return[e,1-t];default:return[t,e]}}static createBezierPoints(t,e,i,s,n,r){return[(t+5*i)/6,(e+5*s)/6,(5*i+n)/6,(5*s+r)/6,(i+n)/2,(s+r)/2]}}class FreeDrawOutliner{#Vn;#qn=[];#Xn;#Kn;#Yn=[];#Qn=new Float32Array(18);#Jn;#Zn;#tr;#er;#ir;#sr;#nr=[];static#rr=8;static#ar=2;static#or=FreeDrawOutliner.#rr+FreeDrawOutliner.#ar;constructor({x:t,y:e},i,s,n,r,a=0){this.#Vn=i;this.#sr=n*s;this.#Kn=r;this.#Qn.set([NaN,NaN,NaN,NaN,t,e],6);this.#Xn=a;this.#er=FreeDrawOutliner.#rr*s;this.#tr=FreeDrawOutliner.#or*s;this.#ir=s;this.#nr.push(t,e)}isEmpty(){return isNaN(this.#Qn[8])}#lr(){const t=this.#Qn.subarray(4,6),e=this.#Qn.subarray(16,18),[i,s,n,r]=this.#Vn;return[(this.#Jn+(t[0]-e[0])/2-i)/n,(this.#Zn+(t[1]-e[1])/2-s)/r,(this.#Jn+(e[0]-t[0])/2-i)/n,(this.#Zn+(e[1]-t[1])/2-s)/r]}add({x:t,y:e}){this.#Jn=t;this.#Zn=e;const[i,s,n,r]=this.#Vn;let[a,o,l,h]=this.#Qn.subarray(8,12);const c=t-l,d=e-h,u=Math.hypot(c,d);if(u<this.#tr)return!1;const p=u-this.#er,g=p/u,f=g*c,m=g*d;let b=a,v=o;a=l;o=h;l+=f;h+=m;this.#nr?.push(t,e);const w=f/p,y=-m/p*this.#sr,A=w*this.#sr;this.#Qn.set(this.#Qn.subarray(2,8),0);this.#Qn.set([l+y,h+A],4);this.#Qn.set(this.#Qn.subarray(14,18),12);this.#Qn.set([l-y,h-A],16);if(isNaN(this.#Qn[6])){if(0===this.#Yn.length){this.#Qn.set([a+y,o+A],2);this.#Yn.push(NaN,NaN,NaN,NaN,(a+y-i)/n,(o+A-s)/r);this.#Qn.set([a-y,o-A],14);this.#qn.push(NaN,NaN,NaN,NaN,(a-y-i)/n,(o-A-s)/r)}this.#Qn.set([b,v,a,o,l,h],6);return!this.isEmpty()}this.#Qn.set([b,v,a,o,l,h],6);if(Math.abs(Math.atan2(v-o,b-a)-Math.atan2(m,f))<Math.PI/2){[a,o,l,h]=this.#Qn.subarray(2,6);this.#Yn.push(NaN,NaN,NaN,NaN,((a+l)/2-i)/n,((o+h)/2-s)/r);[a,o,b,v]=this.#Qn.subarray(14,18);this.#qn.push(NaN,NaN,NaN,NaN,((b+a)/2-i)/n,((v+o)/2-s)/r);return!0}[b,v,a,o,l,h]=this.#Qn.subarray(0,6);this.#Yn.push(((b+5*a)/6-i)/n,((v+5*o)/6-s)/r,((5*a+l)/6-i)/n,((5*o+h)/6-s)/r,((a+l)/2-i)/n,((o+h)/2-s)/r);[l,h,a,o,b,v]=this.#Qn.subarray(12,18);this.#qn.push(((b+5*a)/6-i)/n,((v+5*o)/6-s)/r,((5*a+l)/6-i)/n,((5*o+h)/6-s)/r,((a+l)/2-i)/n,((o+h)/2-s)/r);return!0}toSVGPath(){if(this.isEmpty())return"";const t=this.#Yn,e=this.#qn;if(isNaN(this.#Qn[6])&&!this.isEmpty())return this.#hr();const i=[];i.push(`M${t[4]} ${t[5]}`);for(let e=6;e<t.length;e+=6)isNaN(t[e])?i.push(`L${t[e+4]} ${t[e+5]}`):i.push(`C${t[e]} ${t[e+1]} ${t[e+2]} ${t[e+3]} ${t[e+4]} ${t[e+5]}`);this.#cr(i);for(let t=e.length-6;t>=6;t-=6)isNaN(e[t])?i.push(`L${e[t+4]} ${e[t+5]}`):i.push(`C${e[t]} ${e[t+1]} ${e[t+2]} ${e[t+3]} ${e[t+4]} ${e[t+5]}`);this.#dr(i);return i.join(" ")}#hr(){const[t,e,i,s]=this.#Vn,[n,r,a,o]=this.#lr();return`M${(this.#Qn[2]-t)/i} ${(this.#Qn[3]-e)/s} L${(this.#Qn[4]-t)/i} ${(this.#Qn[5]-e)/s} L${n} ${r} L${a} ${o} L${(this.#Qn[16]-t)/i} ${(this.#Qn[17]-e)/s} L${(this.#Qn[14]-t)/i} ${(this.#Qn[15]-e)/s} Z`}#dr(t){const e=this.#qn;t.push(`L${e[4]} ${e[5]} Z`)}#cr(t){const[e,i,s,n]=this.#Vn,r=this.#Qn.subarray(4,6),a=this.#Qn.subarray(16,18),[o,l,h,c]=this.#lr();t.push(`L${(r[0]-e)/s} ${(r[1]-i)/n} L${o} ${l} L${h} ${c} L${(a[0]-e)/s} ${(a[1]-i)/n}`)}newFreeDrawOutline(t,e,i,s,n,r){return new FreeDrawOutline(t,e,i,s,n,r)}getOutlines(){const t=this.#Yn,e=this.#qn,i=this.#Qn,[s,n,r,a]=this.#Vn,o=new Float32Array((this.#nr?.length??0)+2);for(let t=0,e=o.length-2;t<e;t+=2){o[t]=(this.#nr[t]-s)/r;o[t+1]=(this.#nr[t+1]-n)/a}o[o.length-2]=(this.#Jn-s)/r;o[o.length-1]=(this.#Zn-n)/a;if(isNaN(i[6])&&!this.isEmpty())return this.#ur(o);const l=new Float32Array(this.#Yn.length+24+this.#qn.length);let h=t.length;for(let e=0;e<h;e+=2)if(isNaN(t[e]))l[e]=l[e+1]=NaN;else{l[e]=t[e];l[e+1]=t[e+1]}h=this.#pr(l,h);for(let t=e.length-6;t>=6;t-=6)for(let i=0;i<6;i+=2)if(isNaN(e[t+i])){l[h]=l[h+1]=NaN;h+=2}else{l[h]=e[t+i];l[h+1]=e[t+i+1];h+=2}this.#gr(l,h);return this.newFreeDrawOutline(l,o,this.#Vn,this.#ir,this.#Xn,this.#Kn)}#ur(t){const e=this.#Qn,[i,s,n,r]=this.#Vn,[a,o,l,h]=this.#lr(),c=new Float32Array(36);c.set([NaN,NaN,NaN,NaN,(e[2]-i)/n,(e[3]-s)/r,NaN,NaN,NaN,NaN,(e[4]-i)/n,(e[5]-s)/r,NaN,NaN,NaN,NaN,a,o,NaN,NaN,NaN,NaN,l,h,NaN,NaN,NaN,NaN,(e[16]-i)/n,(e[17]-s)/r,NaN,NaN,NaN,NaN,(e[14]-i)/n,(e[15]-s)/r],0);return this.newFreeDrawOutline(c,t,this.#Vn,this.#ir,this.#Xn,this.#Kn)}#gr(t,e){const i=this.#qn;t.set([NaN,NaN,NaN,NaN,i[4],i[5]],e);return e+6}#pr(t,e){const i=this.#Qn.subarray(4,6),s=this.#Qn.subarray(16,18),[n,r,a,o]=this.#Vn,[l,h,c,d]=this.#lr();t.set([NaN,NaN,NaN,NaN,(i[0]-n)/a,(i[1]-r)/o,NaN,NaN,NaN,NaN,l,h,NaN,NaN,NaN,NaN,c,d,NaN,NaN,NaN,NaN,(s[0]-n)/a,(s[1]-r)/o],e);return e+24}}class FreeDrawOutline extends Outline{#Vn;#fr=new Float32Array(4);#Xn;#Kn;#nr;#ir;#mr;constructor(t,e,i,s,n,r){super();this.#mr=t;this.#nr=e;this.#Vn=i;this.#ir=s;this.#Xn=n;this.#Kn=r;this.lastPoint=[NaN,NaN];this.#br(r);const[a,o,l,h]=this.#fr;for(let e=0,i=t.length;e<i;e+=2){t[e]=(t[e]-a)/l;t[e+1]=(t[e+1]-o)/h}for(let t=0,i=e.length;t<i;t+=2){e[t]=(e[t]-a)/l;e[t+1]=(e[t+1]-o)/h}}toSVGPath(){const t=[`M${this.#mr[4]} ${this.#mr[5]}`];for(let e=6,i=this.#mr.length;e<i;e+=6)isNaN(this.#mr[e])?t.push(`L${this.#mr[e+4]} ${this.#mr[e+5]}`):t.push(`C${this.#mr[e]} ${this.#mr[e+1]} ${this.#mr[e+2]} ${this.#mr[e+3]} ${this.#mr[e+4]} ${this.#mr[e+5]}`);t.push("Z");return t.join(" ")}serialize([t,e,i,s],n){const r=i-t,a=s-e;let o,l;switch(n){case 0:o=Outline._rescale(this.#mr,t,s,r,-a);l=Outline._rescale(this.#nr,t,s,r,-a);break;case 90:o=Outline._rescaleAndSwap(this.#mr,t,e,r,a);l=Outline._rescaleAndSwap(this.#nr,t,e,r,a);break;case 180:o=Outline._rescale(this.#mr,i,e,-r,a);l=Outline._rescale(this.#nr,i,e,-r,a);break;case 270:o=Outline._rescaleAndSwap(this.#mr,i,s,-r,-a);l=Outline._rescaleAndSwap(this.#nr,i,s,-r,-a)}return{outline:Array.from(o),points:[Array.from(l)]}}#br(t){const e=this.#mr;let i=e[4],s=e[5];const n=[i,s,i,s];let r=i,a=s;const o=t?Math.max:Math.min;for(let t=6,l=e.length;t<l;t+=6){const l=e[t+4],h=e[t+5];if(isNaN(e[t])){Util.pointBoundingBox(l,h,n);if(a<h){r=l;a=h}else a===h&&(r=o(r,l))}else{const l=[1/0,1/0,-1/0,-1/0];Util.bezierBoundingBox(i,s,...e.slice(t,t+6),l);Util.rectBoundingBox(...l,n);if(a<l[3]){r=l[2];a=l[3]}else a===l[3]&&(r=o(r,l[2]))}i=l;s=h}const l=this.#fr;l[0]=n[0]-this.#Xn;l[1]=n[1]-this.#Xn;l[2]=n[2]-n[0]+2*this.#Xn;l[3]=n[3]-n[1]+2*this.#Xn;this.lastPoint=[r,a]}get box(){return this.#fr}newOutliner(t,e,i,s,n,r=0){return new FreeDrawOutliner(t,e,i,s,n,r)}getNewOutline(t,e){const[i,s,n,r]=this.#fr,[a,o,l,h]=this.#Vn,c=n*l,d=r*h,u=i*l+a,p=s*h+o,g=this.newOutliner({x:this.#nr[0]*c+u,y:this.#nr[1]*d+p},this.#Vn,this.#ir,t,this.#Kn,e??this.#Xn);for(let t=2;t<this.#nr.length;t+=2)g.add({x:this.#nr[t]*c+u,y:this.#nr[t+1]*d+p});return g.getOutlines()}}class HighlightOutliner{#Vn;#vr;#wr=[];#yr=[];constructor(t,e=0,i=0,s=!0){const n=[1/0,1/0,-1/0,-1/0],r=10**-4;for(const{x:i,y:s,width:a,height:o}of t){const t=Math.floor((i-e)/r)*r,l=Math.ceil((i+a+e)/r)*r,h=Math.floor((s-e)/r)*r,c=Math.ceil((s+o+e)/r)*r,d=[t,h,c,!0],u=[l,h,c,!1];this.#wr.push(d,u);Util.rectBoundingBox(t,h,l,c,n)}const a=n[2]-n[0]+2*i,o=n[3]-n[1]+2*i,l=n[0]-i,h=n[1]-i,c=this.#wr.at(s?-1:-2),d=[c[0],c[2]];for(const t of this.#wr){const[e,i,s]=t;t[0]=(e-l)/a;t[1]=(i-h)/o;t[2]=(s-h)/o}this.#Vn=new Float32Array([l,h,a,o]);this.#vr=d}getOutlines(){this.#wr.sort(((t,e)=>t[0]-e[0]||t[1]-e[1]||t[2]-e[2]));const t=[];for(const e of this.#wr)if(e[3]){t.push(...this.#Ar(e));this.#_r(e)}else{this.#xr(e);t.push(...this.#Ar(e))}return this.#Er(t)}#Er(t){const e=[],i=new Set;for(const i of t){const[t,s,n]=i;e.push([t,s,i],[t,n,i])}e.sort(((t,e)=>t[1]-e[1]||t[0]-e[0]));for(let t=0,s=e.length;t<s;t+=2){const s=e[t][2],n=e[t+1][2];s.push(n);n.push(s);i.add(s);i.add(n)}const s=[];let n;for(;i.size>0;){const t=i.values().next().value;let[e,r,a,o,l]=t;i.delete(t);let h=e,c=r;n=[e,a];s.push(n);for(;;){let t;if(i.has(o))t=o;else{if(!i.has(l))break;t=l}i.delete(t);[e,r,a,o,l]=t;if(h!==e){n.push(h,c,e,c===r?r:a);h=e}c=c===r?a:r}n.push(h,c)}return new HighlightOutline(s,this.#Vn,this.#vr)}#Sr(t){const e=this.#yr;let i=0,s=e.length-1;for(;i<=s;){const n=i+s>>1,r=e[n][0];if(r===t)return n;r<t?i=n+1:s=n-1}return s+1}#_r([,t,e]){const i=this.#Sr(t);this.#yr.splice(i,0,[t,e])}#xr([,t,e]){const i=this.#Sr(t);for(let s=i;s<this.#yr.length;s++){const[i,n]=this.#yr[s];if(i!==t)break;if(i===t&&n===e){this.#yr.splice(s,1);return}}for(let s=i-1;s>=0;s--){const[i,n]=this.#yr[s];if(i!==t)break;if(i===t&&n===e){this.#yr.splice(s,1);return}}}#Ar(t){const[e,i,s]=t,n=[[e,i,s]],r=this.#Sr(s);for(let t=0;t<r;t++){const[i,s]=this.#yr[t];for(let t=0,r=n.length;t<r;t++){const[,a,o]=n[t];if(!(s<=a||o<=i))if(a>=i)if(o>s)n[t][1]=s;else{if(1===r)return[];n.splice(t,1);t--;r--}else{n[t][2]=i;o>s&&n.push([e,s,o])}}}return n}}class HighlightOutline extends Outline{#Vn;#Cr;constructor(t,e,i){super();this.#Cr=t;this.#Vn=e;this.lastPoint=i}toSVGPath(){const t=[];for(const e of this.#Cr){let[i,s]=e;t.push(`M${i} ${s}`);for(let n=2;n<e.length;n+=2){const r=e[n],a=e[n+1];if(r===i){t.push(`V${a}`);s=a}else if(a===s){t.push(`H${r}`);i=r}}t.push("Z")}return t.join(" ")}serialize([t,e,i,s],n){const r=[],a=i-t,o=s-e;for(const e of this.#Cr){const i=new Array(e.length);for(let n=0;n<e.length;n+=2){i[n]=t+e[n]*a;i[n+1]=s-e[n+1]*o}r.push(i)}return r}get box(){return this.#Vn}get classNamesForOutlining(){return["highlightOutline"]}}class FreeHighlightOutliner extends FreeDrawOutliner{newFreeDrawOutline(t,e,i,s,n,r){return new FreeHighlightOutline(t,e,i,s,n,r)}}class FreeHighlightOutline extends FreeDrawOutline{newOutliner(t,e,i,s,n,r=0){return new FreeHighlightOutliner(t,e,i,s,n,r)}}class ColorPicker{#Tr=null;#Dr=null;#Mr;#Pr=null;#kr=!1;#Ir=!1;#r=null;#Rr;#Fr=null;#m=null;#Lr;static#Or=null;static get _keyboardManager(){return shadow(this,"_keyboardManager",new KeyboardManager([[["Escape","mac+Escape"],ColorPicker.prototype._hideDropdownFromKeyboard],[[" ","mac+ "],ColorPicker.prototype._colorSelectFromKeyboard],[["ArrowDown","ArrowRight","mac+ArrowDown","mac+ArrowRight"],ColorPicker.prototype._moveToNext],[["ArrowUp","ArrowLeft","mac+ArrowUp","mac+ArrowLeft"],ColorPicker.prototype._moveToPrevious],[["Home","mac+Home"],ColorPicker.prototype._moveToBeginning],[["End","mac+End"],ColorPicker.prototype._moveToEnd]]))}constructor({editor:t=null,uiManager:e=null}){if(t){this.#Ir=!1;this.#Lr=f.HIGHLIGHT_COLOR;this.#r=t}else{this.#Ir=!0;this.#Lr=f.HIGHLIGHT_DEFAULT_COLOR}this.#m=t?._uiManager||e;this.#Rr=this.#m._eventBus;this.#Mr=t?.color||this.#m?.highlightColors.values().next().value||"#FFFF98";ColorPicker.#Or||=Object.freeze({blue:"pdfjs-editor-colorpicker-blue",green:"pdfjs-editor-colorpicker-green",pink:"pdfjs-editor-colorpicker-pink",red:"pdfjs-editor-colorpicker-red",yellow:"pdfjs-editor-colorpicker-yellow"})}renderButton(){const t=this.#Tr=document.createElement("button");t.className="colorPicker";t.tabIndex="0";t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-button");t.setAttribute("aria-haspopup",!0);const e=this.#m._signal;t.addEventListener("click",this.#Nr.bind(this),{signal:e});t.addEventListener("keydown",this.#Ks.bind(this),{signal:e});const i=this.#Dr=document.createElement("span");i.className="swatch";i.setAttribute("aria-hidden",!0);i.style.backgroundColor=this.#Mr;t.append(i);return t}renderMainDropdown(){const t=this.#Pr=this.#Br();t.setAttribute("aria-orientation","horizontal");t.setAttribute("aria-labelledby","highlightColorPickerLabel");return t}#Br(){const t=document.createElement("div"),e=this.#m._signal;t.addEventListener("contextmenu",noContextMenu,{signal:e});t.className="dropdown";t.role="listbox";t.setAttribute("aria-multiselectable",!1);t.setAttribute("aria-orientation","vertical");t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-dropdown");for(const[i,s]of this.#m.highlightColors){const n=document.createElement("button");n.tabIndex="0";n.role="option";n.setAttribute("data-color",s);n.title=i;n.setAttribute("data-l10n-id",ColorPicker.#Or[i]);const r=document.createElement("span");n.append(r);r.className="swatch";r.style.backgroundColor=s;n.setAttribute("aria-selected",s===this.#Mr);n.addEventListener("click",this.#Ur.bind(this,s),{signal:e});t.append(n)}t.addEventListener("keydown",this.#Ks.bind(this),{signal:e});return t}#Ur(t,e){e.stopPropagation();this.#Rr.dispatch("switchannotationeditorparams",{source:this,type:this.#Lr,value:t})}_colorSelectFromKeyboard(t){if(t.target===this.#Tr){this.#Nr(t);return}const e=t.target.getAttribute("data-color");e&&this.#Ur(e,t)}_moveToNext(t){this.#Hr?t.target!==this.#Tr?t.target.nextSibling?.focus():this.#Pr.firstChild?.focus():this.#Nr(t)}_moveToPrevious(t){if(t.target!==this.#Pr?.firstChild&&t.target!==this.#Tr){this.#Hr||this.#Nr(t);t.target.previousSibling?.focus()}else this.#Hr&&this._hideDropdownFromKeyboard()}_moveToBeginning(t){this.#Hr?this.#Pr.firstChild?.focus():this.#Nr(t)}_moveToEnd(t){this.#Hr?this.#Pr.lastChild?.focus():this.#Nr(t)}#Ks(t){ColorPicker._keyboardManager.exec(this,t)}#Nr(t){if(this.#Hr){this.hideDropdown();return}this.#kr=0===t.detail;if(!this.#Fr){this.#Fr=new AbortController;window.addEventListener("pointerdown",this.#c.bind(this),{signal:this.#m.combinedSignal(this.#Fr)})}if(this.#Pr){this.#Pr.classList.remove("hidden");return}const e=this.#Pr=this.#Br();this.#Tr.append(e)}#c(t){this.#Pr?.contains(t.target)||this.hideDropdown()}hideDropdown(){this.#Pr?.classList.add("hidden");this.#Fr?.abort();this.#Fr=null}get#Hr(){return this.#Pr&&!this.#Pr.classList.contains("hidden")}_hideDropdownFromKeyboard(){if(!this.#Ir)if(this.#Hr){this.hideDropdown();this.#Tr.focus({preventScroll:!0,focusVisible:this.#kr})}else this.#r?.unselect()}updateColor(t){this.#Dr&&(this.#Dr.style.backgroundColor=t);if(!this.#Pr)return;const e=this.#m.highlightColors.values();for(const i of this.#Pr.children)i.setAttribute("aria-selected",e.next().value===t)}destroy(){this.#Tr?.remove();this.#Tr=null;this.#Dr=null;this.#Pr?.remove();this.#Pr=null}}class HighlightEditor extends AnnotationEditor{#zr=null;#jr=0;#Gr;#Wr=null;#n=null;#$r=null;#Vr=null;#qr=0;#Xr=null;#Kr=null;#y=null;#Yr=!1;#vr=null;#Qr;#Jr=null;#Zr="";#sr;#ta="";static _defaultColor=null;static _defaultOpacity=1;static _defaultThickness=12;static _type="highlight";static _editorType=g.HIGHLIGHT;static _freeHighlightId=-1;static _freeHighlight=null;static _freeHighlightClipId="";static get _keyboardManager(){const t=HighlightEditor.prototype;return shadow(this,"_keyboardManager",new KeyboardManager([[["ArrowLeft","mac+ArrowLeft"],t._moveCaret,{args:[0]}],[["ArrowRight","mac+ArrowRight"],t._moveCaret,{args:[1]}],[["ArrowUp","mac+ArrowUp"],t._moveCaret,{args:[2]}],[["ArrowDown","mac+ArrowDown"],t._moveCaret,{args:[3]}]]))}constructor(t){super({...t,name:"highlightEditor"});this.color=t.color||HighlightEditor._defaultColor;this.#sr=t.thickness||HighlightEditor._defaultThickness;this.#Qr=t.opacity||HighlightEditor._defaultOpacity;this.#Gr=t.boxes||null;this.#ta=t.methodOfCreation||"";this.#Zr=t.text||"";this._isDraggable=!1;this.defaultL10nId="pdfjs-editor-highlight-editor";if(t.highlightId>-1){this.#Yr=!0;this.#ea(t);this.#ia()}else if(this.#Gr){this.#zr=t.anchorNode;this.#jr=t.anchorOffset;this.#Vr=t.focusNode;this.#qr=t.focusOffset;this.#sa();this.#ia();this.rotate(this.rotation)}}get telemetryInitialData(){return{action:"added",type:this.#Yr?"free_highlight":"highlight",color:this._uiManager.highlightColorNames.get(this.color),thickness:this.#sr,methodOfCreation:this.#ta}}get telemetryFinalData(){return{type:"highlight",color:this._uiManager.highlightColorNames.get(this.color)}}static computeTelemetryFinalData(t){return{numberOfColors:t.get("color").size}}#sa(){const t=new HighlightOutliner(this.#Gr,.001);this.#Kr=t.getOutlines();[this.x,this.y,this.width,this.height]=this.#Kr.box;const e=new HighlightOutliner(this.#Gr,.0025,.001,"ltr"===this._uiManager.direction);this.#$r=e.getOutlines();const{lastPoint:i}=this.#$r;this.#vr=[(i[0]-this.x)/this.width,(i[1]-this.y)/this.height]}#ea({highlightOutlines:t,highlightId:e,clipPathId:i}){this.#Kr=t;this.#$r=t.getNewOutline(this.#sr/2****,.0025);if(e>=0){this.#y=e;this.#Wr=i;this.parent.drawLayer.finalizeDraw(e,{bbox:t.box,path:{d:t.toSVGPath()}});this.#Jr=this.parent.drawLayer.drawOutline({rootClass:{highlightOutline:!0,free:!0},bbox:this.#$r.box,path:{d:this.#$r.toSVGPath()}},!0)}else if(this.parent){const e=this.parent.viewport.rotation;this.parent.drawLayer.updateProperties(this.#y,{bbox:HighlightEditor.#na(this.#Kr.box,(e-this.rotation+360)%360),path:{d:t.toSVGPath()}});this.parent.drawLayer.updateProperties(this.#Jr,{bbox:HighlightEditor.#na(this.#$r.box,e),path:{d:this.#$r.toSVGPath()}})}const[s,n,r,a]=t.box;switch(this.rotation){case 0:this.x=s;this.y=n;this.width=r;this.height=a;break;case 90:{const[t,e]=this.parentDimensions;this.x=n;this.y=1-s;this.width=r*e/t;this.height=a*t/e;break}case 180:this.x=1-s;this.y=1-n;this.width=r;this.height=a;break;case 270:{const[t,e]=this.parentDimensions;this.x=1-n;this.y=s;this.width=r*e/t;this.height=a*t/e;break}}const{lastPoint:o}=this.#$r;this.#vr=[(o[0]-s)/r,(o[1]-n)/a]}static initialize(t,e){AnnotationEditor.initialize(t,e);HighlightEditor._defaultColor||=e.highlightColors?.values().next().value||"#fff066"}static updateDefaultParams(t,e){switch(t){case f.HIGHLIGHT_DEFAULT_COLOR:HighlightEditor._defaultColor=e;break;case f.HIGHLIGHT_THICKNESS:HighlightEditor._defaultThickness=e}}translateInPage(t,e){}get toolbarPosition(){return this.#vr}updateParams(t,e){switch(t){case f.HIGHLIGHT_COLOR:this.#Bn(e);break;case f.HIGHLIGHT_THICKNESS:this.#ra(e)}}static get defaultPropertiesToUpdate(){return[[f.HIGHLIGHT_DEFAULT_COLOR,HighlightEditor._defaultColor],[f.HIGHLIGHT_THICKNESS,HighlightEditor._defaultThickness]]}get propertiesToUpdate(){return[[f.HIGHLIGHT_COLOR,this.color||HighlightEditor._defaultColor],[f.HIGHLIGHT_THICKNESS,this.#sr||HighlightEditor._defaultThickness],[f.HIGHLIGHT_FREE,this.#Yr]]}#Bn(t){const setColorAndOpacity=(t,e)=>{this.color=t;this.#Qr=e;this.parent?.drawLayer.updateProperties(this.#y,{root:{fill:t,"fill-opacity":e}});this.#n?.updateColor(t)},e=this.color,i=this.#Qr;this.addCommands({cmd:setColorAndOpacity.bind(this,t,HighlightEditor._defaultOpacity),undo:setColorAndOpacity.bind(this,e,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:f.HIGHLIGHT_COLOR,overwriteIfSameType:!0,keepUndo:!0});this._reportTelemetry({action:"color_changed",color:this._uiManager.highlightColorNames.get(t)},!0)}#ra(t){const e=this.#sr,setThickness=t=>{this.#sr=t;this.#aa(t)};this.addCommands({cmd:setThickness.bind(this,t),undo:setThickness.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:f.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0});this._reportTelemetry({action:"thickness_changed",thickness:t},!0)}async addEditToolbar(){const t=await super.addEditToolbar();if(!t)return null;if(this._uiManager.highlightColors){this.#n=new ColorPicker({editor:this});t.addColorPicker(this.#n)}return t}disableEditing(){super.disableEditing();this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing();this.div.classList.toggle("disabled",!1)}fixAndSetPosition(){return super.fixAndSetPosition(this.#oa())}getBaseTranslation(){return[0,0]}getRect(t,e){return super.getRect(t,e,this.#oa())}onceAdded(t){this.annotationElementId||this.parent.addUndoableEditor(this);t&&this.div.focus()}remove(){this.#la();this._reportTelemetry({action:"deleted"});super.remove()}rebuild(){if(this.parent){super.rebuild();if(null!==this.div){this.#ia();this.isAttachedToDOM||this.parent.add(this)}}}setParent(t){let e=!1;if(this.parent&&!t)this.#la();else if(t){this.#ia(t);e=!this.parent&&this.div?.classList.contains("selectedEditor")}super.setParent(t);this.show(this._isVisible);e&&this.select()}#aa(t){if(!this.#Yr)return;this.#ea({highlightOutlines:this.#Kr.getNewOutline(t/2)});this.fixAndSetPosition();const[e,i]=this.parentDimensions;this.setDims(this.width*e,this.height*i)}#la(){if(null!==this.#y&&this.parent){this.parent.drawLayer.remove(this.#y);this.#y=null;this.parent.drawLayer.remove(this.#Jr);this.#Jr=null}}#ia(t=this.parent){if(null===this.#y){({id:this.#y,clipPathId:this.#Wr}=t.drawLayer.draw({bbox:this.#Kr.box,root:{viewBox:"0 0 1 1",fill:this.color,"fill-opacity":this.#Qr},rootClass:{highlight:!0,free:this.#Yr},path:{d:this.#Kr.toSVGPath()}},!1,!0));this.#Jr=t.drawLayer.drawOutline({rootClass:{highlightOutline:!0,free:this.#Yr},bbox:this.#$r.box,path:{d:this.#$r.toSVGPath()}},this.#Yr);this.#Xr&&(this.#Xr.style.clipPath=this.#Wr)}}static#na([t,e,i,s],n){switch(n){case 90:return[1-e-s,t,s,i];case 180:return[1-t-i,1-e-s,i,s];case 270:return[e,1-t-i,s,i]}return[t,e,i,s]}rotate(t){const{drawLayer:e}=this.parent;let i;if(this.#Yr){t=(t-this.rotation+360)%360;i=HighlightEditor.#na(this.#Kr.box,t)}else i=HighlightEditor.#na([this.x,this.y,this.width,this.height],t);e.updateProperties(this.#y,{bbox:i,root:{"data-main-rotation":t}});e.updateProperties(this.#Jr,{bbox:HighlightEditor.#na(this.#$r.box,t),root:{"data-main-rotation":t}})}render(){if(this.div)return this.div;const t=super.render();if(this.#Zr){t.setAttribute("aria-label",this.#Zr);t.setAttribute("role","mark")}this.#Yr?t.classList.add("free"):this.div.addEventListener("keydown",this.#ha.bind(this),{signal:this._uiManager._signal});const e=this.#Xr=document.createElement("div");t.append(e);e.setAttribute("aria-hidden","true");e.className="internal";e.style.clipPath=this.#Wr;const[i,s]=this.parentDimensions;this.setDims(this.width*i,this.height*s);bindEvents(this,this.#Xr,["pointerover","pointerleave"]);this.enableEditing();return t}pointerover(){this.isSelected||this.parent?.drawLayer.updateProperties(this.#Jr,{rootClass:{hovered:!0}})}pointerleave(){this.isSelected||this.parent?.drawLayer.updateProperties(this.#Jr,{rootClass:{hovered:!1}})}#ha(t){HighlightEditor._keyboardManager.exec(this,t)}_moveCaret(t){this.parent.unselect(this);switch(t){case 0:case 2:this.#ca(!0);break;case 1:case 3:this.#ca(!1)}}#ca(t){if(!this.#zr)return;const e=window.getSelection();t?e.setPosition(this.#zr,this.#jr):e.setPosition(this.#Vr,this.#qr)}select(){super.select();this.#Jr&&this.parent?.drawLayer.updateProperties(this.#Jr,{rootClass:{hovered:!1,selected:!0}})}unselect(){super.unselect();if(this.#Jr){this.parent?.drawLayer.updateProperties(this.#Jr,{rootClass:{selected:!1}});this.#Yr||this.#ca(!1)}}get _mustFixPosition(){return!this.#Yr}show(t=this._isVisible){super.show(t);if(this.parent){this.parent.drawLayer.updateProperties(this.#y,{rootClass:{hidden:!t}});this.parent.drawLayer.updateProperties(this.#Jr,{rootClass:{hidden:!t}})}}#oa(){return this.#Yr?this.rotation:0}#da(){if(this.#Yr)return null;const[t,e]=this.pageDimensions,[i,s]=this.pageTranslation,n=this.#Gr,r=new Float32Array(8*n.length);let a=0;for(const{x:o,y:l,width:h,height:c}of n){const n=o*t+i,d=(1-l)*e+s;r[a]=r[a+4]=n;r[a+1]=r[a+3]=d;r[a+2]=r[a+6]=n+h*t;r[a+5]=r[a+7]=d-c*e;a+=8}return r}#ua(t){return this.#Kr.serialize(t,this.#oa())}static startHighlighting(t,e,{target:i,x:s,y:n}){const{x:r,y:a,width:o,height:l}=i.getBoundingClientRect(),h=new AbortController,c=t.combinedSignal(h),pointerUpCallback=e=>{h.abort();this.#pa(t,e)};window.addEventListener("blur",pointerUpCallback,{signal:c});window.addEventListener("pointerup",pointerUpCallback,{signal:c});window.addEventListener("pointerdown",stopEvent,{capture:!0,passive:!1,signal:c});window.addEventListener("contextmenu",noContextMenu,{signal:c});i.addEventListener("pointermove",this.#ga.bind(this,t),{signal:c});this._freeHighlight=new FreeHighlightOutliner({x:s,y:n},[r,a,o,l],t.scale,this._defaultThickness/2,e,.001);({id:this._freeHighlightId,clipPathId:this._freeHighlightClipId}=t.drawLayer.draw({bbox:[0,0,1,1],root:{viewBox:"0 0 1 1",fill:this._defaultColor,"fill-opacity":this._defaultOpacity},rootClass:{highlight:!0,free:!0},path:{d:this._freeHighlight.toSVGPath()}},!0,!0))}static#ga(t,e){this._freeHighlight.add(e)&&t.drawLayer.updateProperties(this._freeHighlightId,{path:{d:this._freeHighlight.toSVGPath()}})}static#pa(t,e){this._freeHighlight.isEmpty()?t.drawLayer.remove(this._freeHighlightId):t.createAndAddNewEditor(e,!1,{highlightId:this._freeHighlightId,highlightOutlines:this._freeHighlight.getOutlines(),clipPathId:this._freeHighlightClipId,methodOfCreation:"main_toolbar"});this._freeHighlightId=-1;this._freeHighlight=null;this._freeHighlightClipId=""}static async deserialize(t,e,i){let s=null;if(t instanceof HighlightAnnotationElement){const{data:{quadPoints:e,rect:i,rotation:n,id:r,color:a,opacity:o,popupRef:l},parent:{page:{pageNumber:h}}}=t;s=t={annotationType:g.HIGHLIGHT,color:Array.from(a),opacity:o,quadPoints:e,boxes:null,pageIndex:h-1,rect:i.slice(0),rotation:n,id:r,deleted:!1,popupRef:l}}else if(t instanceof InkAnnotationElement){const{data:{inkLists:e,rect:i,rotation:n,id:r,color:a,borderStyle:{rawWidth:o},popupRef:l},parent:{page:{pageNumber:h}}}=t;s=t={annotationType:g.HIGHLIGHT,color:Array.from(a),thickness:o,inkLists:e,boxes:null,pageIndex:h-1,rect:i.slice(0),rotation:n,id:r,deleted:!1,popupRef:l}}const{color:n,quadPoints:r,inkLists:a,opacity:o}=t,l=await super.deserialize(t,e,i);l.color=Util.makeHexColor(...n);l.#Qr=o||1;a&&(l.#sr=t.thickness);l.annotationElementId=t.id||null;l._initialData=s;const[h,c]=l.pageDimensions,[d,u]=l.pageTranslation;if(r){const t=l.#Gr=[];for(let e=0;e<r.length;e+=8)t.push({x:(r[e]-d)/h,y:1-(r[e+1]-u)/c,width:(r[e+2]-r[e])/h,height:(r[e+1]-r[e+5])/c});l.#sa();l.#ia();l.rotate(l.rotation)}else if(a){l.#Yr=!0;const t=a[0],i={x:t[0]-d,y:c-(t[1]-u)},s=new FreeHighlightOutliner(i,[0,0,h,c],1,l.#sr/2,!0,.001);for(let e=0,n=t.length;e<n;e+=2){i.x=t[e]-d;i.y=c-(t[e+1]-u);s.add(i)}const{id:n,clipPathId:r}=e.drawLayer.draw({bbox:[0,0,1,1],root:{viewBox:"0 0 1 1",fill:l.color,"fill-opacity":l._defaultOpacity},rootClass:{highlight:!0,free:!0},path:{d:s.toSVGPath()}},!0,!0);l.#ea({highlightOutlines:s.getOutlines(),highlightId:n,clipPathId:r});l.#ia();l.rotate(l.parentRotation)}return l}serialize(t=!1){if(this.isEmpty()||t)return null;if(this.deleted)return this.serializeDeleted();const e=this.getRect(0,0),i=AnnotationEditor._colorManager.convert(this.color),s={annotationType:g.HIGHLIGHT,color:i,opacity:this.#Qr,thickness:this.#sr,quadPoints:this.#da(),outlines:this.#ua(e),pageIndex:this.pageIndex,rect:e,rotation:this.#oa(),structTreeParentId:this._structTreeParentId};if(this.annotationElementId&&!this.#$n(s))return null;s.id=this.annotationElementId;return s}#$n(t){const{color:e}=this._initialData;return t.color.some(((t,i)=>t!==e[i]))}renderAnnotationElement(t){t.updateEdited({rect:this.getRect(0,0)});return null}static canCreateNewEmptyEditor(){return!1}}class DrawingOptions{#fa=Object.create(null);updateProperty(t,e){this[t]=e;this.updateSVGProperty(t,e)}updateProperties(t){if(t)for(const[e,i]of Object.entries(t))e.startsWith("_")||this.updateProperty(e,i)}updateSVGProperty(t,e){this.#fa[t]=e}toSVGProperties(){const t=this.#fa;this.#fa=Object.create(null);return{root:t}}reset(){this.#fa=Object.create(null)}updateAll(t=this){this.updateProperties(t)}clone(){unreachable("Not implemented")}}class DrawingEditor extends AnnotationEditor{#ma=null;#ba;_drawId=null;static _currentDrawId=-1;static _currentParent=null;static#va=null;static#wa=null;static#ya=null;static#Aa=NaN;static#_a=null;static#xa=null;static#Ea=NaN;static _INNER_MARGIN=3;constructor(t){super(t);this.#ba=t.mustBeCommitted||!1;this._addOutlines(t)}_addOutlines(t){if(t.drawOutlines){this.#Sa(t);this.#ia()}}#Sa({drawOutlines:t,drawId:e,drawingOptions:i}){this.#ma=t;this._drawingOptions||=i;if(e>=0){this._drawId=e;this.parent.drawLayer.finalizeDraw(e,t.defaultProperties)}else this._drawId=this.#Ca(t,this.parent);this.#Ta(t.box)}#Ca(t,e){const{id:i}=e.drawLayer.draw(DrawingEditor._mergeSVGProperties(this._drawingOptions.toSVGProperties(),t.defaultSVGProperties),!1,!1);return i}static _mergeSVGProperties(t,e){const i=new Set(Object.keys(t));for(const[s,n]of Object.entries(e))i.has(s)?Object.assign(t[s],n):t[s]=n;return t}static getDefaultDrawingOptions(t){unreachable("Not implemented")}static get typesMap(){unreachable("Not implemented")}static get isDrawer(){return!0}static get supportMultipleDrawings(){return!1}static updateDefaultParams(t,e){const i=this.typesMap.get(t);i&&this._defaultDrawingOptions.updateProperty(i,e);if(this._currentParent){DrawingEditor.#va.updateProperty(i,e);this._currentParent.drawLayer.updateProperties(this._currentDrawId,this._defaultDrawingOptions.toSVGProperties())}}updateParams(t,e){const i=this.constructor.typesMap.get(t);i&&this._updateProperty(t,i,e)}static get defaultPropertiesToUpdate(){const t=[],e=this._defaultDrawingOptions;for(const[i,s]of this.typesMap)t.push([i,e[s]]);return t}get propertiesToUpdate(){const t=[],{_drawingOptions:e}=this;for(const[i,s]of this.constructor.typesMap)t.push([i,e[s]]);return t}_updateProperty(t,e,i){const s=this._drawingOptions,n=s[e],setter=t=>{s.updateProperty(e,t);const i=this.#ma.updateProperty(e,t);i&&this.#Ta(i);this.parent?.drawLayer.updateProperties(this._drawId,s.toSVGProperties())};this.addCommands({cmd:setter.bind(this,i),undo:setter.bind(this,n),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:t,overwriteIfSameType:!0,keepUndo:!0})}_onResizing(){this.parent?.drawLayer.updateProperties(this._drawId,DrawingEditor._mergeSVGProperties(this.#ma.getPathResizingSVGProperties(this.#Da()),{bbox:this.#Ma()}))}_onResized(){this.parent?.drawLayer.updateProperties(this._drawId,DrawingEditor._mergeSVGProperties(this.#ma.getPathResizedSVGProperties(this.#Da()),{bbox:this.#Ma()}))}_onTranslating(t,e){this.parent?.drawLayer.updateProperties(this._drawId,{bbox:this.#Ma()})}_onTranslated(){this.parent?.drawLayer.updateProperties(this._drawId,DrawingEditor._mergeSVGProperties(this.#ma.getPathTranslatedSVGProperties(this.#Da(),this.parentDimensions),{bbox:this.#Ma()}))}_onStartDragging(){this.parent?.drawLayer.updateProperties(this._drawId,{rootClass:{moving:!0}})}_onStopDragging(){this.parent?.drawLayer.updateProperties(this._drawId,{rootClass:{moving:!1}})}commit(){super.commit();this.disableEditMode();this.disableEditing()}disableEditing(){super.disableEditing();this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing();this.div.classList.toggle("disabled",!1)}getBaseTranslation(){return[0,0]}get isResizable(){return!0}onceAdded(t){this.annotationElementId||this.parent.addUndoableEditor(this);this._isDraggable=!0;if(this.#ba){this.#ba=!1;this.commit();this.parent.setSelected(this);t&&this.isOnScreen&&this.div.focus()}}remove(){this.#la();super.remove()}rebuild(){if(this.parent){super.rebuild();if(null!==this.div){this.#ia();this.#Ta(this.#ma.box);this.isAttachedToDOM||this.parent.add(this)}}}setParent(t){let e=!1;if(this.parent&&!t){this._uiManager.removeShouldRescale(this);this.#la()}else if(t){this._uiManager.addShouldRescale(this);this.#ia(t);e=!this.parent&&this.div?.classList.contains("selectedEditor")}super.setParent(t);e&&this.select()}#la(){if(null!==this._drawId&&this.parent){this.parent.drawLayer.remove(this._drawId);this._drawId=null;this._drawingOptions.reset()}}#ia(t=this.parent){if(null===this._drawId||this.parent!==t)if(null===this._drawId){this._drawingOptions.updateAll();this._drawId=this.#Ca(this.#ma,t)}else this.parent.drawLayer.updateParent(this._drawId,t.drawLayer)}#Pa([t,e,i,s]){const{parentDimensions:[n,r],rotation:a}=this;switch(a){case 90:return[e,1-t,i*(r/n),s*(n/r)];case 180:return[1-t,1-e,i,s];case 270:return[1-e,t,i*(r/n),s*(n/r)];default:return[t,e,i,s]}}#Da(){const{x:t,y:e,width:i,height:s,parentDimensions:[n,r],rotation:a}=this;switch(a){case 90:return[1-e,t,i*(n/r),s*(r/n)];case 180:return[1-t,1-e,i,s];case 270:return[e,1-t,i*(n/r),s*(r/n)];default:return[t,e,i,s]}}#Ta(t){[this.x,this.y,this.width,this.height]=this.#Pa(t);if(this.div){this.fixAndSetPosition();const[t,e]=this.parentDimensions;this.setDims(this.width*t,this.height*e)}this._onResized()}#Ma(){const{x:t,y:e,width:i,height:s,rotation:n,parentRotation:r,parentDimensions:[a,o]}=this;switch((4*n+r)/90){case 1:return[1-e-s,t,s,i];case 2:return[1-t-i,1-e-s,i,s];case 3:return[e,1-t-i,s,i];case 4:return[t,e-i*(a/o),s*(o/a),i*(a/o)];case 5:return[1-e,t,i*(a/o),s*(o/a)];case 6:return[1-t-s*(o/a),1-e,s*(o/a),i*(a/o)];case 7:return[e-i*(a/o),1-t-s*(o/a),i*(a/o),s*(o/a)];case 8:return[t-i,e-s,i,s];case 9:return[1-e,t-i,s,i];case 10:return[1-t,1-e,i,s];case 11:return[e-s,1-t,s,i];case 12:return[t-s*(o/a),e,s*(o/a),i*(a/o)];case 13:return[1-e-i*(a/o),t-s*(o/a),i*(a/o),s*(o/a)];case 14:return[1-t,1-e-i*(a/o),s*(o/a),i*(a/o)];case 15:return[e,1-t,i*(a/o),s*(o/a)];default:return[t,e,i,s]}}rotate(){this.parent&&this.parent.drawLayer.updateProperties(this._drawId,DrawingEditor._mergeSVGProperties({bbox:this.#Ma()},this.#ma.updateRotation((this.parentRotation-this.rotation+360)%360)))}onScaleChanging(){this.parent&&this.#Ta(this.#ma.updateParentDimensions(this.parentDimensions,this.parent.scale))}static onScaleChangingWhenDrawing(){}render(){if(this.div)return this.div;let t,e;if(this._isCopy){t=this.x;e=this.y}const i=super.render();i.classList.add("draw");const s=document.createElement("div");i.append(s);s.setAttribute("aria-hidden","true");s.className="internal";const[n,r]=this.parentDimensions;this.setDims(this.width*n,this.height*r);this._uiManager.addShouldRescale(this);this.disableEditing();this._isCopy&&this._moveAfterPaste(t,e);return i}static createDrawerInstance(t,e,i,s,n){unreachable("Not implemented")}static startDrawing(t,e,i,s){const{target:n,offsetX:r,offsetY:a,pointerId:o,pointerType:l}=s;if(DrawingEditor.#_a&&DrawingEditor.#_a!==l)return;const{viewport:{rotation:h}}=t,{width:c,height:d}=n.getBoundingClientRect(),u=DrawingEditor.#wa=new AbortController,p=t.combinedSignal(u);DrawingEditor.#Aa||=o;DrawingEditor.#_a??=l;window.addEventListener("pointerup",(t=>{DrawingEditor.#Aa===t.pointerId?this._endDraw(t):DrawingEditor.#xa?.delete(t.pointerId)}),{signal:p});window.addEventListener("pointercancel",(t=>{DrawingEditor.#Aa===t.pointerId?this._currentParent.endDrawingSession():DrawingEditor.#xa?.delete(t.pointerId)}),{signal:p});window.addEventListener("pointerdown",(t=>{if(DrawingEditor.#_a===t.pointerType){(DrawingEditor.#xa||=new Set).add(t.pointerId);if(DrawingEditor.#va.isCancellable()){DrawingEditor.#va.removeLastElement();DrawingEditor.#va.isEmpty()?this._currentParent.endDrawingSession(!0):this._endDraw(null)}}}),{capture:!0,passive:!1,signal:p});window.addEventListener("contextmenu",noContextMenu,{signal:p});n.addEventListener("pointermove",this._drawMove.bind(this),{signal:p});n.addEventListener("touchmove",(t=>{t.timeStamp===DrawingEditor.#Ea&&stopEvent(t)}),{signal:p});t.toggleDrawing();e._editorUndoBar?.hide();if(DrawingEditor.#va)t.drawLayer.updateProperties(this._currentDrawId,DrawingEditor.#va.startNew(r,a,c,d,h));else{e.updateUIForDefaultProperties(this);DrawingEditor.#va=this.createDrawerInstance(r,a,c,d,h);DrawingEditor.#ya=this.getDefaultDrawingOptions();this._currentParent=t;({id:this._currentDrawId}=t.drawLayer.draw(this._mergeSVGProperties(DrawingEditor.#ya.toSVGProperties(),DrawingEditor.#va.defaultSVGProperties),!0,!1))}}static _drawMove(t){DrawingEditor.#Ea=-1;if(!DrawingEditor.#va)return;const{offsetX:e,offsetY:i,pointerId:s}=t;if(DrawingEditor.#Aa===s)if(DrawingEditor.#xa?.size>=1)this._endDraw(t);else{this._currentParent.drawLayer.updateProperties(this._currentDrawId,DrawingEditor.#va.add(e,i));DrawingEditor.#Ea=t.timeStamp;stopEvent(t)}}static _cleanup(t){if(t){this._currentDrawId=-1;this._currentParent=null;DrawingEditor.#va=null;DrawingEditor.#ya=null;DrawingEditor.#_a=null;DrawingEditor.#Ea=NaN}if(DrawingEditor.#wa){DrawingEditor.#wa.abort();DrawingEditor.#wa=null;DrawingEditor.#Aa=NaN;DrawingEditor.#xa=null}}static _endDraw(t){const e=this._currentParent;if(e){e.toggleDrawing(!0);this._cleanup(!1);t?.target===e.div&&e.drawLayer.updateProperties(this._currentDrawId,DrawingEditor.#va.end(t.offsetX,t.offsetY));if(this.supportMultipleDrawings){const t=DrawingEditor.#va,i=this._currentDrawId,s=t.getLastElement();e.addCommands({cmd:()=>{e.drawLayer.updateProperties(i,t.setLastElement(s))},undo:()=>{e.drawLayer.updateProperties(i,t.removeLastElement())},mustExec:!1,type:f.DRAW_STEP})}else this.endDrawing(!1)}}static endDrawing(t){const e=this._currentParent;if(!e)return null;e.toggleDrawing(!0);e.cleanUndoStack(f.DRAW_STEP);if(!DrawingEditor.#va.isEmpty()){const{pageDimensions:[i,s],scale:n}=e,r=e.createAndAddNewEditor({offsetX:0,offsetY:0},!1,{drawId:this._currentDrawId,drawOutlines:DrawingEditor.#va.getOutlines(i*n,s*n,n,this._INNER_MARGIN),drawingOptions:DrawingEditor.#ya,mustBeCommitted:!t});this._cleanup(!0);return r}e.drawLayer.remove(this._currentDrawId);this._cleanup(!0);return null}createDrawingOptions(t){}static deserializeDraw(t,e,i,s,n,r){unreachable("Not implemented")}static async deserialize(t,e,i){const{rawDims:{pageWidth:s,pageHeight:n,pageX:r,pageY:a}}=e.viewport,o=this.deserializeDraw(r,a,s,n,this._INNER_MARGIN,t),l=await super.deserialize(t,e,i);l.createDrawingOptions(t);l.#Sa({drawOutlines:o});l.#ia();l.onScaleChanging();l.rotate();return l}serializeDraw(t){const[e,i]=this.pageTranslation,[s,n]=this.pageDimensions;return this.#ma.serialize([e,i,s,n],t)}renderAnnotationElement(t){t.updateEdited({rect:this.getRect(0,0)});return null}static canCreateNewEmptyEditor(){return!1}}class InkDrawOutliner{#Qn=new Float64Array(6);#wn;#ka;#es;#sr;#nr;#Ia="";#Ra=0;#Cr=new InkDrawOutline;#Fa;#La;constructor(t,e,i,s,n,r){this.#Fa=i;this.#La=s;this.#es=n;this.#sr=r;[t,e]=this.#Oa(t,e);const a=this.#wn=[NaN,NaN,NaN,NaN,t,e];this.#nr=[t,e];this.#ka=[{line:a,points:this.#nr}];this.#Qn.set(a,0)}updateProperty(t,e){"stroke-width"===t&&(this.#sr=e)}#Oa(t,e){return Outline._normalizePoint(t,e,this.#Fa,this.#La,this.#es)}isEmpty(){return!this.#ka||0===this.#ka.length}isCancellable(){return this.#nr.length<=10}add(t,e){[t,e]=this.#Oa(t,e);const[i,s,n,r]=this.#Qn.subarray(2,6),a=t-n,o=e-r;if(Math.hypot(this.#Fa*a,this.#La*o)<=2)return null;this.#nr.push(t,e);if(isNaN(i)){this.#Qn.set([n,r,t,e],2);this.#wn.push(NaN,NaN,NaN,NaN,t,e);return{path:{d:this.toSVGPath()}}}isNaN(this.#Qn[0])&&this.#wn.splice(6,6);this.#Qn.set([i,s,n,r,t,e],0);this.#wn.push(...Outline.createBezierPoints(i,s,n,r,t,e));return{path:{d:this.toSVGPath()}}}end(t,e){const i=this.add(t,e);return i||(2===this.#nr.length?{path:{d:this.toSVGPath()}}:null)}startNew(t,e,i,s,n){this.#Fa=i;this.#La=s;this.#es=n;[t,e]=this.#Oa(t,e);const r=this.#wn=[NaN,NaN,NaN,NaN,t,e];this.#nr=[t,e];const a=this.#ka.at(-1);if(a){a.line=new Float32Array(a.line);a.points=new Float32Array(a.points)}this.#ka.push({line:r,points:this.#nr});this.#Qn.set(r,0);this.#Ra=0;this.toSVGPath();return null}getLastElement(){return this.#ka.at(-1)}setLastElement(t){if(!this.#ka)return this.#Cr.setLastElement(t);this.#ka.push(t);this.#wn=t.line;this.#nr=t.points;this.#Ra=0;return{path:{d:this.toSVGPath()}}}removeLastElement(){if(!this.#ka)return this.#Cr.removeLastElement();this.#ka.pop();this.#Ia="";for(let t=0,e=this.#ka.length;t<e;t++){const{line:e,points:i}=this.#ka[t];this.#wn=e;this.#nr=i;this.#Ra=0;this.toSVGPath()}return{path:{d:this.#Ia}}}toSVGPath(){const t=Outline.svgRound(this.#wn[4]),e=Outline.svgRound(this.#wn[5]);if(2===this.#nr.length){this.#Ia=`${this.#Ia} M ${t} ${e} Z`;return this.#Ia}if(this.#nr.length<=6){const i=this.#Ia.lastIndexOf("M");this.#Ia=`${this.#Ia.slice(0,i)} M ${t} ${e}`;this.#Ra=6}if(4===this.#nr.length){const t=Outline.svgRound(this.#wn[10]),e=Outline.svgRound(this.#wn[11]);this.#Ia=`${this.#Ia} L ${t} ${e}`;this.#Ra=12;return this.#Ia}const i=[];if(0===this.#Ra){i.push(`M ${t} ${e}`);this.#Ra=6}for(let t=this.#Ra,e=this.#wn.length;t<e;t+=6){const[e,s,n,r,a,o]=this.#wn.slice(t,t+6).map(Outline.svgRound);i.push(`C${e} ${s} ${n} ${r} ${a} ${o}`)}this.#Ia+=i.join(" ");this.#Ra=this.#wn.length;return this.#Ia}getOutlines(t,e,i,s){const n=this.#ka.at(-1);n.line=new Float32Array(n.line);n.points=new Float32Array(n.points);this.#Cr.build(this.#ka,t,e,i,this.#es,this.#sr,s);this.#Qn=null;this.#wn=null;this.#ka=null;this.#Ia=null;return this.#Cr}get defaultSVGProperties(){return{root:{viewBox:"0 0 10000 10000"},rootClass:{draw:!0},bbox:[0,0,1,1]}}}class InkDrawOutline extends Outline{#fr;#Na=0;#Xn;#ka;#Fa;#La;#Ba;#es;#sr;build(t,e,i,s,n,r,a){this.#Fa=e;this.#La=i;this.#Ba=s;this.#es=n;this.#sr=r;this.#Xn=a??0;this.#ka=t;this.#Ua()}get thickness(){return this.#sr}setLastElement(t){this.#ka.push(t);return{path:{d:this.toSVGPath()}}}removeLastElement(){this.#ka.pop();return{path:{d:this.toSVGPath()}}}toSVGPath(){const t=[];for(const{line:e}of this.#ka){t.push(`M${Outline.svgRound(e[4])} ${Outline.svgRound(e[5])}`);if(6!==e.length)if(12===e.length&&isNaN(e[6]))t.push(`L${Outline.svgRound(e[10])} ${Outline.svgRound(e[11])}`);else for(let i=6,s=e.length;i<s;i+=6){const[s,n,r,a,o,l]=e.subarray(i,i+6).map(Outline.svgRound);t.push(`C${s} ${n} ${r} ${a} ${o} ${l}`)}else t.push("Z")}return t.join("")}serialize([t,e,i,s],n){const r=[],a=[],[o,l,h,c]=this.#Ha();let d,u,p,g,f,m,b,v,w;switch(this.#es){case 0:w=Outline._rescale;d=t;u=e+s;p=i;g=-s;f=t+o*i;m=e+(1-l-c)*s;b=t+(o+h)*i;v=e+(1-l)*s;break;case 90:w=Outline._rescaleAndSwap;d=t;u=e;p=i;g=s;f=t+l*i;m=e+o*s;b=t+(l+c)*i;v=e+(o+h)*s;break;case 180:w=Outline._rescale;d=t+i;u=e;p=-i;g=s;f=t+(1-o-h)*i;m=e+l*s;b=t+(1-o)*i;v=e+(l+c)*s;break;case 270:w=Outline._rescaleAndSwap;d=t+i;u=e+s;p=-i;g=-s;f=t+(1-l-c)*i;m=e+(1-o-h)*s;b=t+(1-l)*i;v=e+(1-o)*s}for(const{line:t,points:e}of this.#ka){r.push(w(t,d,u,p,g,n?new Array(t.length):null));a.push(w(e,d,u,p,g,n?new Array(e.length):null))}return{lines:r,points:a,rect:[f,m,b,v]}}static deserialize(t,e,i,s,n,{paths:{lines:r,points:a},rotation:o,thickness:l}){const h=[];let c,d,u,p,g;switch(o){case 0:g=Outline._rescale;c=-t/i;d=e/s+1;u=1/i;p=-1/s;break;case 90:g=Outline._rescaleAndSwap;c=-e/s;d=-t/i;u=1/s;p=1/i;break;case 180:g=Outline._rescale;c=t/i+1;d=-e/s;u=-1/i;p=1/s;break;case 270:g=Outline._rescaleAndSwap;c=e/s+1;d=t/i+1;u=-1/s;p=-1/i}if(!r){r=[];for(const t of a){const e=t.length;if(2===e){r.push(new Float32Array([NaN,NaN,NaN,NaN,t[0],t[1]]));continue}if(4===e){r.push(new Float32Array([NaN,NaN,NaN,NaN,t[0],t[1],NaN,NaN,NaN,NaN,t[2],t[3]]));continue}const i=new Float32Array(3*(e-2));r.push(i);let[s,n,a,o]=t.subarray(0,4);i.set([NaN,NaN,NaN,NaN,s,n],0);for(let r=4;r<e;r+=2){const e=t[r],l=t[r+1];i.set(Outline.createBezierPoints(s,n,a,o,e,l),3*(r-2));[s,n,a,o]=[a,o,e,l]}}}for(let t=0,e=r.length;t<e;t++)h.push({line:g(r[t].map((t=>t??NaN)),c,d,u,p),points:g(a[t].map((t=>t??NaN)),c,d,u,p)});const f=new this.prototype.constructor;f.build(h,i,s,1,o,l,n);return f}#za(t=this.#sr){const e=this.#Xn+t/2*this.#Ba;return this.#es%180==0?[e/this.#Fa,e/this.#La]:[e/this.#La,e/this.#Fa]}#Ha(){const[t,e,i,s]=this.#fr,[n,r]=this.#za(0);return[t+n,e+r,i-2*n,s-2*r]}#Ua(){const t=this.#fr=new Float32Array([1/0,1/0,-1/0,-1/0]);for(const{line:e}of this.#ka){if(e.length<=12){for(let i=4,s=e.length;i<s;i+=6)Util.pointBoundingBox(e[i],e[i+1],t);continue}let i=e[4],s=e[5];for(let n=6,r=e.length;n<r;n+=6){const[r,a,o,l,h,c]=e.subarray(n,n+6);Util.bezierBoundingBox(i,s,r,a,o,l,h,c,t);i=h;s=c}}const[e,i]=this.#za();t[0]=MathClamp(t[0]-e,0,1);t[1]=MathClamp(t[1]-i,0,1);t[2]=MathClamp(t[2]+e,0,1);t[3]=MathClamp(t[3]+i,0,1);t[2]-=t[0];t[3]-=t[1]}get box(){return this.#fr}updateProperty(t,e){return"stroke-width"===t?this.#ra(e):null}#ra(t){const[e,i]=this.#za();this.#sr=t;const[s,n]=this.#za(),[r,a]=[s-e,n-i],o=this.#fr;o[0]-=r;o[1]-=a;o[2]+=2*r;o[3]+=2*a;return o}updateParentDimensions([t,e],i){const[s,n]=this.#za();this.#Fa=t;this.#La=e;this.#Ba=i;const[r,a]=this.#za(),o=r-s,l=a-n,h=this.#fr;h[0]-=o;h[1]-=l;h[2]+=2*o;h[3]+=2*l;return h}updateRotation(t){this.#Na=t;return{path:{transform:this.rotationTransform}}}get viewBox(){return this.#fr.map(Outline.svgRound).join(" ")}get defaultProperties(){const[t,e]=this.#fr;return{root:{viewBox:this.viewBox},path:{"transform-origin":`${Outline.svgRound(t)} ${Outline.svgRound(e)}`}}}get rotationTransform(){const[,,t,e]=this.#fr;let i=0,s=0,n=0,r=0,a=0,o=0;switch(this.#Na){case 90:s=e/t;n=-t/e;a=t;break;case 180:i=-1;r=-1;a=t;o=e;break;case 270:s=-e/t;n=t/e;o=e;break;default:return""}return`matrix(${i} ${s} ${n} ${r} ${Outline.svgRound(a)} ${Outline.svgRound(o)})`}getPathResizingSVGProperties([t,e,i,s]){const[n,r]=this.#za(),[a,o,l,h]=this.#fr;if(Math.abs(l-n)<=Outline.PRECISION||Math.abs(h-r)<=Outline.PRECISION){const n=t+i/2-(a+l/2),r=e+s/2-(o+h/2);return{path:{"transform-origin":`${Outline.svgRound(t)} ${Outline.svgRound(e)}`,transform:`${this.rotationTransform} translate(${n} ${r})`}}}const c=(i-2*n)/(l-2*n),d=(s-2*r)/(h-2*r),u=l/i,p=h/s;return{path:{"transform-origin":`${Outline.svgRound(a)} ${Outline.svgRound(o)}`,transform:`${this.rotationTransform} scale(${u} ${p}) translate(${Outline.svgRound(n)} ${Outline.svgRound(r)}) scale(${c} ${d}) translate(${Outline.svgRound(-n)} ${Outline.svgRound(-r)})`}}}getPathResizedSVGProperties([t,e,i,s]){const[n,r]=this.#za(),a=this.#fr,[o,l,h,c]=a;a[0]=t;a[1]=e;a[2]=i;a[3]=s;if(Math.abs(h-n)<=Outline.PRECISION||Math.abs(c-r)<=Outline.PRECISION){const n=t+i/2-(o+h/2),r=e+s/2-(l+c/2);for(const{line:t,points:e}of this.#ka){Outline._translate(t,n,r,t);Outline._translate(e,n,r,e)}return{root:{viewBox:this.viewBox},path:{"transform-origin":`${Outline.svgRound(t)} ${Outline.svgRound(e)}`,transform:this.rotationTransform||null,d:this.toSVGPath()}}}const d=(i-2*n)/(h-2*n),u=(s-2*r)/(c-2*r),p=-d*(o+n)+t+n,g=-u*(l+r)+e+r;if(1!==d||1!==u||0!==p||0!==g)for(const{line:t,points:e}of this.#ka){Outline._rescale(t,p,g,d,u,t);Outline._rescale(e,p,g,d,u,e)}return{root:{viewBox:this.viewBox},path:{"transform-origin":`${Outline.svgRound(t)} ${Outline.svgRound(e)}`,transform:this.rotationTransform||null,d:this.toSVGPath()}}}getPathTranslatedSVGProperties([t,e],i){const[s,n]=i,r=this.#fr,a=t-r[0],o=e-r[1];if(this.#Fa===s&&this.#La===n)for(const{line:t,points:e}of this.#ka){Outline._translate(t,a,o,t);Outline._translate(e,a,o,e)}else{const t=this.#Fa/s,e=this.#La/n;this.#Fa=s;this.#La=n;for(const{line:i,points:s}of this.#ka){Outline._rescale(i,a,o,t,e,i);Outline._rescale(s,a,o,t,e,s)}r[2]*=t;r[3]*=e}r[0]=t;r[1]=e;return{root:{viewBox:this.viewBox},path:{d:this.toSVGPath(),"transform-origin":`${Outline.svgRound(t)} ${Outline.svgRound(e)}`}}}get defaultSVGProperties(){const t=this.#fr;return{root:{viewBox:this.viewBox},rootClass:{draw:!0},path:{d:this.toSVGPath(),"transform-origin":`${Outline.svgRound(t[0])} ${Outline.svgRound(t[1])}`,transform:this.rotationTransform||null},bbox:t}}}class InkDrawingOptions extends DrawingOptions{constructor(t){super();this._viewParameters=t;super.updateProperties({fill:"none",stroke:AnnotationEditor._defaultLineColor,"stroke-opacity":1,"stroke-width":1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-miterlimit":10})}updateSVGProperty(t,e){if("stroke-width"===t){e??=this["stroke-width"];e*=this._viewParameters.realScale}super.updateSVGProperty(t,e)}clone(){const t=new InkDrawingOptions(this._viewParameters);t.updateAll(this);return t}}class InkEditor extends DrawingEditor{static _type="ink";static _editorType=g.INK;static _defaultDrawingOptions=null;constructor(t){super({...t,name:"inkEditor"});this._willKeepAspectRatio=!0;this.defaultL10nId="pdfjs-editor-ink-editor"}static initialize(t,e){AnnotationEditor.initialize(t,e);this._defaultDrawingOptions=new InkDrawingOptions(e.viewParameters)}static getDefaultDrawingOptions(t){const e=this._defaultDrawingOptions.clone();e.updateProperties(t);return e}static get supportMultipleDrawings(){return!0}static get typesMap(){return shadow(this,"typesMap",new Map([[f.INK_THICKNESS,"stroke-width"],[f.INK_COLOR,"stroke"],[f.INK_OPACITY,"stroke-opacity"]]))}static createDrawerInstance(t,e,i,s,n){return new InkDrawOutliner(t,e,i,s,n,this._defaultDrawingOptions["stroke-width"])}static deserializeDraw(t,e,i,s,n,r){return InkDrawOutline.deserialize(t,e,i,s,n,r)}static async deserialize(t,e,i){let s=null;if(t instanceof InkAnnotationElement){const{data:{inkLists:e,rect:i,rotation:n,id:r,color:a,opacity:o,borderStyle:{rawWidth:l},popupRef:h},parent:{page:{pageNumber:c}}}=t;s=t={annotationType:g.INK,color:Array.from(a),thickness:l,opacity:o,paths:{points:e},boxes:null,pageIndex:c-1,rect:i.slice(0),rotation:n,id:r,deleted:!1,popupRef:h}}const n=await super.deserialize(t,e,i);n.annotationElementId=t.id||null;n._initialData=s;return n}onScaleChanging(){if(!this.parent)return;super.onScaleChanging();const{_drawId:t,_drawingOptions:e,parent:i}=this;e.updateSVGProperty("stroke-width");i.drawLayer.updateProperties(t,e.toSVGProperties())}static onScaleChangingWhenDrawing(){const t=this._currentParent;if(t){super.onScaleChangingWhenDrawing();this._defaultDrawingOptions.updateSVGProperty("stroke-width");t.drawLayer.updateProperties(this._currentDrawId,this._defaultDrawingOptions.toSVGProperties())}}createDrawingOptions({color:t,thickness:e,opacity:i}){this._drawingOptions=InkEditor.getDefaultDrawingOptions({stroke:Util.makeHexColor(...t),"stroke-width":e,"stroke-opacity":i})}serialize(t=!1){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const{lines:e,points:i,rect:s}=this.serializeDraw(t),{_drawingOptions:{stroke:n,"stroke-opacity":r,"stroke-width":a}}=this,o={annotationType:g.INK,color:AnnotationEditor._colorManager.convert(n),opacity:r,thickness:a,paths:{lines:e,points:i},pageIndex:this.pageIndex,rect:s,rotation:this.rotation,structTreeParentId:this._structTreeParentId};if(t){o.isCopy=!0;return o}if(this.annotationElementId&&!this.#$n(o))return null;o.id=this.annotationElementId;return o}#$n(t){const{color:e,thickness:i,opacity:s,pageIndex:n}=this._initialData;return this._hasBeenMoved||this._hasBeenResized||t.color.some(((t,i)=>t!==e[i]))||t.thickness!==i||t.opacity!==s||t.pageIndex!==n}renderAnnotationElement(t){const{points:e,rect:i}=this.serializeDraw(!1);t.updateEdited({rect:i,thickness:this._drawingOptions["stroke-width"],points:e});return null}}class ContourDrawOutline extends InkDrawOutline{toSVGPath(){let t=super.toSVGPath();t.endsWith("Z")||(t+="Z");return t}}class SignatureExtractor{static#ja={maxDim:512,sigmaSFactor:.02,sigmaR:25,kernelSize:16};static#Ga(t,e,i,s){s-=e;return 0===(i-=t)?s>0?0:4:1===i?s+6:2-s}static#Wa=new Int32Array([0,1,-1,1,-1,0,-1,-1,0,-1,1,-1,1,0,1,1]);static#$a(t,e,i,s,n,r,a){const o=this.#Ga(i,s,n,r);for(let n=0;n<8;n++){const r=(-n+o-a+16)%8;if(0!==t[(i+this.#Wa[2*r])*e+(s+this.#Wa[2*r+1])])return r}return-1}static#Va(t,e,i,s,n,r,a){const o=this.#Ga(i,s,n,r);for(let n=0;n<8;n++){const r=(n+o+a+16)%8;if(0!==t[(i+this.#Wa[2*r])*e+(s+this.#Wa[2*r+1])])return r}return-1}static#qa(t,e,i,s){const n=t.length,r=new Int32Array(n);for(let e=0;e<n;e++)r[e]=t[e]<=s?1:0;for(let t=1;t<i-1;t++)r[t*e]=r[t*e+e-1]=0;for(let t=0;t<e;t++)r[t]=r[e*i-1-t]=0;let a,o=1;const l=[];for(let t=1;t<i-1;t++){a=1;for(let i=1;i<e-1;i++){const s=t*e+i,n=r[s];if(0===n)continue;let h=t,c=i;if(1===n&&0===r[s-1]){o+=1;c-=1}else{if(!(n>=1&&0===r[s+1])){1!==n&&(a=Math.abs(n));continue}o+=1;c+=1;n>1&&(a=n)}const d=[i,t],u=c===i+1,p={isHole:u,points:d,id:o,parent:0};l.push(p);let g;for(const t of l)if(t.id===a){g=t;break}g?g.isHole?p.parent=u?g.parent:a:p.parent=u?a:g.parent:p.parent=u?a:0;const f=this.#$a(r,e,t,i,h,c,0);if(-1===f){r[s]=-o;1!==r[s]&&(a=Math.abs(r[s]));continue}let m=this.#Wa[2*f],b=this.#Wa[2*f+1];const v=t+m,w=i+b;h=v;c=w;let y=t,A=i;for(;;){const n=this.#Va(r,e,y,A,h,c,1);m=this.#Wa[2*n];b=this.#Wa[2*n+1];const l=y+m,u=A+b;d.push(u,l);const p=y*e+A;0===r[p+1]?r[p]=-o:1===r[p]&&(r[p]=o);if(l===t&&u===i&&y===v&&A===w){1!==r[s]&&(a=Math.abs(r[s]));break}h=y;c=A;y=l;A=u}}}return l}static#Xa(t,e,i,s){if(i-e<=4){for(let n=e;n<i-2;n+=2)s.push(t[n],t[n+1]);return}const n=t[e],r=t[e+1],a=t[i-4]-n,o=t[i-3]-r,l=Math.hypot(a,o),h=a/l,c=o/l,d=h*r-c*n,u=o/a,p=1/l,g=Math.atan(u),f=Math.cos(g),m=Math.sin(g),b=p*(Math.abs(f)+Math.abs(m)),v=p*(1-b+b**2),w=Math.max(Math.atan(Math.abs(m+f)*v),Math.atan(Math.abs(m-f)*v));let y=0,A=e;for(let s=e+2;s<i-2;s+=2){const e=Math.abs(d-h*t[s+1]+c*t[s]);if(e>y){A=s;y=e}}if(y>(l*w)**2){this.#Xa(t,e,A+2,s);this.#Xa(t,A,i,s)}else s.push(n,r)}static#Ka(t){const e=[],i=t.length;this.#Xa(t,0,i,e);e.push(t[i-2],t[i-1]);return e.length<=4?null:e}static#Ya(t,e,i,s,n,r){const a=new Float32Array(r**2),o=-2*s**2,l=r>>1;for(let t=0;t<r;t++){const e=(t-l)**2;for(let i=0;i<r;i++)a[t*r+i]=Math.exp((e+(i-l)**2)/o)}const h=new Float32Array(256),c=-2*n**2;for(let t=0;t<256;t++)h[t]=Math.exp(t**2/c);const d=t.length,u=new Uint8Array(d),p=new Uint32Array(256);for(let s=0;s<i;s++)for(let n=0;n<e;n++){const o=s*e+n,c=t[o];let d=0,g=0;for(let o=0;o<r;o++){const u=s+o-l;if(!(u<0||u>=i))for(let i=0;i<r;i++){const s=n+i-l;if(s<0||s>=e)continue;const p=t[u*e+s],f=a[o*r+i]*h[Math.abs(p-c)];d+=p*f;g+=f}}p[u[o]=Math.round(d/g)]++}return[u,p]}static#Qa(t){const e=new Uint32Array(256);for(const i of t)e[i]++;return e}static#Ja(t){const e=t.length,i=new Uint8ClampedArray(e>>2);let s=-1/0,n=1/0;for(let e=0,r=i.length;e<r;e++){if(0===t[3+(e<<2)]){s=i[e]=255;continue}const r=i[e]=t[e<<2];r>s&&(s=r);r<n&&(n=r)}const r=255/(s-n);for(let t=0;t<e;t++)i[t]=(i[t]-n)*r;return i}static#Za(t){let e,i=-1/0,s=-1/0;const n=t.findIndex((t=>0!==t));let r=n,a=n;for(e=n;e<256;e++){const n=t[e];if(n>i){if(e-r>s){s=e-r;a=e-1}i=n;r=e}}for(e=a-1;e>=0&&!(t[e]>t[e+1]);e--);return e}static#to(t){const e=t,{width:i,height:s}=t,{maxDim:n}=this.#ja;let r=i,a=s;if(i>n||s>n){let o=i,l=s,h=Math.log2(Math.max(i,s)/n);const c=Math.floor(h);h=h===c?c-1:c;for(let i=0;i<h;i++){r=o;a=l;r>n&&(r=Math.ceil(r/2));a>n&&(a=Math.ceil(a/2));const i=new OffscreenCanvas(r,a);i.getContext("2d").drawImage(t,0,0,o,l,0,0,r,a);o=r;l=a;t!==e&&t.close();t=i.transferToImageBitmap()}const d=Math.min(n/r,n/a);r=Math.round(r*d);a=Math.round(a*d)}const o=new OffscreenCanvas(r,a).getContext("2d",{willReadFrequently:!0});o.filter="grayscale(1)";o.drawImage(t,0,0,t.width,t.height,0,0,r,a);const l=o.getImageData(0,0,r,a).data;return[this.#Ja(l),r,a]}static extractContoursFromText(t,{fontFamily:e,fontStyle:i,fontWeight:s},n,r,a,o){let l=new OffscreenCanvas(1,1),h=l.getContext("2d",{alpha:!1});const c=h.font=`${i} ${s} 200px ${e}`,{actualBoundingBoxLeft:d,actualBoundingBoxRight:u,actualBoundingBoxAscent:p,actualBoundingBoxDescent:g,fontBoundingBoxAscent:f,fontBoundingBoxDescent:m,width:b}=h.measureText(t),v=1.5,w=Math.ceil(Math.max(Math.abs(d)+Math.abs(u)||0,b)*v),y=Math.ceil(Math.max(Math.abs(p)+Math.abs(g)||200,Math.abs(f)+Math.abs(m)||200)*v);l=new OffscreenCanvas(w,y);h=l.getContext("2d",{alpha:!0,willReadFrequently:!0});h.font=c;h.filter="grayscale(1)";h.fillStyle="white";h.fillRect(0,0,w,y);h.fillStyle="black";h.fillText(t,.5*w/2,1.5*y/2);const A=this.#Ja(h.getImageData(0,0,w,y).data),_=this.#Qa(A),x=this.#Za(_),E=this.#qa(A,w,y,x);return this.processDrawnLines({lines:{curves:E,width:w,height:y},pageWidth:n,pageHeight:r,rotation:a,innerMargin:o,mustSmooth:!0,areContours:!0})}static process(t,e,i,s,n){const[r,a,o]=this.#to(t),[l,h]=this.#Ya(r,a,o,Math.hypot(a,o)*this.#ja.sigmaSFactor,this.#ja.sigmaR,this.#ja.kernelSize),c=this.#Za(h),d=this.#qa(l,a,o,c);return this.processDrawnLines({lines:{curves:d,width:a,height:o},pageWidth:e,pageHeight:i,rotation:s,innerMargin:n,mustSmooth:!0,areContours:!0})}static processDrawnLines({lines:t,pageWidth:e,pageHeight:i,rotation:s,innerMargin:n,mustSmooth:r,areContours:a}){s%180!=0&&([e,i]=[i,e]);const{curves:o,width:l,height:h}=t,c=t.thickness??0,d=[],u=Math.min(e/l,i/h),p=u/e,g=u/i,f=[];for(const{points:t}of o){const e=r?this.#Ka(t):t;if(!e)continue;f.push(e);const i=e.length,s=new Float32Array(i),n=new Float32Array(3*(2===i?2:i-2));d.push({line:n,points:s});if(2===i){s[0]=e[0]*p;s[1]=e[1]*g;n.set([NaN,NaN,NaN,NaN,s[0],s[1]],0);continue}let[a,o,l,h]=e;a*=p;o*=g;l*=p;h*=g;s.set([a,o,l,h],0);n.set([NaN,NaN,NaN,NaN,a,o],0);for(let t=4;t<i;t+=2){const i=s[t]=e[t]*p,r=s[t+1]=e[t+1]*g;n.set(Outline.createBezierPoints(a,o,l,h,i,r),3*(t-2));[a,o,l,h]=[l,h,i,r]}}if(0===d.length)return null;const m=a?new ContourDrawOutline:new InkDrawOutline;m.build(d,e,i,1,s,a?0:c,n);return{outline:m,newCurves:f,areContours:a,thickness:c,width:l,height:h}}static async compressSignature({outlines:t,areContours:e,thickness:i,width:s,height:n}){let r,a=1/0,o=-1/0,l=0;for(const e of t){l+=e.length;for(let t=2,i=e.length;t<i;t++){const i=e[t]-e[t-2];a=Math.min(a,i);o=Math.max(o,i)}}r=a>=-128&&o<=127?Int8Array:a>=-32768&&o<=32767?Int16Array:Int32Array;const h=t.length,c=8+3*h,d=new Uint32Array(c);let u=0;d[u++]=c*Uint32Array.BYTES_PER_ELEMENT+(l-2*h)*r.BYTES_PER_ELEMENT;d[u++]=0;d[u++]=s;d[u++]=n;d[u++]=e?0:1;d[u++]=Math.max(0,Math.floor(i??0));d[u++]=h;d[u++]=r.BYTES_PER_ELEMENT;for(const e of t){d[u++]=e.length-2;d[u++]=e[0];d[u++]=e[1]}const p=new CompressionStream("deflate-raw"),g=p.writable.getWriter();await g.ready;g.write(d);const f=r.prototype.constructor;for(const e of t){const t=new f(e.length-2);for(let i=2,s=e.length;i<s;i++)t[i-2]=e[i]-e[i-2];g.write(t)}g.close();const m=await new Response(p.readable).arrayBuffer();return toBase64Util(new Uint8Array(m))}static async decompressSignature(t){try{const e=function fromBase64Util(t){return Uint8Array.fromBase64?Uint8Array.fromBase64(t):stringToBytes(atob(t))}(t),{readable:i,writable:s}=new DecompressionStream("deflate-raw"),n=s.getWriter();await n.ready;n.write(e).then((async()=>{await n.ready;await n.close()})).catch((()=>{}));let r=null,a=0;for await(const t of i){r||=new Uint8Array(new Uint32Array(t.buffer,0,4)[0]);r.set(t,a);a+=t.length}const o=new Uint32Array(r.buffer,0,r.length>>2),l=o[1];if(0!==l)throw new Error(`Invalid version: ${l}`);const h=o[2],c=o[3],d=0===o[4],u=o[5],p=o[6],g=o[7],f=[],m=(8+3*p)*Uint32Array.BYTES_PER_ELEMENT;let b;switch(g){case Int8Array.BYTES_PER_ELEMENT:b=new Int8Array(r.buffer,m);break;case Int16Array.BYTES_PER_ELEMENT:b=new Int16Array(r.buffer,m);break;case Int32Array.BYTES_PER_ELEMENT:b=new Int32Array(r.buffer,m)}a=0;for(let t=0;t<p;t++){const e=o[3*t+8],i=new Float32Array(e+2);f.push(i);for(let e=0;e<2;e++)i[e]=o[3*t+8+e+1];for(let t=0;t<e;t++)i[t+2]=i[t]+b[a++]}return{areContours:d,thickness:u,outlines:f,width:h,height:c}}catch(t){warn(`decompressSignature: ${t}`);return null}}}class SignatureOptions extends DrawingOptions{constructor(){super();super.updateProperties({fill:AnnotationEditor._defaultLineColor,"stroke-width":0})}clone(){const t=new SignatureOptions;t.updateAll(this);return t}}class DrawnSignatureOptions extends InkDrawingOptions{constructor(t){super(t);super.updateProperties({stroke:AnnotationEditor._defaultLineColor,"stroke-width":1})}clone(){const t=new DrawnSignatureOptions(this._viewParameters);t.updateAll(this);return t}}class SignatureEditor extends DrawingEditor{#eo=!1;#io=null;#so=null;#no=null;static _type="signature";static _editorType=g.SIGNATURE;static _defaultDrawingOptions=null;constructor(t){super({...t,mustBeCommitted:!0,name:"signatureEditor"});this._willKeepAspectRatio=!0;this.#so=t.signatureData||null;this.#io=null;this.defaultL10nId="pdfjs-editor-signature-editor1"}static initialize(t,e){AnnotationEditor.initialize(t,e);this._defaultDrawingOptions=new SignatureOptions;this._defaultDrawnSignatureOptions=new DrawnSignatureOptions(e.viewParameters)}static getDefaultDrawingOptions(t){const e=this._defaultDrawingOptions.clone();e.updateProperties(t);return e}static get supportMultipleDrawings(){return!1}static get typesMap(){return shadow(this,"typesMap",new Map)}static get isDrawer(){return!1}get telemetryFinalData(){return{type:"signature",hasDescription:!!this.#io}}static computeTelemetryFinalData(t){const e=t.get("hasDescription");return{hasAltText:e.get(!0)??0,hasNoAltText:e.get(!1)??0}}get isResizable(){return!0}onScaleChanging(){null!==this._drawId&&super.onScaleChanging()}render(){if(this.div)return this.div;let t,e;const{_isCopy:i}=this;if(i){this._isCopy=!1;t=this.x;e=this.y}super.render();if(null===this._drawId)if(this.#so){const{lines:t,mustSmooth:e,areContours:i,description:s,uuid:n,heightInPage:r}=this.#so,{rawDims:{pageWidth:a,pageHeight:o},rotation:l}=this.parent.viewport,h=SignatureExtractor.processDrawnLines({lines:t,pageWidth:a,pageHeight:o,rotation:l,innerMargin:SignatureEditor._INNER_MARGIN,mustSmooth:e,areContours:i});this.addSignature(h,r,s,n)}else{this.div.setAttribute("data-l10n-args",JSON.stringify({description:""}));this.div.hidden=!0;this._uiManager.getSignature(this)}if(i){this._isCopy=!0;this._moveAfterPaste(t,e)}return this.div}setUuid(t){this.#no=t;this.addEditToolbar()}getUuid(){return this.#no}get description(){return this.#io}set description(t){this.#io=t;super.addEditToolbar().then((e=>{e?.updateEditSignatureButton(t)}))}getSignaturePreview(){const{newCurves:t,areContours:e,thickness:i,width:s,height:n}=this.#so,r=Math.max(s,n);return{areContours:e,outline:SignatureExtractor.processDrawnLines({lines:{curves:t.map((t=>({points:t}))),thickness:i,width:s,height:n},pageWidth:r,pageHeight:r,rotation:0,innerMargin:0,mustSmooth:!1,areContours:e}).outline}}async addEditToolbar(){const t=await super.addEditToolbar();if(!t)return null;if(this._uiManager.signatureManager&&null!==this.#io){await t.addEditSignatureButton(this._uiManager.signatureManager,this.#no,this.#io);t.show()}return t}addSignature(t,e,i,s){const{x:n,y:r}=this,{outline:a}=this.#so=t;this.#eo=a instanceof ContourDrawOutline;this.#io=i;this.div.setAttribute("data-l10n-args",JSON.stringify({description:i}));let o;if(this.#eo)o=SignatureEditor.getDefaultDrawingOptions();else{o=SignatureEditor._defaultDrawnSignatureOptions.clone();o.updateProperties({"stroke-width":a.thickness})}this._addOutlines({drawOutlines:a,drawingOptions:o});const[l,h]=this.parentDimensions,[,c]=this.pageDimensions;let d=e/c;d=d>=1?.5:d;this.width*=d/this.height;if(this.width>=1){d*=.9/this.width;this.width=.9}this.height=d;this.setDims(l*this.width,h*this.height);this.x=n;this.y=r;this.center();this._onResized();this.onScaleChanging();this.rotate();this._uiManager.addToAnnotationStorage(this);this.setUuid(s);this._reportTelemetry({action:"pdfjs.signature.inserted",data:{hasBeenSaved:!!s,hasDescription:!!i}});this.div.hidden=!1}getFromImage(t){const{rawDims:{pageWidth:e,pageHeight:i},rotation:s}=this.parent.viewport;return SignatureExtractor.process(t,e,i,s,SignatureEditor._INNER_MARGIN)}getFromText(t,e){const{rawDims:{pageWidth:i,pageHeight:s},rotation:n}=this.parent.viewport;return SignatureExtractor.extractContoursFromText(t,e,i,s,n,SignatureEditor._INNER_MARGIN)}getDrawnSignature(t){const{rawDims:{pageWidth:e,pageHeight:i},rotation:s}=this.parent.viewport;return SignatureExtractor.processDrawnLines({lines:t,pageWidth:e,pageHeight:i,rotation:s,innerMargin:SignatureEditor._INNER_MARGIN,mustSmooth:!1,areContours:!1})}createDrawingOptions({areContours:t,thickness:e}){if(t)this._drawingOptions=SignatureEditor.getDefaultDrawingOptions();else{this._drawingOptions=SignatureEditor._defaultDrawnSignatureOptions.clone();this._drawingOptions.updateProperties({"stroke-width":e})}}serialize(t=!1){if(this.isEmpty())return null;const{lines:e,points:i,rect:s}=this.serializeDraw(t),{_drawingOptions:{"stroke-width":n}}=this,r={annotationType:g.SIGNATURE,isSignature:!0,areContours:this.#eo,color:[0,0,0],thickness:this.#eo?0:n,pageIndex:this.pageIndex,rect:s,rotation:this.rotation,structTreeParentId:this._structTreeParentId};if(t){r.paths={lines:e,points:i};r.uuid=this.#no;r.isCopy=!0}else r.lines=e;this.#io&&(r.accessibilityData={type:"Figure",alt:this.#io});return r}static deserializeDraw(t,e,i,s,n,r){return r.areContours?ContourDrawOutline.deserialize(t,e,i,s,n,r):InkDrawOutline.deserialize(t,e,i,s,n,r)}static async deserialize(t,e,i){const s=await super.deserialize(t,e,i);s.#eo=t.areContours;s.#io=t.accessibilityData?.alt||"";s.#no=t.uuid;return s}}class StampEditor extends AnnotationEditor{#ro=null;#ao=null;#oo=null;#lo=null;#ho=null;#co="";#do=null;#uo=!1;#po=null;#go=!1;#fo=!1;static _type="stamp";static _editorType=g.STAMP;constructor(t){super({...t,name:"stampEditor"});this.#lo=t.bitmapUrl;this.#ho=t.bitmapFile;this.defaultL10nId="pdfjs-editor-stamp-editor"}static initialize(t,e){AnnotationEditor.initialize(t,e)}static isHandlingMimeForPasting(t){return $.includes(t)}static paste(t,e){e.pasteEditor(g.STAMP,{bitmapFile:t.getAsFile()})}altTextFinish(){this._uiManager.useNewAltTextFlow&&(this.div.hidden=!1);super.altTextFinish()}get telemetryFinalData(){return{type:"stamp",hasAltText:!!this.altTextData?.altText}}static computeTelemetryFinalData(t){const e=t.get("hasAltText");return{hasAltText:e.get(!0)??0,hasNoAltText:e.get(!1)??0}}#mo(t,e=!1){if(t){this.#ro=t.bitmap;if(!e){this.#ao=t.id;this.#go=t.isSvg}t.file&&(this.#co=t.file.name);this.#bo()}else this.remove()}#vo(){this.#oo=null;this._uiManager.enableWaiting(!1);if(this.#do)if(this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&this.#ro){this._editToolbar.hide();this._uiManager.editAltText(this,!0)}else{if(!this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&this.#ro){this._reportTelemetry({action:"pdfjs.image.image_added",data:{alt_text_modal:!1,alt_text_type:"empty"}});try{this.mlGuessAltText()}catch{}}this.div.focus()}}async mlGuessAltText(t=null,e=!0){if(this.hasAltTextData())return null;const{mlManager:i}=this._uiManager;if(!i)throw new Error("No ML.");if(!await i.isEnabledFor("altText"))throw new Error("ML isn't enabled for alt text.");const{data:s,width:n,height:r}=t||this.copyCanvas(null,null,!0).imageData,a=await i.guess({name:"altText",request:{data:s,width:n,height:r,channels:s.length/(n*r)}});if(!a)throw new Error("No response from the AI service.");if(a.error)throw new Error("Error from the AI service.");if(a.cancel)return null;if(!a.output)throw new Error("No valid response from the AI service.");const o=a.output;await this.setGuessedAltText(o);e&&!this.hasAltTextData()&&(this.altTextData={alt:o,decorative:!1});return o}#wo(){if(this.#ao){this._uiManager.enableWaiting(!0);this._uiManager.imageManager.getFromId(this.#ao).then((t=>this.#mo(t,!0))).finally((()=>this.#vo()));return}if(this.#lo){const t=this.#lo;this.#lo=null;this._uiManager.enableWaiting(!0);this.#oo=this._uiManager.imageManager.getFromUrl(t).then((t=>this.#mo(t))).finally((()=>this.#vo()));return}if(this.#ho){const t=this.#ho;this.#ho=null;this._uiManager.enableWaiting(!0);this.#oo=this._uiManager.imageManager.getFromFile(t).then((t=>this.#mo(t))).finally((()=>this.#vo()));return}const t=document.createElement("input");t.type="file";t.accept=$.join(",");const e=this._uiManager._signal;this.#oo=new Promise((i=>{t.addEventListener("change",(async()=>{if(t.files&&0!==t.files.length){this._uiManager.enableWaiting(!0);const e=await this._uiManager.imageManager.getFromFile(t.files[0]);this._reportTelemetry({action:"pdfjs.image.image_selected",data:{alt_text_modal:this._uiManager.useNewAltTextFlow}});this.#mo(e)}else this.remove();i()}),{signal:e});t.addEventListener("cancel",(()=>{this.remove();i()}),{signal:e})})).finally((()=>this.#vo()));t.click()}remove(){if(this.#ao){this.#ro=null;this._uiManager.imageManager.deleteId(this.#ao);this.#do?.remove();this.#do=null;if(this.#po){clearTimeout(this.#po);this.#po=null}}super.remove()}rebuild(){if(this.parent){super.rebuild();if(null!==this.div){this.#ao&&null===this.#do&&this.#wo();this.isAttachedToDOM||this.parent.add(this)}}else this.#ao&&this.#wo()}onceAdded(t){this._isDraggable=!0;t&&this.div.focus()}isEmpty(){return!(this.#oo||this.#ro||this.#lo||this.#ho||this.#ao||this.#uo)}get isResizable(){return!0}render(){if(this.div)return this.div;let t,e;if(this._isCopy){t=this.x;e=this.y}super.render();this.div.hidden=!0;this.addAltTextButton();this.#uo||(this.#ro?this.#bo():this.#wo());this._isCopy&&this._moveAfterPaste(t,e);this._uiManager.addShouldRescale(this);return this.div}setCanvas(t,e){const{id:i,bitmap:s}=this._uiManager.imageManager.getFromCanvas(t,e);e.remove();if(i&&this._uiManager.imageManager.isValidId(i)){this.#ao=i;s&&(this.#ro=s);this.#uo=!1;this.#bo()}}_onResized(){this.onScaleChanging()}onScaleChanging(){if(!this.parent)return;null!==this.#po&&clearTimeout(this.#po);this.#po=setTimeout((()=>{this.#po=null;this.#yo()}),200)}#bo(){const{div:t}=this;let{width:e,height:i}=this.#ro;const[s,n]=this.pageDimensions,r=.75;if(this.width){e=this.width*s;i=this.height*n}else if(e>r*s||i>r*n){const t=Math.min(r*s/e,r*n/i);e*=t;i*=t}const[a,o]=this.parentDimensions;this.setDims(e*a/s,i*o/n);this._uiManager.enableWaiting(!1);const l=this.#do=document.createElement("canvas");l.setAttribute("role","img");this.addContainer(l);this.width=e/s;this.height=i/n;this._initialOptions?.isCentered?this.center():this.fixAndSetPosition();this._initialOptions=null;this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&!this.annotationElementId||(t.hidden=!1);this.#yo();if(!this.#fo){this.parent.addUndoableEditor(this);this.#fo=!0}this._reportTelemetry({action:"inserted_image"});this.#co&&this.div.setAttribute("aria-description",this.#co)}copyCanvas(t,e,i=!1){t||(t=224);const{width:s,height:n}=this.#ro,r=new OutputScale;let a=this.#ro,o=s,l=n,h=null;if(e){if(s>e||n>e){const t=Math.min(e/s,e/n);o=Math.floor(s*t);l=Math.floor(n*t)}h=document.createElement("canvas");const t=h.width=Math.ceil(o*r.sx),i=h.height=Math.ceil(l*r.sy);this.#go||(a=this.#Ao(t,i));const c=h.getContext("2d");c.filter=this._uiManager.hcmFilter;let d="white",u="#cfcfd8";if("none"!==this._uiManager.hcmFilter)u="black";else if(window.matchMedia?.("(prefers-color-scheme: dark)").matches){d="#8f8f9d";u="#42414d"}const p=15,g=p*r.sx,f=p*r.sy,m=new OffscreenCanvas(2*g,2*f),b=m.getContext("2d");b.fillStyle=d;b.fillRect(0,0,2*g,2*f);b.fillStyle=u;b.fillRect(0,0,g,f);b.fillRect(g,f,g,f);c.fillStyle=c.createPattern(m,"repeat");c.fillRect(0,0,t,i);c.drawImage(a,0,0,a.width,a.height,0,0,t,i)}let c=null;if(i){let e,i;if(r.symmetric&&a.width<t&&a.height<t){e=a.width;i=a.height}else{a=this.#ro;if(s>t||n>t){const r=Math.min(t/s,t/n);e=Math.floor(s*r);i=Math.floor(n*r);this.#go||(a=this.#Ao(e,i))}}const o=new OffscreenCanvas(e,i).getContext("2d",{willReadFrequently:!0});o.drawImage(a,0,0,a.width,a.height,0,0,e,i);c={width:e,height:i,data:o.getImageData(0,0,e,i).data}}return{canvas:h,width:o,height:l,imageData:c}}#Ao(t,e){const{width:i,height:s}=this.#ro;let n=i,r=s,a=this.#ro;for(;n>2*t||r>2*e;){const i=n,s=r;n>2*t&&(n=n>=16384?Math.floor(n/2)-1:Math.ceil(n/2));r>2*e&&(r=r>=16384?Math.floor(r/2)-1:Math.ceil(r/2));const o=new OffscreenCanvas(n,r);o.getContext("2d").drawImage(a,0,0,i,s,0,0,n,r);a=o.transferToImageBitmap()}return a}#yo(){const[t,e]=this.parentDimensions,{width:i,height:s}=this,n=new OutputScale,r=Math.ceil(i*t*n.sx),a=Math.ceil(s*e*n.sy),o=this.#do;if(!o||o.width===r&&o.height===a)return;o.width=r;o.height=a;const l=this.#go?this.#ro:this.#Ao(r,a),h=o.getContext("2d");h.filter=this._uiManager.hcmFilter;h.drawImage(l,0,0,l.width,l.height,0,0,r,a)}#_o(t){if(t){if(this.#go){const t=this._uiManager.imageManager.getSvgUrl(this.#ao);if(t)return t}const t=document.createElement("canvas");({width:t.width,height:t.height}=this.#ro);t.getContext("2d").drawImage(this.#ro,0,0);return t.toDataURL()}if(this.#go){const[t,e]=this.pageDimensions,i=Math.round(this.width*t*PixelsPerInch.PDF_TO_CSS_UNITS),s=Math.round(this.height*e*PixelsPerInch.PDF_TO_CSS_UNITS),n=new OffscreenCanvas(i,s);n.getContext("2d").drawImage(this.#ro,0,0,this.#ro.width,this.#ro.height,0,0,i,s);return n.transferToImageBitmap()}return structuredClone(this.#ro)}static async deserialize(t,e,i){let s=null,n=!1;if(t instanceof StampAnnotationElement){const{data:{rect:r,rotation:a,id:o,structParent:l,popupRef:h},container:c,parent:{page:{pageNumber:d}},canvas:u}=t;let p,f;if(u){delete t.canvas;({id:p,bitmap:f}=i.imageManager.getFromCanvas(c.id,u));u.remove()}else{n=!0;t._hasNoCanvas=!0}const m=(await e._structTree.getAriaAttributes(`${G}${o}`))?.get("aria-label")||"";s=t={annotationType:g.STAMP,bitmapId:p,bitmap:f,pageIndex:d-1,rect:r.slice(0),rotation:a,id:o,deleted:!1,accessibilityData:{decorative:!1,altText:m},isSvg:!1,structParent:l,popupRef:h}}const r=await super.deserialize(t,e,i),{rect:a,bitmap:o,bitmapUrl:l,bitmapId:h,isSvg:c,accessibilityData:d}=t;if(n){i.addMissingCanvas(t.id,r);r.#uo=!0}else if(h&&i.imageManager.isValidId(h)){r.#ao=h;o&&(r.#ro=o)}else r.#lo=l;r.#go=c;const[u,p]=r.pageDimensions;r.width=(a[2]-a[0])/u;r.height=(a[3]-a[1])/p;r.annotationElementId=t.id||null;d&&(r.altTextData=d);r._initialData=s;r.#fo=!!s;return r}serialize(t=!1,e=null){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const i={annotationType:g.STAMP,bitmapId:this.#ao,pageIndex:this.pageIndex,rect:this.getRect(0,0),rotation:this.rotation,isSvg:this.#go,structTreeParentId:this._structTreeParentId};if(t){i.bitmapUrl=this.#_o(!0);i.accessibilityData=this.serializeAltText(!0);i.isCopy=!0;return i}const{decorative:s,altText:n}=this.serializeAltText(!1);!s&&n&&(i.accessibilityData={type:"Figure",alt:n});if(this.annotationElementId){const t=this.#$n(i);if(t.isSame)return null;t.isSameAltText?delete i.accessibilityData:i.accessibilityData.structParent=this._initialData.structParent??-1}i.id=this.annotationElementId;if(null===e)return i;e.stamps||=new Map;const r=this.#go?(i.rect[2]-i.rect[0])*(i.rect[3]-i.rect[1]):null;if(e.stamps.has(this.#ao)){if(this.#go){const t=e.stamps.get(this.#ao);if(r>t.area){t.area=r;t.serialized.bitmap.close();t.serialized.bitmap=this.#_o(!1)}}}else{e.stamps.set(this.#ao,{area:r,serialized:i});i.bitmap=this.#_o(!1)}return i}#$n(t){const{pageIndex:e,accessibilityData:{altText:i}}=this._initialData,s=t.pageIndex===e,n=(t.accessibilityData?.alt||"")===i;return{isSame:!this._hasBeenMoved&&!this._hasBeenResized&&s&&n,isSameAltText:n}}renderAnnotationElement(t){t.updateEdited({rect:this.getRect(0,0)});return null}}class AnnotationEditorLayer{#Dn;#xo=!1;#Eo=null;#So=null;#Co=null;#To=new Map;#Do=!1;#Mo=!1;#Po=!1;#ko=null;#Io=null;#Ro=null;#Fo=null;#m;static _initialized=!1;static#j=new Map([FreeTextEditor,InkEditor,StampEditor,HighlightEditor,SignatureEditor].map((t=>[t._editorType,t])));constructor({uiManager:t,pageIndex:e,div:i,structTreeLayer:s,accessibilityManager:n,annotationLayer:r,drawLayer:a,textLayer:o,viewport:l,l10n:h}){const c=[...AnnotationEditorLayer.#j.values()];if(!AnnotationEditorLayer._initialized){AnnotationEditorLayer._initialized=!0;for(const e of c)e.initialize(h,t)}t.registerEditorTypes(c);this.#m=t;this.pageIndex=e;this.div=i;this.#Dn=n;this.#Eo=r;this.viewport=l;this.#Ro=o;this.drawLayer=a;this._structTree=s;this.#m.addLayer(this)}get isEmpty(){return 0===this.#To.size}get isInvisible(){return this.isEmpty&&this.#m.getMode()===g.NONE}updateToolbar(t){this.#m.updateToolbar(t)}updateMode(t=this.#m.getMode()){this.#Lo();switch(t){case g.NONE:this.disableTextSelection();this.togglePointerEvents(!1);this.toggleAnnotationLayerPointerEvents(!0);this.disableClick();return;case g.INK:this.disableTextSelection();this.togglePointerEvents(!0);this.enableClick();break;case g.HIGHLIGHT:this.enableTextSelection();this.togglePointerEvents(!1);this.disableClick();break;default:this.disableTextSelection();this.togglePointerEvents(!0);this.enableClick()}this.toggleAnnotationLayerPointerEvents(!1);const{classList:e}=this.div;for(const i of AnnotationEditorLayer.#j.values())e.toggle(`${i._type}Editing`,t===i._editorType);this.div.hidden=!1}hasTextLayer(t){return t===this.#Ro?.div}setEditingState(t){this.#m.setEditingState(t)}addCommands(t){this.#m.addCommands(t)}cleanUndoStack(t){this.#m.cleanUndoStack(t)}toggleDrawing(t=!1){this.div.classList.toggle("drawing",!t)}togglePointerEvents(t=!1){this.div.classList.toggle("disabled",!t)}toggleAnnotationLayerPointerEvents(t=!1){this.#Eo?.div.classList.toggle("disabled",!t)}async enable(){this.#Po=!0;this.div.tabIndex=0;this.togglePointerEvents(!0);const t=new Set;for(const e of this.#To.values()){e.enableEditing();e.show(!0);if(e.annotationElementId){this.#m.removeChangedExistingAnnotation(e);t.add(e.annotationElementId)}}if(!this.#Eo){this.#Po=!1;return}const e=this.#Eo.getEditableAnnotations();for(const i of e){i.hide();if(this.#m.isDeletedAnnotationElement(i.data.id))continue;if(t.has(i.data.id))continue;const e=await this.deserialize(i);if(e){this.addOrRebuild(e);e.enableEditing()}}this.#Po=!1}disable(){this.#Mo=!0;this.div.tabIndex=-1;this.togglePointerEvents(!1);const t=new Map,e=new Map;for(const i of this.#To.values()){i.disableEditing();if(i.annotationElementId)if(null===i.serialize()){e.set(i.annotationElementId,i);this.getEditableAnnotation(i.annotationElementId)?.show();i.remove()}else t.set(i.annotationElementId,i)}if(this.#Eo){const i=this.#Eo.getEditableAnnotations();for(const s of i){const{id:i}=s.data;if(this.#m.isDeletedAnnotationElement(i))continue;let n=e.get(i);if(n){n.resetAnnotationElement(s);n.show(!1);s.show()}else{n=t.get(i);if(n){this.#m.addChangedExistingAnnotation(n);n.renderAnnotationElement(s)&&n.show(!1)}s.show()}}}this.#Lo();this.isEmpty&&(this.div.hidden=!0);const{classList:i}=this.div;for(const t of AnnotationEditorLayer.#j.values())i.remove(`${t._type}Editing`);this.disableTextSelection();this.toggleAnnotationLayerPointerEvents(!0);this.#Mo=!1}getEditableAnnotation(t){return this.#Eo?.getEditableAnnotation(t)||null}setActiveEditor(t){this.#m.getActive()!==t&&this.#m.setActiveEditor(t)}enableTextSelection(){this.div.tabIndex=-1;if(this.#Ro?.div&&!this.#Fo){this.#Fo=new AbortController;const t=this.#m.combinedSignal(this.#Fo);this.#Ro.div.addEventListener("pointerdown",this.#Oo.bind(this),{signal:t});this.#Ro.div.classList.add("highlighting")}}disableTextSelection(){this.div.tabIndex=0;if(this.#Ro?.div&&this.#Fo){this.#Fo.abort();this.#Fo=null;this.#Ro.div.classList.remove("highlighting")}}#Oo(t){this.#m.unselectAll();const{target:e}=t;if(e===this.#Ro.div||("img"===e.getAttribute("role")||e.classList.contains("endOfContent"))&&this.#Ro.div.contains(e)){const{isMac:e}=util_FeatureTest.platform;if(0!==t.button||t.ctrlKey&&e)return;this.#m.showAllEditors("highlight",!0,!0);this.#Ro.div.classList.add("free");this.toggleDrawing();HighlightEditor.startHighlighting(this,"ltr"===this.#m.direction,{target:this.#Ro.div,x:t.x,y:t.y});this.#Ro.div.addEventListener("pointerup",(()=>{this.#Ro.div.classList.remove("free");this.toggleDrawing(!0)}),{once:!0,signal:this.#m._signal});t.preventDefault()}}enableClick(){if(this.#So)return;this.#So=new AbortController;const t=this.#m.combinedSignal(this.#So);this.div.addEventListener("pointerdown",this.pointerdown.bind(this),{signal:t});const e=this.pointerup.bind(this);this.div.addEventListener("pointerup",e,{signal:t});this.div.addEventListener("pointercancel",e,{signal:t})}disableClick(){this.#So?.abort();this.#So=null}attach(t){this.#To.set(t.id,t);const{annotationElementId:e}=t;e&&this.#m.isDeletedAnnotationElement(e)&&this.#m.removeDeletedAnnotationElement(t)}detach(t){this.#To.delete(t.id);this.#Dn?.removePointerInTextLayer(t.contentDiv);!this.#Mo&&t.annotationElementId&&this.#m.addDeletedAnnotationElement(t)}remove(t){this.detach(t);this.#m.removeEditor(t);t.div.remove();t.isAttachedToDOM=!1}changeParent(t){if(t.parent!==this){if(t.parent&&t.annotationElementId){this.#m.addDeletedAnnotationElement(t.annotationElementId);AnnotationEditor.deleteAnnotationElement(t);t.annotationElementId=null}this.attach(t);t.parent?.detach(t);t.setParent(this);if(t.div&&t.isAttachedToDOM){t.div.remove();this.div.append(t.div)}}}add(t){if(t.parent!==this||!t.isAttachedToDOM){this.changeParent(t);this.#m.addEditor(t);this.attach(t);if(!t.isAttachedToDOM){const e=t.render();this.div.append(e);t.isAttachedToDOM=!0}t.fixAndSetPosition();t.onceAdded(!this.#Po);this.#m.addToAnnotationStorage(t);t._reportTelemetry(t.telemetryInitialData)}}moveEditorInDOM(t){if(!t.isAttachedToDOM)return;const{activeElement:e}=document;if(t.div.contains(e)&&!this.#Co){t._focusEventsAllowed=!1;this.#Co=setTimeout((()=>{this.#Co=null;if(t.div.contains(document.activeElement))t._focusEventsAllowed=!0;else{t.div.addEventListener("focusin",(()=>{t._focusEventsAllowed=!0}),{once:!0,signal:this.#m._signal});e.focus()}}),0)}t._structTreeParentId=this.#Dn?.moveElementInDOM(this.div,t.div,t.contentDiv,!0)}addOrRebuild(t){if(t.needsToBeRebuilt()){t.parent||=this;t.rebuild();t.show()}else this.add(t)}addUndoableEditor(t){this.addCommands({cmd:()=>t._uiManager.rebuild(t),undo:()=>{t.remove()},mustExec:!1})}getNextId(){return this.#m.getId()}get#No(){return AnnotationEditorLayer.#j.get(this.#m.getMode())}combinedSignal(t){return this.#m.combinedSignal(t)}#Bo(t){const e=this.#No;return e?new e.prototype.constructor(t):null}canCreateNewEmptyEditor(){return this.#No?.canCreateNewEmptyEditor()}async pasteEditor(t,e){this.#m.updateToolbar(t);await this.#m.updateMode(t);const{offsetX:i,offsetY:s}=this.#Uo(),n=this.getNextId(),r=this.#Bo({parent:this,id:n,x:i,y:s,uiManager:this.#m,isCentered:!0,...e});r&&this.add(r)}async deserialize(t){return await(AnnotationEditorLayer.#j.get(t.annotationType??t.annotationEditorType)?.deserialize(t,this,this.#m))||null}createAndAddNewEditor(t,e,i={}){const s=this.getNextId(),n=this.#Bo({parent:this,id:s,x:t.offsetX,y:t.offsetY,uiManager:this.#m,isCentered:e,...i});n&&this.add(n);return n}#Uo(){const{x:t,y:e,width:i,height:s}=this.div.getBoundingClientRect(),n=Math.max(0,t),r=Math.max(0,e),a=(n+Math.min(window.innerWidth,t+i))/2-t,o=(r+Math.min(window.innerHeight,e+s))/2-e,[l,h]=this.viewport.rotation%180==0?[a,o]:[o,a];return{offsetX:l,offsetY:h}}addNewEditor(t={}){this.createAndAddNewEditor(this.#Uo(),!0,t)}setSelected(t){this.#m.setSelected(t)}toggleSelected(t){this.#m.toggleSelected(t)}unselect(t){this.#m.unselect(t)}pointerup(t){const{isMac:e}=util_FeatureTest.platform;if(0!==t.button||t.ctrlKey&&e)return;if(t.target!==this.div)return;if(!this.#Do)return;this.#Do=!1;if(this.#No?.isDrawer&&this.#No.supportMultipleDrawings)return;if(!this.#xo){this.#xo=!0;return}const i=this.#m.getMode();i!==g.STAMP&&i!==g.SIGNATURE?this.createAndAddNewEditor(t,!1):this.#m.unselectAll()}pointerdown(t){this.#m.getMode()===g.HIGHLIGHT&&this.enableTextSelection();if(this.#Do){this.#Do=!1;return}const{isMac:e}=util_FeatureTest.platform;if(0!==t.button||t.ctrlKey&&e)return;if(t.target!==this.div)return;this.#Do=!0;if(this.#No?.isDrawer){this.startDrawingSession(t);return}const i=this.#m.getActive();this.#xo=!i||i.isEmpty()}startDrawingSession(t){this.div.focus({preventScroll:!0});if(this.#ko){this.#No.startDrawing(this,this.#m,!1,t);return}this.#m.setCurrentDrawingSession(this);this.#ko=new AbortController;const e=this.#m.combinedSignal(this.#ko);this.div.addEventListener("blur",(({relatedTarget:t})=>{if(t&&!this.div.contains(t)){this.#Io=null;this.commitOrRemove()}}),{signal:e});this.#No.startDrawing(this,this.#m,!1,t)}pause(t){if(t){const{activeElement:t}=document;this.div.contains(t)&&(this.#Io=t)}else this.#Io&&setTimeout((()=>{this.#Io?.focus();this.#Io=null}),0)}endDrawingSession(t=!1){if(!this.#ko)return null;this.#m.setCurrentDrawingSession(null);this.#ko.abort();this.#ko=null;this.#Io=null;return this.#No.endDrawing(t)}findNewParent(t,e,i){const s=this.#m.findParent(e,i);if(null===s||s===this)return!1;s.changeParent(t);return!0}commitOrRemove(){if(this.#ko){this.endDrawingSession();return!0}return!1}onScaleChanging(){this.#ko&&this.#No.onScaleChangingWhenDrawing(this)}destroy(){this.commitOrRemove();if(this.#m.getActive()?.parent===this){this.#m.commitOrRemove();this.#m.setActiveEditor(null)}if(this.#Co){clearTimeout(this.#Co);this.#Co=null}for(const t of this.#To.values()){this.#Dn?.removePointerInTextLayer(t.contentDiv);t.setParent(null);t.isAttachedToDOM=!1;t.div.remove()}this.div=null;this.#To.clear();this.#m.removeLayer(this)}#Lo(){for(const t of this.#To.values())t.isEmpty()&&t.remove()}render({viewport:t}){this.viewport=t;setLayerDimensions(this.div,t);for(const t of this.#m.getEditors(this.pageIndex)){this.add(t);t.rebuild()}this.updateMode()}update({viewport:t}){this.#m.commitOrRemove();this.#Lo();const e=this.viewport.rotation,i=t.rotation;this.viewport=t;setLayerDimensions(this.div,{rotation:i});if(e!==i)for(const t of this.#To.values())t.rotate(i)}get pageDimensions(){const{pageWidth:t,pageHeight:e}=this.viewport.rawDims;return[t,e]}get scale(){return this.#m.viewParameters.realScale}}class DrawLayer{#an=null;#Ho=new Map;#zo=new Map;static#y=0;constructor({pageIndex:t}){this.pageIndex=t}setParent(t){if(this.#an){if(this.#an!==t){if(this.#Ho.size>0)for(const e of this.#Ho.values()){e.remove();t.append(e)}this.#an=t}}else this.#an=t}static get _svgFactory(){return shadow(this,"_svgFactory",new DOMSVGFactory)}static#jo(t,[e,i,s,n]){const{style:r}=t;r.top=100*i+"%";r.left=100*e+"%";r.width=100*s+"%";r.height=100*n+"%"}#Go(){const t=DrawLayer._svgFactory.create(1,1,!0);this.#an.append(t);t.setAttribute("aria-hidden",!0);return t}#Wo(t,e){const i=DrawLayer._svgFactory.createElement("clipPath");t.append(i);const s=`clip_${e}`;i.setAttribute("id",s);i.setAttribute("clipPathUnits","objectBoundingBox");const n=DrawLayer._svgFactory.createElement("use");i.append(n);n.setAttribute("href",`#${e}`);n.classList.add("clip");return s}#$o(t,e){for(const[i,s]of Object.entries(e))null===s?t.removeAttribute(i):t.setAttribute(i,s)}draw(t,e=!1,i=!1){const s=DrawLayer.#y++,n=this.#Go(),r=DrawLayer._svgFactory.createElement("defs");n.append(r);const a=DrawLayer._svgFactory.createElement("path");r.append(a);const o=`path_p${this.pageIndex}_${s}`;a.setAttribute("id",o);a.setAttribute("vector-effect","non-scaling-stroke");e&&this.#zo.set(s,a);const l=i?this.#Wo(r,o):null,h=DrawLayer._svgFactory.createElement("use");n.append(h);h.setAttribute("href",`#${o}`);this.updateProperties(n,t);this.#Ho.set(s,n);return{id:s,clipPathId:`url(#${l})`}}drawOutline(t,e){const i=DrawLayer.#y++,s=this.#Go(),n=DrawLayer._svgFactory.createElement("defs");s.append(n);const r=DrawLayer._svgFactory.createElement("path");n.append(r);const a=`path_p${this.pageIndex}_${i}`;r.setAttribute("id",a);r.setAttribute("vector-effect","non-scaling-stroke");let o;if(e){const t=DrawLayer._svgFactory.createElement("mask");n.append(t);o=`mask_p${this.pageIndex}_${i}`;t.setAttribute("id",o);t.setAttribute("maskUnits","objectBoundingBox");const e=DrawLayer._svgFactory.createElement("rect");t.append(e);e.setAttribute("width","1");e.setAttribute("height","1");e.setAttribute("fill","white");const s=DrawLayer._svgFactory.createElement("use");t.append(s);s.setAttribute("href",`#${a}`);s.setAttribute("stroke","none");s.setAttribute("fill","black");s.setAttribute("fill-rule","nonzero");s.classList.add("mask")}const l=DrawLayer._svgFactory.createElement("use");s.append(l);l.setAttribute("href",`#${a}`);o&&l.setAttribute("mask",`url(#${o})`);const h=l.cloneNode();s.append(h);l.classList.add("mainOutline");h.classList.add("secondaryOutline");this.updateProperties(s,t);this.#Ho.set(i,s);return i}finalizeDraw(t,e){this.#zo.delete(t);this.updateProperties(t,e)}updateProperties(t,e){if(!e)return;const{root:i,bbox:s,rootClass:n,path:r}=e,a="number"==typeof t?this.#Ho.get(t):t;if(a){i&&this.#$o(a,i);s&&DrawLayer.#jo(a,s);if(n){const{classList:t}=a;for(const[e,i]of Object.entries(n))t.toggle(e,i)}if(r){const t=a.firstChild.firstChild;this.#$o(t,r)}}}updateParent(t,e){if(e===this)return;const i=this.#Ho.get(t);if(i){e.#an.append(i);this.#Ho.delete(t);e.#Ho.set(t,i)}}remove(t){this.#zo.delete(t);if(null!==this.#an){this.#Ho.get(t).remove();this.#Ho.delete(t)}}destroy(){this.#an=null;for(const t of this.#Ho.values())t.remove();this.#Ho.clear();this.#zo.clear()}}globalThis.pdfjsTestingUtils={HighlightOutliner};globalThis.pdfjsLib={AbortException,AnnotationEditorLayer,AnnotationEditorParamsType:f,AnnotationEditorType:g,AnnotationEditorUIManager,AnnotationLayer,AnnotationMode:p,AnnotationType:E,build:St,ColorPicker,createValidAbsoluteUrl,DOMSVGFactory,DrawLayer,FeatureTest:util_FeatureTest,fetchData,getDocument,getFilenameFromUrl,getPdfFilenameFromUrl,getUuid,getXfaPageViewport,GlobalWorkerOptions,ImageKind:x,InvalidPDFException,isDataScheme,isPdfFile,isValidExplicitDest:_t,MathClamp,noContextMenu,normalizeUnicode,OPS:k,OutputScale,PasswordResponses:N,PDFDataRangeTransport,PDFDateString,PDFWorker,PermissionFlag:m,PixelsPerInch,RenderingCancelledException,ResponseException,setLayerDimensions,shadow,SignatureExtractor,stopEvent,SupportedImageMimeTypes:$,TextLayer,TouchManager,updateUrlHash,Util,VerbosityLevel:P,version:Et,XfaLayer};export{AbortException,AnnotationEditorLayer,f as AnnotationEditorParamsType,g as AnnotationEditorType,AnnotationEditorUIManager,AnnotationLayer,p as AnnotationMode,E as AnnotationType,ColorPicker,DOMSVGFactory,DrawLayer,util_FeatureTest as FeatureTest,GlobalWorkerOptions,x as ImageKind,InvalidPDFException,MathClamp,k as OPS,OutputScale,PDFDataRangeTransport,PDFDateString,PDFWorker,N as PasswordResponses,m as PermissionFlag,PixelsPerInch,RenderingCancelledException,ResponseException,SignatureExtractor,$ as SupportedImageMimeTypes,TextLayer,TouchManager,Util,P as VerbosityLevel,XfaLayer,St as build,createValidAbsoluteUrl,fetchData,getDocument,getFilenameFromUrl,getPdfFilenameFromUrl,getUuid,getXfaPageViewport,isDataScheme,isPdfFile,_t as isValidExplicitDest,noContextMenu,normalizeUnicode,setLayerDimensions,shadow,stopEvent,updateUrlHash,Et as version};