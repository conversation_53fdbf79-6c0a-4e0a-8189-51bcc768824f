* {
  box-sizing: border-box;
}

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color: #213547;
  background-color: #ffffff;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  padding: 0;
  min-width: 320px;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: 600;
}

button {
  border: none;
  cursor: pointer;
  font-family: inherit;
  transition: all 0.2s ease;
}

button:focus,
button:focus-visible {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

textarea {
  font-family: inherit;
}

textarea:focus {
  outline: none;
}
