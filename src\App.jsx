import { useState, useRef, useEffect } from 'react'
import OpenAI from 'openai'
import './App.css'

const openai = new OpenAI({
  apiKey: import.meta.env.VITE_OPENAI_API_KEY,
  dangerouslyAllowBrowser: true
})

function App() {
  const [messages, setMessages] = useState([])
  const [inputMessage, setInputMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [selectedImages, setSelectedImages] = useState([])
  const messagesEndRef = useRef(null)
  const fileInputRef = useRef(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // Convert file to base64
  const fileToBase64 = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result)
      reader.onerror = error => reject(error)
    })
  }

  // Handle image selection
  const handleImageSelect = (event) => {
    const files = Array.from(event.target.files)
    const imageFiles = files.filter(file => file.type.startsWith('image/'))

    if (imageFiles.length > 0) {
      setSelectedImages(prev => [...prev, ...imageFiles])
    }
  }

  // Remove selected image
  const removeImage = (index) => {
    setSelectedImages(prev => prev.filter((_, i) => i !== index))
  }

  // Open file picker
  const openFilePicker = () => {
    fileInputRef.current?.click()
  }

  const sendMessage = async () => {
    if ((!inputMessage.trim() && selectedImages.length === 0) || isLoading) return

    // Prepare message content
    let messageContent = []

    if (inputMessage.trim()) {
      messageContent.push({
        type: "text",
        text: inputMessage
      })
    }

    // Add images to message content
    if (selectedImages.length > 0) {
      for (const image of selectedImages) {
        const base64Image = await fileToBase64(image)
        messageContent.push({
          type: "image_url",
          image_url: {
            url: base64Image
          }
        })
      }
    }

    const userMessage = {
      role: 'user',
      content: messageContent.length === 1 && messageContent[0].type === "text"
        ? messageContent[0].text
        : messageContent,
      images: selectedImages.length > 0 ? selectedImages : undefined
    }

    const newMessages = [...messages, userMessage]
    setMessages(newMessages)
    setInputMessage('')
    setSelectedImages([])
    setIsLoading(true)

    try {
      // Prepare messages for API (convert our format to OpenAI format)
      const apiMessages = newMessages.map(msg => ({
        role: msg.role,
        content: typeof msg.content === 'string' ? msg.content : msg.content
      }))

      const completion = await openai.chat.completions.create({
        model: selectedImages.length > 0 || newMessages.some(m => m.images) ? "gpt-4o" : "gpt-3.5-turbo",
        messages: apiMessages,
        max_tokens: 1000,
        temperature: 0.7,
      })

      const assistantMessage = {
        role: 'assistant',
        content: completion.choices[0].message.content
      }

      setMessages([...newMessages, assistantMessage])
    } catch (error) {
      console.error('Error calling OpenAI API:', error)
      const errorMessage = {
        role: 'assistant',
        content: 'Sorry, I encountered an error. Please try again.'
      }
      setMessages([...newMessages, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const clearChat = () => {
    setMessages([])
    setSelectedImages([])
  }

  return (
    <div className="chat-container">
      <div className="chat-header">
        <h1>ChatGPT Interface</h1>
        <button onClick={clearChat} className="clear-button">
          Clear Chat
        </button>
      </div>

      <div className="messages-container">
        {messages.length === 0 && (
          <div className="welcome-message">
            <h2>Welcome to ChatGPT with Vision!</h2>
            <p>Start a conversation by typing a message or uploading images below.</p>
            <p>📷 Click the camera button to upload images for analysis!</p>
          </div>
        )}

        {messages.map((message, index) => (
          <div key={index} className={`message ${message.role}`}>
            <div className="message-content">
              <strong>{message.role === 'user' ? 'You' : 'ChatGPT'}:</strong>
              {message.images && message.images.length > 0 && (
                <div className="message-images">
                  {message.images.map((image, imgIndex) => (
                    <img
                      key={imgIndex}
                      src={URL.createObjectURL(image)}
                      alt={`Uploaded image ${imgIndex + 1}`}
                      className="message-image"
                    />
                  ))}
                </div>
              )}
              <div className="message-text">
                {typeof message.content === 'string' ? message.content :
                  Array.isArray(message.content) ?
                    message.content.find(c => c.type === 'text')?.text || '' :
                    message.content
                }
              </div>
            </div>
          </div>
        ))}

        {isLoading && (
          <div className="message assistant">
            <div className="message-content">
              <strong>ChatGPT:</strong>
              <div className="message-text">
                <div className="typing-indicator">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      <div className="input-container">
        {/* Image preview area */}
        {selectedImages.length > 0 && (
          <div className="selected-images">
            {selectedImages.map((image, index) => (
              <div key={index} className="selected-image">
                <img
                  src={URL.createObjectURL(image)}
                  alt={`Selected ${index + 1}`}
                  className="selected-image-preview"
                />
                <button
                  onClick={() => removeImage(index)}
                  className="remove-image-button"
                  type="button"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        )}

        <div className="input-row">
          <textarea
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type your message here... (Press Enter to send)"
            className="message-input"
            rows="3"
            disabled={isLoading}
          />
          <div className="input-buttons">
            <button
              onClick={openFilePicker}
              className="image-button"
              type="button"
              disabled={isLoading}
              title="Upload image"
            >
              📷
            </button>
            <button
              onClick={sendMessage}
              disabled={(!inputMessage.trim() && selectedImages.length === 0) || isLoading}
              className="send-button"
            >
              {isLoading ? 'Sending...' : 'Send'}
            </button>
          </div>
        </div>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          multiple
          onChange={handleImageSelect}
          style={{ display: 'none' }}
        />
      </div>
    </div>
  )
}

export default App
