import { useState, useRef, useEffect } from 'react'
import OpenAI from 'openai'
import * as pdfjsLib from 'pdfjs-dist'
import './App.css'

// Set up PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`

const openai = new OpenAI({
  apiKey: import.meta.env.VITE_OPENAI_API_KEY,
  dangerouslyAllowBrowser: true
})

function App() {
  const [messages, setMessages] = useState([])
  const [inputMessage, setInputMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [selectedFiles, setSelectedFiles] = useState([])
  const messagesEndRef = useRef(null)
  const fileInputRef = useRef(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // Convert file to base64
  const fileToBase64 = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result)
      reader.onerror = error => reject(error)
    })
  }

  // Extract text from PDF using PDF.js
  const extractPDFText = async (file) => {
    try {
      const arrayBuffer = await file.arrayBuffer()
      const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise
      let fullText = ''

      // Extract text from each page
      for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
        const page = await pdf.getPage(pageNum)
        const textContent = await page.getTextContent()
        const pageText = textContent.items.map(item => item.str).join(' ')
        fullText += pageText + '\n'
      }

      return fullText.trim()
    } catch (error) {
      console.error('Error extracting PDF text:', error)
      throw new Error(`Failed to extract text from PDF: ${error.message}`)
    }
  }

  // Handle file selection (images and PDFs)
  const handleFileSelect = (event) => {
    const files = Array.from(event.target.files)
    const supportedFiles = files.filter(file =>
      file.type.startsWith('image/') || file.type === 'application/pdf'
    )

    if (supportedFiles.length > 0) {
      setSelectedFiles(prev => [...prev, ...supportedFiles])
    }
  }

  // Remove selected file
  const removeFile = (index) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index))
  }

  // Open file picker
  const openFilePicker = () => {
    fileInputRef.current?.click()
  }

  const sendMessage = async () => {
    if ((!inputMessage.trim() && selectedFiles.length === 0) || isLoading) return

    // Prepare message content
    let messageContent = []
    let hasImages = false
    let pdfTexts = []

    if (inputMessage.trim()) {
      messageContent.push({
        type: "text",
        text: inputMessage
      })
    }

    // Process files
    if (selectedFiles.length > 0) {
      for (const file of selectedFiles) {
        if (file.type.startsWith('image/')) {
          hasImages = true
          const base64Image = await fileToBase64(file)
          messageContent.push({
            type: "image_url",
            image_url: {
              url: base64Image
            }
          })
        } else if (file.type === 'application/pdf') {
          try {
            const pdfText = await extractPDFText(file)
            pdfTexts.push(`PDF Content from "${file.name}":\n${pdfText}`)
          } catch (error) {
            console.error('Error extracting PDF text:', error)
            pdfTexts.push(`Error reading PDF "${file.name}": ${error.message}`)
          }
        }
      }
    }

    // Add PDF text content to message
    if (pdfTexts.length > 0) {
      const pdfContent = pdfTexts.join('\n\n')
      if (messageContent.length > 0) {
        // If there's already text content, append PDF content
        const textContent = messageContent.find(c => c.type === 'text')
        if (textContent) {
          textContent.text += '\n\n' + pdfContent
        } else {
          messageContent.push({
            type: "text",
            text: pdfContent
          })
        }
      } else {
        messageContent.push({
          type: "text",
          text: pdfContent
        })
      }
    }

    const userMessage = {
      role: 'user',
      content: messageContent.length === 1 && messageContent[0].type === "text"
        ? messageContent[0].text
        : messageContent,
      files: selectedFiles.length > 0 ? selectedFiles : undefined
    }

    const newMessages = [...messages, userMessage]
    setMessages(newMessages)
    setInputMessage('')
    setSelectedFiles([])
    setIsLoading(true)

    try {
      // Prepare messages for API (convert our format to OpenAI format)
      const apiMessages = newMessages.map(msg => ({
        role: msg.role,
        content: typeof msg.content === 'string' ? msg.content : msg.content
      }))

      const completion = await openai.chat.completions.create({
        model: hasImages || newMessages.some(m => m.files?.some(f => f.type.startsWith('image/'))) ? "gpt-4o" : "gpt-3.5-turbo",
        messages: apiMessages,
        max_tokens: 2000,
        temperature: 0.7,
      })

      const assistantMessage = {
        role: 'assistant',
        content: completion.choices[0].message.content
      }

      setMessages([...newMessages, assistantMessage])
    } catch (error) {
      console.error('Error calling OpenAI API:', error)
      const errorMessage = {
        role: 'assistant',
        content: 'Sorry, I encountered an error. Please try again.'
      }
      setMessages([...newMessages, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const clearChat = () => {
    setMessages([])
    setSelectedFiles([])
  }

  return (
    <div className="chat-container">
      <div className="chat-header">
        <h1>ChatGPT Interface</h1>
        <button onClick={clearChat} className="clear-button">
          Clear Chat
        </button>
      </div>

      <div className="messages-container">
        {messages.length === 0 && (
          <div className="welcome-message">
            <h2>Welcome to ChatGPT with Vision & Documents!</h2>
            <p>Start a conversation by typing a message or uploading files below.</p>
            <p>📷 Upload images for visual analysis</p>
            <p>📄 Upload PDF documents for text analysis</p>
          </div>
        )}

        {messages.map((message, index) => (
          <div key={index} className={`message ${message.role}`}>
            <div className="message-content">
              <strong>{message.role === 'user' ? 'You' : 'ChatGPT'}:</strong>
              {message.files && message.files.length > 0 && (
                <div className="message-files">
                  {message.files.map((file, fileIndex) => (
                    <div key={fileIndex} className="message-file">
                      {file.type.startsWith('image/') ? (
                        <img
                          src={URL.createObjectURL(file)}
                          alt={`Uploaded content ${fileIndex + 1}`}
                          className="message-image"
                        />
                      ) : file.type === 'application/pdf' ? (
                        <div className="pdf-indicator">
                          📄 {file.name}
                        </div>
                      ) : null}
                    </div>
                  ))}
                </div>
              )}
              <div className="message-text">
                {typeof message.content === 'string' ? message.content :
                  Array.isArray(message.content) ?
                    message.content.find(c => c.type === 'text')?.text || '' :
                    message.content
                }
              </div>
            </div>
          </div>
        ))}

        {isLoading && (
          <div className="message assistant">
            <div className="message-content">
              <strong>ChatGPT:</strong>
              <div className="message-text">
                <div className="typing-indicator">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      <div className="input-container">
        {/* File preview area */}
        {selectedFiles.length > 0 && (
          <div className="selected-files">
            {selectedFiles.map((file, index) => (
              <div key={index} className="selected-file">
                {file.type.startsWith('image/') ? (
                  <img
                    src={URL.createObjectURL(file)}
                    alt={`Selected ${index + 1}`}
                    className="selected-file-preview"
                  />
                ) : file.type === 'application/pdf' ? (
                  <div className="selected-pdf-preview">
                    <div className="pdf-icon">📄</div>
                    <div className="pdf-name">{file.name}</div>
                  </div>
                ) : null}
                <button
                  onClick={() => removeFile(index)}
                  className="remove-file-button"
                  type="button"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        )}

        <div className="input-row">
          <textarea
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type your message here... (Press Enter to send)"
            className="message-input"
            rows="3"
            disabled={isLoading}
          />
          <div className="input-buttons">
            <button
              onClick={openFilePicker}
              className="file-button"
              type="button"
              disabled={isLoading}
              title="Upload files (images & PDFs)"
            >
              📎
            </button>
            <button
              onClick={sendMessage}
              disabled={(!inputMessage.trim() && selectedFiles.length === 0) || isLoading}
              className="send-button"
            >
              {isLoading ? 'Sending...' : 'Send'}
            </button>
          </div>
        </div>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*,application/pdf"
          multiple
          onChange={handleFileSelect}
          style={{ display: 'none' }}
        />
      </div>
    </div>
  )
}

export default App
